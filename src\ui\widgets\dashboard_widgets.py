#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QFrame, QPushButton, QGraphicsDropShadowEffect,
    QSizePolicy, QGridLayout, QSpacerItem
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QColor, QPalette, QIcon, QFont, QPainter, QPen, QPixmap
import qtawesome as qta
from datetime import datetime, timedelta
import os

from .base_widgets import StyledLabel, HeaderLabel
from src.utils import translation_manager as tr
from src.models import User
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.ui.styles.theme_manager import ThemeManager

class DashboardCard(QFrame):
    """بطاقة معلومات لوحة التحكم"""

    # إشارة النقر
    clicked = pyqtSignal()

    def __init__(self, title="", icon=None, value="", module_name=None, color=None, parent=None):
        super().__init__(parent)
        self.title = title
        self.icon = icon
        self.value = value

        # استخدام لون الوحدة إذا تم تحديده، وإلا استخدام اللون المخصص
        if module_name:
            self.color = QColor(get_module_color(module_name))
        elif color:
            self.color = QColor(color)
        else:
            self.color = QColor(get_ui_color('card', 'dark'))

        # حالة التحويم
        self.is_hovered = False

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        # إعداد الإطار
        self.setFrameShape(QFrame.StyledPanel)
        self.setFixedSize(300, 180)  # حجم أصغر قليلاً للمظهر الأفضل

        # تعيين لون الخلفية
        palette = self.palette()
        palette.setColor(QPalette.Window, self.color)
        self.setAutoFillBackground(True)
        self.setPalette(palette)

        # تعيين نمط البطاقة
        self.setStyleSheet(f"""
            QFrame {{
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }}
            QLabel {{
                color: white;
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # العنوان والأيقونة
        header_layout = QHBoxLayout()

        title_label = HeaderLabel(self.title)
        title_label.setStyleSheet(f"""
            color: white;
            font-size: {get_font_size('subheader')};
            font-weight: bold;
        """)
        header_layout.addWidget(title_label)

        if self.icon:
            icon_label = QLabel()
            icon_label.setPixmap(self.icon.pixmap(QSize(32, 32)))
            header_layout.addWidget(icon_label, alignment=Qt.AlignRight)

        layout.addLayout(header_layout)

        # القيمة
        if self.value:
            value_label = HeaderLabel(self.value)
            value_label.setStyleSheet(f"""
                color: white;
                font-size: {get_font_size('header')};
                font-weight: bold;
            """)
            value_label.setAlignment(Qt.AlignRight)
            layout.addWidget(value_label)

            # إضافة خط أفقي تزييني
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setStyleSheet("background-color: rgba(255, 255, 255, 0.2); margin: 5px 0;")
            line.setFixedHeight(1)
            layout.addWidget(line)

        # إضافة أيقونة صغيرة في الأسفل
        bottom_layout = QHBoxLayout()
        info_label = QLabel("اضغط للتفاصيل")
        info_label.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-size: 12px;")
        bottom_layout.addWidget(info_label)

        arrow_icon = QLabel()
        arrow_icon.setPixmap(qta.icon("fa5s.arrow-right", color="white").pixmap(16, 16))
        bottom_layout.addWidget(arrow_icon, alignment=Qt.AlignRight)

        layout.addLayout(bottom_layout)

        # جعل البطاقة قابلة للنقر
        self.setCursor(Qt.PointingHandCursor)

        # تمكين تتبع الماوس للتأثيرات
        self.setMouseTracking(True)

    def enterEvent(self, event):
        """حدث دخول الماوس"""
        self.is_hovered = True
        # تغيير لون الخلفية عند التحويم
        darker_color = self.color.darker(110)
        palette = self.palette()
        palette.setColor(QPalette.Window, darker_color)
        self.setPalette(palette)

        # تكبير البطاقة قليلاً
        self.setFixedSize(305, 185)

        super().enterEvent(event)

    def leaveEvent(self, event):
        """حدث خروج الماوس"""
        self.is_hovered = False
        # إعادة اللون الأصلي
        palette = self.palette()
        palette.setColor(QPalette.Window, self.color)
        self.setPalette(palette)

        # إعادة الحجم الأصلي
        self.setFixedSize(300, 180)

        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """حدث الضغط بالماوس"""
        if event.button() == Qt.LeftButton:
            # تغيير لون الخلفية عند الضغط
            darker_color = self.color.darker(130)
            palette = self.palette()
            palette.setColor(QPalette.Window, darker_color)
            self.setPalette(palette)
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """حدث تحرير الماوس"""
        if event.button() == Qt.LeftButton:
            # إعادة لون التحويم
            darker_color = self.color.darker(110) if self.is_hovered else self.color
            palette = self.palette()
            palette.setColor(QPalette.Window, darker_color)
            self.setPalette(palette)

            # إرسال إشارة النقر
            self.clicked.emit()

        super().mouseReleaseEvent(event)

    def paintEvent(self, event):
        """حدث الرسم"""
        super().paintEvent(event)

        # إضافة تأثير توهج خفيف في الأعلى
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        gradient = QLinearGradient(0, 0, 0, 40)
        gradient.setColorAt(0, QColor(255, 255, 255, 50))
        gradient.setColorAt(1, QColor(255, 255, 255, 0))

        painter.setPen(Qt.NoPen)
        painter.setBrush(gradient)
        painter.drawRoundedRect(0, 0, self.width(), 40, 15, 15)

class UserProfileWidget(QWidget):
    """ودجت معلومات المستخدم"""

    def __init__(self, user: User, parent=None):
        super().__init__(parent)
        self.user = user
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة معلومات المستخدم"""
        # إنشاء إطار للمستخدم
        frame = QFrame(self)
        frame.setFrameShape(QFrame.StyledPanel)
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 10px;
                padding: 5px;
            }}
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(frame)

        # تخطيط الإطار
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)

        # أيقونة المستخدم
        icon_label = QLabel()
        icon_label.setPixmap(qta.icon('fa5s.user-circle', color=get_ui_color('highlight', 'dark')).pixmap(48, 48))
        layout.addWidget(icon_label)

        # معلومات المستخدم
        info_layout = QVBoxLayout()

        name_label = StyledLabel(self.user.full_name)
        name_label.setStyleSheet(f"""
            color: {get_ui_color('text', 'dark')};
            font-weight: bold;
            font-size: {get_font_size('subheader')};
        """)
        info_layout.addWidget(name_label)

        role_text = "مدير النظام" if self.user.is_admin else "مستخدم"
        role_label = StyledLabel(role_text)
        role_label.setStyleSheet(f"""
            color: {get_ui_color('text_secondary', 'dark')};
            font-size: {get_font_size('normal')};
        """)
        info_layout.addWidget(role_label)

        layout.addLayout(info_layout)

class StatisticsWidget(QWidget):
    """ودجت الإحصائيات السريعة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة الإحصائيات"""
        # العنوان الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 10)
        main_layout.setSpacing(15)

        # عنوان القسم
        header = HeaderLabel(tr.get_text("quick_stats", "إحصائيات سريعة"))
        header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            color: {get_ui_color('text', 'dark')};
            margin-bottom: 5px;
        """)
        main_layout.addWidget(header)

        # تخطيط البطاقات
        cards_layout = QHBoxLayout()
        cards_layout.setContentsMargins(0, 0, 0, 0)
        cards_layout.setSpacing(25)

        # تعريف الإحصائيات مع ألوان الوحدات المناسبة
        stats = [
            # استخدام الألوان المطلوبة حسب المواصفات
            ("sales_today", "shopping-cart", "1,234", "+5.2%", "sales_report"),  # أصفر
            ("profit_today", "chart-line", "567", "+3.1%", "treasury"),  # بنفسجي
            ("expenses_today", "money-bill", "890", "-2.4%", "expenses_report"),  # أخضر فاتح
            ("inventory_status", "boxes", "543", "-1.8%", "inventory")  # أحمر
        ]

        for stat_id, icon_name, value, change, module_name in stats:
            # الحصول على لون الوحدة
            module_color = get_module_color(module_name)

            # إنشاء بطاقة الإحصائية
            stat_card = QFrame()
            stat_card.setFrameShape(QFrame.StyledPanel)

            # إضافة تأثير ظل للبطاقة
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 80))
            shadow.setOffset(0, 3)
            stat_card.setGraphicsEffect(shadow)

            # تطبيق النمط مع تدرج لوني
            stat_card.setStyleSheet(f"""
                QFrame {{
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 1,
                        stop: 0 {module_color},
                        stop: 1 {QColor(module_color).darker(120).name()}
                    );
                    border-radius: 12px;
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }}
                QLabel {{
                    color: white;
                }}
            """)

            card_layout = QVBoxLayout(stat_card)
            card_layout.setContentsMargins(15, 15, 15, 15)
            card_layout.setSpacing(8)

            # العنوان والأيقونة
            header_layout = QHBoxLayout()
            title = StyledLabel(tr.get_text(f"stat_{stat_id}", stat_id.replace('_', ' ').title()))
            title.setStyleSheet(f"""
                color: white;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            """)
            header_layout.addWidget(title)

            icon_label = QLabel()
            icon_label.setPixmap(qta.icon(f"fa5s.{icon_name}", color="white").pixmap(24, 24))
            header_layout.addWidget(icon_label, alignment=Qt.AlignRight)

            card_layout.addLayout(header_layout)

            # إضافة خط فاصل
            line = QFrame()
            line.setFrameShape(QFrame.HLine)
            line.setStyleSheet("background-color: rgba(255, 255, 255, 0.2); margin: 2px 0;")
            line.setFixedHeight(1)
            card_layout.addWidget(line)

            # القيمة والتغيير
            value_layout = QHBoxLayout()

            value_label = HeaderLabel(value)
            value_label.setStyleSheet(f"""
                color: white;
                font-size: {get_font_size('header')};
                font-weight: bold;
            """)
            value_layout.addWidget(value_label)

            # تحديد لون التغيير (أخضر للزيادة، أحمر للنقصان)
            change_color = "#4cd137" if change.startswith("+") else "#e84118"

            # إضافة أيقونة للتغيير
            change_icon = "fa5s.arrow-up" if change.startswith("+") else "fa5s.arrow-down"
            change_icon_label = QLabel()
            change_icon_label.setPixmap(qta.icon(change_icon, color=change_color).pixmap(16, 16))

            change_layout = QHBoxLayout()
            change_layout.setSpacing(2)
            change_layout.addWidget(change_icon_label)

            change_label = StyledLabel(change)
            change_label.setStyleSheet(f"""
                color: {change_color};
                font-weight: bold;
                font-size: {get_font_size('small')};
            """)
            change_layout.addWidget(change_label)

            # إضافة مساحة مرنة لدفع التغيير إلى اليمين
            value_layout.addStretch()
            value_layout.addLayout(change_layout)

            card_layout.addLayout(value_layout)

            # إضافة نص وصفي صغير
            description = ""
            if stat_id == "sales_today":
                description = "مقارنة باليوم السابق"
            elif stat_id == "profit_today":
                description = "نسبة الربح من المبيعات"
            elif stat_id == "expenses_today":
                description = "مقارنة بالمتوسط الشهري"
            elif stat_id == "inventory_status":
                description = "عدد المنتجات في المخزون"

            desc_label = QLabel(description)
            desc_label.setStyleSheet("""
                color: rgba(255, 255, 255, 0.7);
                font-size: 11px;
            """)
            card_layout.addWidget(desc_label)

            cards_layout.addWidget(stat_card)

        main_layout.addLayout(cards_layout)

class NotificationsWidget(QWidget):
    """ودجت الإشعارات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة الإشعارات"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # عنوان القسم مع أيقونة
        header_layout = QHBoxLayout()

        header = HeaderLabel(tr.get_text("notifications", "الإشعارات"))
        header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(header)

        # أيقونة الإشعارات
        notif_icon = QLabel()
        notif_icon.setPixmap(qta.icon("fa5s.bell", color=get_module_color('chat')).pixmap(20, 20))
        header_layout.addWidget(notif_icon, alignment=Qt.AlignRight)

        layout.addLayout(header_layout)

        # استخدام لون الدردشة للإطار (برتقالي)
        notifications_color = get_module_color('chat')

        # إطار للإشعارات
        notifications_frame = QFrame()
        notifications_frame.setFrameShape(QFrame.StyledPanel)

        # إضافة تأثير ظل للإطار
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 3)
        notifications_frame.setGraphicsEffect(shadow)

        notifications_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border: 1px solid {notifications_color};
                border-radius: 12px;
                padding: 5px;
            }}
        """)

        notifications_layout = QVBoxLayout(notifications_frame)
        notifications_layout.setContentsMargins(10, 10, 10, 10)
        notifications_layout.setSpacing(8)

        # عنوان الإشعارات داخل الإطار
        notif_header = QLabel(f"لديك {3} إشعارات جديدة")
        notif_header.setStyleSheet(f"""
            color: {get_ui_color('text', 'dark')};
            font-weight: bold;
            font-size: {get_font_size('normal')};
            padding-bottom: 5px;
        """)
        notifications_layout.addWidget(notif_header)

        # خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet(f"""
            background-color: {notifications_color}50;
            margin: 2px 0 8px 0;
        """)
        separator.setFixedHeight(1)
        notifications_layout.addWidget(separator)

        # قائمة الإشعارات مع الأيقونات والألوان المناسبة
        notifications = [
            ("تنبيه: انخفاض المخزون", "inventory", "fa5s.exclamation-triangle", "منذ 10 دقائق"),
            ("فاتورة جديدة تم إنشاؤها", "invoices", "fa5s.file-invoice-dollar", "منذ 25 دقيقة"),
            ("تذكير: دفعة مستحقة اليوم", "treasury", "fa5s.money-check-alt", "منذ ساعة")
        ]

        for notification_text, module_name, icon_name, time_text in notifications:
            # الحصول على لون الوحدة
            module_color = get_module_color(module_name)

            # إنشاء إطار للإشعار
            notif_frame = QFrame()
            notif_frame.setCursor(Qt.PointingHandCursor)
            notif_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {get_ui_color('background', 'dark')};
                    border-left: 4px solid {module_color};
                    border-radius: 6px;
                    padding: 8px;
                }}
                QFrame:hover {{
                    background-color: {module_color}20;
                }}
            """)

            # تخطيط الإشعار
            notif_layout = QHBoxLayout(notif_frame)
            notif_layout.setContentsMargins(10, 8, 10, 8)
            notif_layout.setSpacing(10)

            # أيقونة الإشعار
            icon_label = QLabel()
            icon_label.setPixmap(qta.icon(icon_name, color=module_color).pixmap(24, 24))
            notif_layout.addWidget(icon_label)

            # محتوى الإشعار
            content_layout = QVBoxLayout()
            content_layout.setSpacing(2)

            # نص الإشعار
            text_label = QLabel(notification_text)
            text_label.setStyleSheet(f"""
                color: {get_ui_color('text', 'dark')};
                font-size: {get_font_size('normal')};
                font-weight: bold;
            """)
            content_layout.addWidget(text_label)

            # وقت الإشعار
            time_label = QLabel(time_text)
            time_label.setStyleSheet(f"""
                color: {get_ui_color('text_secondary', 'dark')};
                font-size: {get_font_size('small')};
            """)
            content_layout.addWidget(time_label)

            notif_layout.addLayout(content_layout)

            # زر الإجراء
            action_btn = QPushButton("عرض")
            action_btn.setCursor(Qt.PointingHandCursor)
            action_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {module_color}40;
                    color: {module_color};
                    border: none;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 60px;
                }}
                QPushButton:hover {{
                    background-color: {module_color}70;
                }}
            """)
            notif_layout.addWidget(action_btn, alignment=Qt.AlignRight)

            notifications_layout.addWidget(notif_frame)

        # زر عرض كل الإشعارات
        view_all_btn = QPushButton("عرض كل الإشعارات")
        view_all_btn.setCursor(Qt.PointingHandCursor)
        view_all_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {notifications_color}20;
                color: {notifications_color};
                border: 1px solid {notifications_color}50;
                border-radius: 5px;
                padding: 8px;
                margin-top: 5px;
                font-size: {get_font_size('normal')};
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {notifications_color}40;
            }}
        """)
        notifications_layout.addWidget(view_all_btn)

        layout.addWidget(notifications_frame)

class QuickAccessMenu(QWidget):
    """قائمة الوصول السريع"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة قائمة الوصول السريع"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(12)

        # العنوان مع أيقونة
        header_layout = QHBoxLayout()

        header = HeaderLabel(tr.get_text("quick_access", "الوصول السريع"))
        header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(header)

        # أيقونة الوصول السريع
        quick_icon = QLabel()
        quick_icon.setPixmap(qta.icon("fa5s.bolt", color=get_ui_color('highlight', 'dark')).pixmap(20, 20))
        header_layout.addWidget(quick_icon, alignment=Qt.AlignRight)

        layout.addLayout(header_layout)

        # إطار للأزرار السريعة
        quick_frame = QFrame()
        quick_frame.setFrameShape(QFrame.StyledPanel)

        # إضافة تأثير ظل للإطار
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 3)
        quick_frame.setGraphicsEffect(shadow)

        quick_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('card', 'dark')};
                border-radius: 12px;
                padding: 5px;
            }}
        """)

        quick_layout = QVBoxLayout(quick_frame)
        quick_layout.setContentsMargins(10, 10, 10, 10)
        quick_layout.setSpacing(10)

        # الأزرار السريعة مع ألوان الوحدات المناسبة
        quick_actions = [
            ("new_sale", "cart-plus", "invoices", "إنشاء فاتورة جديدة"),
            ("new_purchase", "truck-loading", "invoices", "إضافة فاتورة شراء"),
            ("add_product", "box", "inventory", "إضافة منتج للمخزون"),
            ("add_customer", "user-plus", "definitions", "إضافة عميل جديد"),
            ("add_expense", "money-bill", "expenses_report", "تسجيل مصروف")
        ]

        for action_id, icon_name, module_name, tooltip in quick_actions:
            # الحصول على لون الوحدة
            module_color = get_module_color(module_name)

            # إنشاء زر الوصول السريع
            btn = QPushButton(tr.get_text(f"action_{action_id}", action_id.replace('_', ' ').title()))
            btn.setIcon(qta.icon(f"fa5s.{icon_name}", color="white"))
            btn.setCursor(Qt.PointingHandCursor)
            btn.setToolTip(tooltip)

            # تطبيق النمط مع تدرج لوني
            btn.setStyleSheet(f"""
                QPushButton {{
                    padding: 12px;
                    text-align: left;
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 {module_color},
                        stop: 1 {QColor(module_color).darker(115).name()}
                    );
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: {get_font_size('normal')};
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: qlineargradient(
                        x1: 0, y1: 0, x2: 1, y2: 0,
                        stop: 0 {QColor(module_color).lighter(110).name()},
                        stop: 1 {module_color}
                    );
                }}
                QPushButton:pressed {{
                    background-color: {QColor(module_color).darker(120).name()};
                }}
            """)

            # إضافة تأثير ظل للزر
            btn_shadow = QGraphicsDropShadowEffect()
            btn_shadow.setBlurRadius(10)
            btn_shadow.setColor(QColor(0, 0, 0, 50))
            btn_shadow.setOffset(0, 2)
            btn.setGraphicsEffect(btn_shadow)

            quick_layout.addWidget(btn)

        # زر إضافي للمزيد من الإجراءات
        more_btn = QPushButton(tr.get_text("more_actions", "المزيد من الإجراءات"))
        more_btn.setIcon(qta.icon("fa5s.ellipsis-h", color=get_ui_color('text', 'dark')))
        more_btn.setCursor(Qt.PointingHandCursor)
        more_btn.setStyleSheet(f"""
            QPushButton {{
                padding: 12px;
                text-align: center;
                background-color: {get_ui_color('background', 'dark')};
                color: {get_ui_color('text', 'dark')};
                border: 1px dashed {get_ui_color('border', 'dark')};
                border-radius: 8px;
                font-size: {get_font_size('normal')};
            }}
            QPushButton:hover {{
                background-color: {get_ui_color('hover', 'dark')};
                border: 1px solid {get_ui_color('highlight', 'dark')};
            }}
        """)
        quick_layout.addWidget(more_btn)

        layout.addWidget(quick_frame)
        layout.addStretch()