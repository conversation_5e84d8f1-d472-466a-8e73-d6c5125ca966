#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة حوار إضافة وتعديل المنتج
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QDoubleSpinBox, QSpinBox, QCheckBox, QMessageBox, QFileDialog,
    QGroupBox, QDateEdit, QFrame, QScrollArea, QWidget
)
from PyQt5.QtCore import Qt, QDate, QDateTime
from PyQt5.QtGui import QPixmap, QIcon
import qtawesome as qta
from datetime import datetime
import os

from src.database import get_db
from src.models import Product, ProductCategory, ProductStatus, Supplier
from src.ui.widgets.base_widgets import (
    PrimaryButton, SecondaryButton, StyledLineEdit, StyledTextEdit,
    StyledComboBox, StyledDoubleSpinBox, StyledSpinBox, StyledCheckBox,
    StyledDateEdit, StyledLabel, HeaderLabel
)
from src.utils import translation_manager as tr, log_info, log_error

class ProductDialog(QDialog):
    """نافذة حوار إضافة وتعديل المنتج"""

    def __init__(self, parent=None, product=None):
        super().__init__(parent)
        self.product = product
        self.is_edit_mode = product is not None
        self.setup_ui()
        self.load_data()

        if self.product:
            self.load_product_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = tr.get_text("edit_product", "تعديل المنتج") if self.is_edit_mode else tr.get_text("add_product", "إضافة منتج")
        self.setWindowTitle(title)
        self.setModal(True)
        self.setMinimumSize(600, 500)

        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(title)
        layout.addWidget(header)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_tab()
        self.tabs.addTab(basic_tab, tr.get_text("basic_info", "معلومات أساسية"))

        # تبويب التسعير
        pricing_tab = self.create_pricing_tab()
        self.tabs.addTab(pricing_tab, tr.get_text("pricing", "التسعير"))

        # تبويب المخزون
        inventory_tab = self.create_inventory_tab()
        self.tabs.addTab(inventory_tab, tr.get_text("inventory", "المخزون"))

        # تبويب معلومات إضافية
        additional_tab = self.create_additional_tab()
        self.tabs.addTab(additional_tab, tr.get_text("additional_info", "معلومات إضافية"))

        layout.addWidget(self.tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        self.save_btn.setIcon(qta.icon('fa5s.save'))
        self.save_btn.clicked.connect(self.save_product)
        buttons_layout.addWidget(self.save_btn)

        self.cancel_btn = SecondaryButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.setIcon(qta.icon('fa5s.times'))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def create_basic_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # الاسم
        self.name_input = StyledLineEdit()
        self.name_input.setMaxLength(100)
        layout.addRow(StyledLabel(tr.get_text("name", "الاسم") + " *"), self.name_input)

        # الكود
        self.code_input = StyledLineEdit()
        self.code_input.setMaxLength(50)
        layout.addRow(StyledLabel(tr.get_text("code", "الكود") + " *"), self.code_input)

        # الباركود
        self.barcode_input = StyledLineEdit()
        self.barcode_input.setMaxLength(100)
        layout.addRow(StyledLabel(tr.get_text("barcode", "الباركود")), self.barcode_input)

        # الفئة
        self.category_input = StyledComboBox()
        layout.addRow(StyledLabel(tr.get_text("category", "الفئة")), self.category_input)

        # المورد
        self.supplier_input = StyledComboBox()
        layout.addRow(StyledLabel(tr.get_text("supplier", "المورد")), self.supplier_input)

        # الوحدة
        self.unit_input = StyledComboBox()
        self.unit_input.setEditable(True)
        self.unit_input.addItems([
            "قطعة", "كيلو", "جرام", "لتر", "متر", "صندوق", "كرتونة", "علبة", "زجاجة", "كيس"
        ])
        layout.addRow(StyledLabel(tr.get_text("unit", "الوحدة")), self.unit_input)

        # الوصف
        self.description_input = StyledTextEdit()
        self.description_input.setMaximumHeight(100)
        layout.addRow(StyledLabel(tr.get_text("description", "الوصف")), self.description_input)

        # الحالة
        status_layout = QHBoxLayout()
        self.is_active_input = StyledCheckBox(tr.get_text("active", "نشط"))
        self.is_active_input.setChecked(True)
        status_layout.addWidget(self.is_active_input)

        self.is_service_input = StyledCheckBox(tr.get_text("is_service", "خدمة"))
        status_layout.addWidget(self.is_service_input)

        self.is_featured_input = StyledCheckBox(tr.get_text("featured", "مميز"))
        status_layout.addWidget(self.is_featured_input)

        layout.addRow(StyledLabel(tr.get_text("status", "الحالة")), status_layout)

        return widget

    def create_pricing_tab(self):
        """إنشاء تبويب التسعير"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # سعر الشراء
        self.purchase_price_input = StyledDoubleSpinBox()
        self.purchase_price_input.setMaximum(999999.99)
        self.purchase_price_input.setDecimals(2)
        layout.addRow(StyledLabel(tr.get_text("purchase_price", "سعر الشراء") + " *"), self.purchase_price_input)

        # سعر البيع
        self.selling_price_input = StyledDoubleSpinBox()
        self.selling_price_input.setMaximum(999999.99)
        self.selling_price_input.setDecimals(2)
        layout.addRow(StyledLabel(tr.get_text("selling_price", "سعر البيع") + " *"), self.selling_price_input)

        # أقل سعر بيع
        self.min_selling_price_input = StyledDoubleSpinBox()
        self.min_selling_price_input.setMaximum(999999.99)
        self.min_selling_price_input.setDecimals(2)
        layout.addRow(StyledLabel(tr.get_text("min_selling_price", "أقل سعر بيع")), self.min_selling_price_input)

        # سعر الجملة
        self.wholesale_price_input = StyledDoubleSpinBox()
        self.wholesale_price_input.setMaximum(999999.99)
        self.wholesale_price_input.setDecimals(2)
        layout.addRow(StyledLabel(tr.get_text("wholesale_price", "سعر الجملة")), self.wholesale_price_input)

        # نسبة الخصم
        self.discount_percentage_input = StyledDoubleSpinBox()
        self.discount_percentage_input.setMaximum(100)
        self.discount_percentage_input.setDecimals(2)
        self.discount_percentage_input.setSuffix("%")
        layout.addRow(StyledLabel(tr.get_text("discount_percentage", "نسبة الخصم")), self.discount_percentage_input)

        # نسبة الضريبة
        self.tax_rate_input = StyledDoubleSpinBox()
        self.tax_rate_input.setMaximum(100)
        self.tax_rate_input.setDecimals(2)
        self.tax_rate_input.setSuffix("%")
        layout.addRow(StyledLabel(tr.get_text("tax_rate", "نسبة الضريبة")), self.tax_rate_input)

        # العملة
        self.currency_input = StyledComboBox()
        self.currency_input.addItems(["EGP", "USD", "EUR", "SAR", "AED"])
        layout.addRow(StyledLabel(tr.get_text("currency", "العملة")), self.currency_input)

        # حساب هامش الربح تلقائياً
        self.purchase_price_input.valueChanged.connect(self.calculate_profit_margin)
        self.selling_price_input.valueChanged.connect(self.calculate_profit_margin)

        # عرض هامش الربح
        self.profit_margin_label = StyledLabel("0%")
        layout.addRow(StyledLabel(tr.get_text("profit_margin", "هامش الربح")), self.profit_margin_label)

        return widget

    def create_inventory_tab(self):
        """إنشاء تبويب المخزون"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # الكمية الحالية
        self.quantity_input = StyledSpinBox()
        self.quantity_input.setMaximum(999999)
        layout.addRow(StyledLabel(tr.get_text("current_quantity", "الكمية الحالية")), self.quantity_input)

        # أقل كمية
        self.min_quantity_input = StyledSpinBox()
        self.min_quantity_input.setMaximum(999999)
        layout.addRow(StyledLabel(tr.get_text("min_quantity", "أقل كمية")), self.min_quantity_input)

        # أكبر كمية
        self.max_quantity_input = StyledSpinBox()
        self.max_quantity_input.setMaximum(999999)
        layout.addRow(StyledLabel(tr.get_text("max_quantity", "أكبر كمية")), self.max_quantity_input)

        # نقطة إعادة الطلب
        self.reorder_point_input = StyledSpinBox()
        self.reorder_point_input.setMaximum(999999)
        layout.addRow(StyledLabel(tr.get_text("reorder_point", "نقطة إعادة الطلب")), self.reorder_point_input)

        # كمية إعادة الطلب
        self.reorder_quantity_input = StyledSpinBox()
        self.reorder_quantity_input.setMaximum(999999)
        layout.addRow(StyledLabel(tr.get_text("reorder_quantity", "كمية إعادة الطلب")), self.reorder_quantity_input)

        # الموقع
        self.location_input = StyledLineEdit()
        self.location_input.setMaxLength(100)
        layout.addRow(StyledLabel(tr.get_text("location", "الموقع")), self.location_input)

        # تاريخ الصنع
        self.manufacture_date_input = StyledDateEdit()
        self.manufacture_date_input.setCalendarPopup(True)
        layout.addRow(StyledLabel(tr.get_text("manufacture_date", "تاريخ الصنع")), self.manufacture_date_input)

        # تاريخ انتهاء الصلاحية
        self.expiry_date_input = StyledDateEdit()
        self.expiry_date_input.setCalendarPopup(True)
        layout.addRow(StyledLabel(tr.get_text("expiry_date", "تاريخ انتهاء الصلاحية")), self.expiry_date_input)

        return widget

    def create_additional_tab(self):
        """إنشاء تبويب المعلومات الإضافية"""
        widget = QWidget()
        layout = QFormLayout(widget)

        # العلامة التجارية
        self.brand_input = StyledLineEdit()
        self.brand_input.setMaxLength(100)
        layout.addRow(StyledLabel(tr.get_text("brand", "العلامة التجارية")), self.brand_input)

        # الموديل
        self.model_input = StyledLineEdit()
        self.model_input.setMaxLength(100)
        layout.addRow(StyledLabel(tr.get_text("model", "الموديل")), self.model_input)

        # اللون
        self.color_input = StyledLineEdit()
        self.color_input.setMaxLength(50)
        layout.addRow(StyledLabel(tr.get_text("color", "اللون")), self.color_input)

        # الحجم
        self.size_input = StyledLineEdit()
        self.size_input.setMaxLength(50)
        layout.addRow(StyledLabel(tr.get_text("size", "الحجم")), self.size_input)

        # الوزن
        weight_layout = QHBoxLayout()
        self.weight_input = StyledDoubleSpinBox()
        self.weight_input.setMaximum(999999.99)
        self.weight_input.setDecimals(2)
        weight_layout.addWidget(self.weight_input)

        self.weight_unit_input = StyledComboBox()
        self.weight_unit_input.addItems(["كجم", "جرام", "طن", "رطل"])
        weight_layout.addWidget(self.weight_unit_input)

        layout.addRow(StyledLabel(tr.get_text("weight", "الوزن")), weight_layout)

        # ملاحظات
        self.notes_input = StyledTextEdit()
        self.notes_input.setMaximumHeight(100)
        layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات")), self.notes_input)

        return widget

    def load_data(self):
        """تحميل البيانات المطلوبة"""
        try:
            db = next(get_db())

            # تحميل الفئات
            self.category_input.addItem(tr.get_text("select_category", "اختر الفئة"), None)
            categories = db.query(ProductCategory).filter(ProductCategory.is_deleted == False).all()
            for category in categories:
                self.category_input.addItem(category.name, category.id)

            # تحميل الموردين
            self.supplier_input.addItem(tr.get_text("select_supplier", "اختر المورد"), None)
            suppliers = db.query(Supplier).filter(Supplier.is_active == True).all()
            for supplier in suppliers:
                self.supplier_input.addItem(supplier.name, supplier.id)

        except Exception as e:
            log_error(f"خطأ في تحميل البيانات: {str(e)}")

    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل"""
        if not self.product:
            return

        # المعلومات الأساسية
        self.name_input.setText(self.product.name or "")
        self.code_input.setText(self.product.code or "")
        self.barcode_input.setText(self.product.barcode or "")
        self.description_input.setText(self.product.description or "")

        # الفئة
        if self.product.category_id:
            index = self.category_input.findData(self.product.category_id)
            if index >= 0:
                self.category_input.setCurrentIndex(index)

        # المورد
        if self.product.supplier_id:
            index = self.supplier_input.findData(self.product.supplier_id)
            if index >= 0:
                self.supplier_input.setCurrentIndex(index)

        # الوحدة
        self.unit_input.setCurrentText(self.product.unit or "قطعة")

        # الحالة
        self.is_active_input.setChecked(self.product.is_active)
        self.is_service_input.setChecked(self.product.is_service)
        self.is_featured_input.setChecked(self.product.is_featured)

        # التسعير
        self.purchase_price_input.setValue(self.product.purchase_price)
        self.selling_price_input.setValue(self.product.selling_price)
        self.min_selling_price_input.setValue(self.product.min_selling_price or 0)
        self.wholesale_price_input.setValue(self.product.wholesale_price or 0)
        self.discount_percentage_input.setValue(self.product.discount_percentage or 0)
        self.tax_rate_input.setValue(self.product.tax_rate or 0)
        self.currency_input.setCurrentText(self.product.currency)

        # المخزون
        self.quantity_input.setValue(self.product.quantity)
        self.min_quantity_input.setValue(self.product.min_quantity or 0)
        self.max_quantity_input.setValue(self.product.max_quantity or 0)
        self.reorder_point_input.setValue(self.product.reorder_point or 0)
        self.reorder_quantity_input.setValue(self.product.reorder_quantity or 0)
        self.location_input.setText(self.product.location or "")

        # التواريخ
        if self.product.manufacture_date:
            self.manufacture_date_input.setDate(QDate.fromString(self.product.manufacture_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))
        if self.product.expiry_date:
            self.expiry_date_input.setDate(QDate.fromString(self.product.expiry_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

        # المعلومات الإضافية
        self.brand_input.setText(self.product.brand or "")
        self.model_input.setText(self.product.model or "")
        self.color_input.setText(self.product.color or "")
        self.size_input.setText(self.product.size or "")
        self.weight_input.setValue(self.product.weight or 0)
        self.weight_unit_input.setCurrentText(self.product.weight_unit or "كجم")
        self.notes_input.setText(self.product.notes or "")

        # حساب هامش الربح
        self.calculate_profit_margin()

    def calculate_profit_margin(self):
        """حساب هامش الربح"""
        purchase_price = self.purchase_price_input.value()
        selling_price = self.selling_price_input.value()

        if purchase_price > 0:
            margin = ((selling_price - purchase_price) / purchase_price) * 100
            self.profit_margin_label.setText(f"{margin:.1f}%")
        else:
            self.profit_margin_label.setText("0%")

    def validate_data(self):
        """التحقق من صحة البيانات"""
        # التحقق من الحقول المطلوبة
        if not self.name_input.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("name_required", "اسم المنتج مطلوب")
            )
            self.name_input.setFocus()
            return False

        if not self.code_input.text().strip():
            QMessageBox.warning(
                self,
                tr.get_text("validation_error", "خطأ في التحقق"),
                tr.get_text("code_required", "كود المنتج مطلوب")
            )
            self.code_input.setFocus()
            return False

        # التحقق من تفرد الكود
        try:
            db = next(get_db())
            existing_product = db.query(Product).filter(
                Product.code == self.code_input.text().strip(),
                Product.is_deleted == False
            )

            if self.is_edit_mode:
                existing_product = existing_product.filter(Product.id != self.product.id)

            if existing_product.first():
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("code_exists", "كود المنتج موجود بالفعل")
                )
                self.code_input.setFocus()
                return False

        except Exception as e:
            log_error(f"خطأ في التحقق من الكود: {str(e)}")
            return False

        # التحقق من الباركود إذا كان موجوداً
        barcode = self.barcode_input.text().strip()
        if barcode:
            try:
                existing_product = db.query(Product).filter(
                    Product.barcode == barcode,
                    Product.is_deleted == False
                )

                if self.is_edit_mode:
                    existing_product = existing_product.filter(Product.id != self.product.id)

                if existing_product.first():
                    QMessageBox.warning(
                        self,
                        tr.get_text("validation_error", "خطأ في التحقق"),
                        tr.get_text("barcode_exists", "الباركود موجود بالفعل")
                    )
                    self.barcode_input.setFocus()
                    return False

            except Exception as e:
                log_error(f"خطأ في التحقق من الباركود: {str(e)}")
                return False

        return True

    def save_product(self):
        """حفظ بيانات المنتج"""
        if not self.validate_data():
            return

        try:
            db = next(get_db())

            # إنشاء أو تحديث المنتج
            if self.is_edit_mode:
                product = self.product
            else:
                product = Product()
                db.add(product)

            # المعلومات الأساسية
            product.name = self.name_input.text().strip()
            product.code = self.code_input.text().strip()
            product.barcode = self.barcode_input.text().strip() or None
            product.description = self.description_input.toPlainText().strip() or None
            product.category_id = self.category_input.currentData()
            product.supplier_id = self.supplier_input.currentData()
            product.unit = self.unit_input.currentText()
            product.is_active = self.is_active_input.isChecked()
            product.is_service = self.is_service_input.isChecked()
            product.is_featured = self.is_featured_input.isChecked()

            # التسعير
            product.purchase_price = self.purchase_price_input.value()
            product.selling_price = self.selling_price_input.value()
            product.min_selling_price = self.min_selling_price_input.value() or None
            product.wholesale_price = self.wholesale_price_input.value() or None
            product.discount_percentage = self.discount_percentage_input.value() or None
            product.tax_rate = self.tax_rate_input.value() or None
            product.currency = self.currency_input.currentText()

            # المخزون
            product.quantity = self.quantity_input.value()
            product.min_quantity = self.min_quantity_input.value() or None
            product.max_quantity = self.max_quantity_input.value() or None
            product.reorder_point = self.reorder_point_input.value() or None
            product.reorder_quantity = self.reorder_quantity_input.value() or None
            product.location = self.location_input.text().strip() or None

            # التواريخ
            if self.manufacture_date_input.date().isValid():
                product.manufacture_date = self.manufacture_date_input.date().toPyDate()
            if self.expiry_date_input.date().isValid():
                product.expiry_date = self.expiry_date_input.date().toPyDate()

            # المعلومات الإضافية
            product.brand = self.brand_input.text().strip() or None
            product.model = self.model_input.text().strip() or None
            product.color = self.color_input.text().strip() or None
            product.size = self.size_input.text().strip() or None
            product.weight = self.weight_input.value() or None
            product.weight_unit = self.weight_unit_input.currentText()
            product.notes = self.notes_input.toPlainText().strip() or None

            # تحديث حالة المنتج
            product.update_status()

            db.commit()

            action = tr.get_text("updated", "تم تحديث") if self.is_edit_mode else tr.get_text("added", "تم إضافة")
            log_info(f"{action} المنتج: {product.name}")

            QMessageBox.information(
                self,
                tr.get_text("success", "نجح"),
                tr.get_text("product_saved", f"{action} المنتج بنجاح")
            )

            self.accept()

        except Exception as e:
            log_error(f"خطأ في حفظ المنتج: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_product", "حدث خطأ أثناء حفظ المنتج")
            )
