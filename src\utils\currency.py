#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
from datetime import datetime, timedelta
import requests
from typing import Dict, Optional
from pathlib import Path
from src.utils.logger import log_error, log_info
from src.utils import translation_manager as tr

class CurrencyManager:
    """Currency management and conversion system"""

    _instance = None
    SUPPORTED_CURRENCIES = {
        'EGP': {'name': 'Egyptian Pound', 'symbol': 'ج.م', 'name_ar': 'جنيه مصري'},
        'USD': {'name': 'US Dollar', 'symbol': '$', 'name_ar': 'دولار أمريكي'},
        'SAR': {'name': 'Saudi Riyal', 'symbol': 'ر.س', 'name_ar': 'ريال سعودي'},
        'KWD': {'name': 'Kuwaiti Dinar', 'symbol': 'د.ك', 'name_ar': 'دينار كويتي'},
        'AED': {'name': 'UAE Dirham', 'symbol': 'د.إ', 'name_ar': 'درهم إماراتي'},
        'EUR': {'name': 'Euro', 'symbol': '€', 'name_ar': 'يورو'}
    }

    @classmethod
    def get_instance(cls):
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize currency manager"""
        self.base_currency = 'USD'  # API base currency - يجب تعريفه أولاً
        self.app_data_path = Path(os.getenv('LOCALAPPDATA')) / 'Amin Al-Hisabat'
        self.rates_file = self.app_data_path / 'exchange_rates.json'
        self.app_data_path.mkdir(exist_ok=True)
        self.rates = self._load_rates()

    def _load_rates(self) -> Dict:
        """Load exchange rates from file"""
        try:
            if self.rates_file.exists():
                with open(self.rates_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if self._are_rates_valid(data):
                        return data
            return self._fetch_and_save_rates()
        except Exception as e:
            log_error(f"Error loading exchange rates: {str(e)}")
            return {}

    def _are_rates_valid(self, data: Dict) -> bool:
        """Check if stored rates are still valid (less than 24 hours old)"""
        try:
            last_update = datetime.fromisoformat(data.get('timestamp', ''))
            return datetime.now() - last_update < timedelta(hours=24)
        except:
            return False

    def _fetch_and_save_rates(self) -> Dict:
        """Fetch latest exchange rates and save to file"""
        try:
            # Using exchangerate-api.com as an example
            # You should replace this with your preferred currency API
            api_key = os.getenv('EXCHANGE_RATE_API_KEY', '')
            if not api_key:
                log_error("Missing exchange rate API key")
                return {}

            currencies = ','.join(self.SUPPORTED_CURRENCIES.keys())
            response = requests.get(
                f'https://api.exchangerate-api.com/v4/latest/{self.base_currency}',
                headers={'Authorization': f'Bearer {api_key}'}
            )

            if response.status_code == 200:
                data = response.json()
                rates = {
                    'timestamp': datetime.now().isoformat(),
                    'base': self.base_currency,
                    'rates': data['rates']
                }

                # Save to file
                with open(self.rates_file, 'w', encoding='utf-8') as f:
                    json.dump(rates, f, indent=4)

                log_info("Exchange rates updated successfully")
                return rates
            else:
                log_error(f"Failed to fetch exchange rates: {response.status_code}")
                return {}

        except Exception as e:
            log_error(f"Error fetching exchange rates: {str(e)}")
            return {}

    def convert(self, amount: float, from_currency: str, to_currency: str) -> Optional[float]:
        """
        Convert amount between currencies
        Returns None if conversion fails
        """
        try:
            if from_currency == to_currency:
                return amount

            rates = self.rates.get('rates', {})
            if not rates:
                return None

            # Convert to base currency first
            if from_currency != self.base_currency:
                amount = amount / rates.get(from_currency, 1)

            # Convert from base currency to target
            if to_currency != self.base_currency:
                amount = amount * rates.get(to_currency, 1)

            return round(amount, 2)

        except Exception as e:
            log_error(f"Error converting currency: {str(e)}")
            return None

    def format_amount(self, amount: float, currency: str, language: str = 'en') -> str:
        """Format amount with currency symbol based on language"""
        try:
            currency_info = self.SUPPORTED_CURRENCIES.get(currency, {})
            if not currency_info:
                return f"{amount} {currency}"

            if language == 'ar':
                return f"{amount} {currency_info['symbol']}"
            else:
                return f"{currency_info['symbol']}{amount}"

        except Exception as e:
            log_error(f"Error formatting currency: {str(e)}")
            return str(amount)

    def get_currency_name(self, currency: str, language: str = 'en') -> str:
        """Get currency name in specified language"""
        try:
            currency_info = self.SUPPORTED_CURRENCIES.get(currency, {})
            if not currency_info:
                return currency

            if language == 'ar':
                return currency_info['name_ar']
            else:
                return currency_info['name']

        except Exception as e:
            log_error(f"Error getting currency name: {str(e)}")
            return currency

    def update_rates(self) -> bool:
        """Force update of exchange rates"""
        try:
            self.rates = self._fetch_and_save_rates()
            return bool(self.rates)
        except Exception as e:
            log_error(f"Error updating rates: {str(e)}")
            return False

    @classmethod
    def get_supported_currencies(cls) -> Dict:
        """Get list of supported currencies"""
        return cls.SUPPORTED_CURRENCIES.copy()