#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة طلبات الشراء
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QPushButton, QComboBox, QDateEdit,
    QLineEdit, QLabel, QGroupBox, QFormLayout, QTextEdit, QSpinBox
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont
from src.utils.icon_manager import get_icon
from datetime import datetime, date

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledComboBox, StyledLabel, <PERSON>er<PERSON><PERSON>l,
    StyledTable, StyledDateEdit, StyledTextEdit, StyledSpinBox
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.database import get_db
from src.models import Supplier, Product

class PurchaseOrdersView(QWidget):
    """واجهة إدارة طلبات الشراء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.setup_ui()
        self.load_orders()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("purchase_orders", "طلبات الشراء"))
        layout.addWidget(header)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.add_btn = PrimaryButton(tr.get_text("add_order", "إضافة طلب"))
        self.add_btn.setIcon(get_icon("fa5s.plus", color="white"))
        self.add_btn.clicked.connect(self.add_order)
        actions_layout.addWidget(self.add_btn)
        
        self.edit_btn = StyledButton(tr.get_text("edit_order", "تعديل"))
        self.edit_btn.setIcon(get_icon("fa5s.edit", color="white"))
        self.edit_btn.clicked.connect(self.edit_order)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)
        
        self.approve_btn = SecondaryButton(tr.get_text("approve_order", "اعتماد"))
        self.approve_btn.setIcon(get_icon("fa5s.check", color="white"))
        self.approve_btn.clicked.connect(self.approve_order)
        self.approve_btn.setEnabled(False)
        actions_layout.addWidget(self.approve_btn)
        
        self.convert_btn = SecondaryButton(tr.get_text("convert_to_invoice", "تحويل لفاتورة"))
        self.convert_btn.setIcon(get_icon("fa5s.file-invoice", color="white"))
        self.convert_btn.clicked.connect(self.convert_to_invoice)
        self.convert_btn.setEnabled(False)
        actions_layout.addWidget(self.convert_btn)
        
        self.delete_btn = DangerButton(tr.get_text("delete_order", "حذف"))
        self.delete_btn.setIcon(get_icon("fa5s.trash", color="white"))
        self.delete_btn.clicked.connect(self.delete_order)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)
        
        actions_layout.addStretch()
        
        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(get_icon("fa5s.sync", color="white"))
        self.refresh_btn.clicked.connect(self.load_orders)
        actions_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(actions_layout)
        
        # فلاتر البحث
        filters_group = QGroupBox(tr.get_text("filters", "الفلاتر"))
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر الحالة
        filters_layout.addWidget(StyledLabel(tr.get_text("status", "الحالة")))
        self.status_filter = StyledComboBox()
        self.status_filter.addItems([
            tr.get_text("all", "الكل"),
            tr.get_text("pending", "معلق"),
            tr.get_text("approved", "معتمد"),
            tr.get_text("completed", "مكتمل"),
            tr.get_text("cancelled", "ملغي")
        ])
        self.status_filter.currentTextChanged.connect(self.filter_orders)
        filters_layout.addWidget(self.status_filter)
        
        # فلتر المورد
        filters_layout.addWidget(StyledLabel(tr.get_text("supplier", "المورد")))
        self.supplier_filter = StyledComboBox()
        self.load_suppliers_filter()
        self.supplier_filter.currentTextChanged.connect(self.filter_orders)
        filters_layout.addWidget(self.supplier_filter)
        
        # فلتر التاريخ
        filters_layout.addWidget(StyledLabel(tr.get_text("from_date", "من تاريخ")))
        self.from_date = StyledDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.dateChanged.connect(self.filter_orders)
        filters_layout.addWidget(self.from_date)
        
        filters_layout.addWidget(StyledLabel(tr.get_text("to_date", "إلى تاريخ")))
        self.to_date = StyledDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.dateChanged.connect(self.filter_orders)
        filters_layout.addWidget(self.to_date)
        
        filters_layout.addStretch()
        
        layout.addWidget(filters_group)
        
        # جدول طلبات الشراء
        self.orders_table = StyledTable()
        self.orders_table.setColumnCount(8)
        self.orders_table.setHorizontalHeaderLabels([
            tr.get_text("order_number", "رقم الطلب"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("supplier", "المورد"),
            tr.get_text("total_amount", "إجمالي المبلغ"),
            tr.get_text("status", "الحالة"),
            tr.get_text("expected_date", "التاريخ المتوقع"),
            tr.get_text("created_by", "أنشأ بواسطة"),
            tr.get_text("notes", "ملاحظات")
        ])
        
        # تعيين خصائص الجدول
        header = self.orders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.Stretch)
        
        self.orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.orders_table.setSelectionMode(QTableWidget.SingleSelection)
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.orders_table.doubleClicked.connect(self.edit_order)
        
        layout.addWidget(self.orders_table)
        
    def load_suppliers_filter(self):
        """تحميل قائمة الموردين للفلتر"""
        try:
            self.supplier_filter.clear()
            self.supplier_filter.addItem(tr.get_text("all_suppliers", "جميع الموردين"), None)
            
            suppliers = self.db.query(Supplier).filter(Supplier.is_active == True).order_by(Supplier.name).all()
            for supplier in suppliers:
                self.supplier_filter.addItem(supplier.name, supplier.id)
                
        except Exception as e:
            log_error(f"خطأ في تحميل الموردين: {str(e)}")
    
    def load_orders(self):
        """تحميل طلبات الشراء"""
        try:
            # TODO: تحميل طلبات الشراء من قاعدة البيانات
            # هذا مثال تجريبي
            sample_orders = [
                {
                    'order_number': 'PO-001',
                    'date': '2024-01-15',
                    'supplier': 'مورد تجريبي 1',
                    'total_amount': 1500.00,
                    'status': 'معلق',
                    'expected_date': '2024-01-20',
                    'created_by': 'أحمد محمد',
                    'notes': 'طلب عاجل'
                },
                {
                    'order_number': 'PO-002',
                    'date': '2024-01-14',
                    'supplier': 'مورد تجريبي 2',
                    'total_amount': 2300.00,
                    'status': 'معتمد',
                    'expected_date': '2024-01-18',
                    'created_by': 'فاطمة أحمد',
                    'notes': 'طلب شهري'
                }
            ]
            
            self.orders_table.setRowCount(len(sample_orders))
            
            for row, order in enumerate(sample_orders):
                # رقم الطلب
                order_item = QTableWidgetItem(order['order_number'])
                order_item.setFlags(order_item.flags() & ~Qt.ItemIsEditable)
                self.orders_table.setItem(row, 0, order_item)
                
                # التاريخ
                date_item = QTableWidgetItem(order['date'])
                date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
                self.orders_table.setItem(row, 1, date_item)
                
                # المورد
                supplier_item = QTableWidgetItem(order['supplier'])
                supplier_item.setFlags(supplier_item.flags() & ~Qt.ItemIsEditable)
                self.orders_table.setItem(row, 2, supplier_item)
                
                # إجمالي المبلغ
                amount_item = QTableWidgetItem(f"{order['total_amount']:.2f}")
                amount_item.setFlags(amount_item.flags() & ~Qt.ItemIsEditable)
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.orders_table.setItem(row, 3, amount_item)
                
                # الحالة
                status_item = QTableWidgetItem(order['status'])
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                if order['status'] == 'معلق':
                    status_item.setBackground(QBrush(QColor(255, 193, 7, 50)))
                elif order['status'] == 'معتمد':
                    status_item.setBackground(QBrush(QColor(40, 167, 69, 50)))
                elif order['status'] == 'مكتمل':
                    status_item.setBackground(QBrush(QColor(23, 162, 184, 50)))
                elif order['status'] == 'ملغي':
                    status_item.setBackground(QBrush(QColor(220, 53, 69, 50)))
                self.orders_table.setItem(row, 4, status_item)
                
                # التاريخ المتوقع
                expected_item = QTableWidgetItem(order['expected_date'])
                expected_item.setFlags(expected_item.flags() & ~Qt.ItemIsEditable)
                self.orders_table.setItem(row, 5, expected_item)
                
                # أنشأ بواسطة
                created_item = QTableWidgetItem(order['created_by'])
                created_item.setFlags(created_item.flags() & ~Qt.ItemIsEditable)
                self.orders_table.setItem(row, 6, created_item)
                
                # الملاحظات
                notes_item = QTableWidgetItem(order['notes'])
                notes_item.setFlags(notes_item.flags() & ~Qt.ItemIsEditable)
                self.orders_table.setItem(row, 7, notes_item)
                
        except Exception as e:
            log_error(f"خطأ في تحميل طلبات الشراء: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_orders", "حدث خطأ أثناء تحميل طلبات الشراء")
            )
    
    def filter_orders(self):
        """فلترة طلبات الشراء"""
        # TODO: تنفيذ فلترة طلبات الشراء
        pass
    
    def on_selection_changed(self):
        """عند تغيير التحديد"""
        selected_rows = self.orders_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0
        
        self.edit_btn.setEnabled(has_selection)
        self.approve_btn.setEnabled(has_selection)
        self.convert_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
    
    def add_order(self):
        """إضافة طلب شراء جديد"""
        dialog = PurchaseOrderDialog(self)
        if dialog.exec_() == dialog.Accepted:
            self.load_orders()
    
    def edit_order(self):
        """تعديل طلب الشراء المحدد"""
        selected_rows = self.orders_table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        # TODO: تنفيذ تعديل طلب الشراء
        QMessageBox.information(
            self,
            tr.get_text("info", "معلومات"),
            tr.get_text("feature_coming_soon", "هذه الميزة قيد التطوير")
        )
    
    def approve_order(self):
        """اعتماد طلب الشراء"""
        selected_rows = self.orders_table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_approve", "تأكيد الاعتماد"),
            tr.get_text("confirm_approve_order", "هل تريد اعتماد طلب الشراء المحدد؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # TODO: تنفيذ اعتماد طلب الشراء
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("order_approved", "تم اعتماد طلب الشراء")
            )
            self.load_orders()
    
    def convert_to_invoice(self):
        """تحويل طلب الشراء إلى فاتورة"""
        selected_rows = self.orders_table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        # TODO: تنفيذ تحويل طلب الشراء إلى فاتورة
        QMessageBox.information(
            self,
            tr.get_text("info", "معلومات"),
            tr.get_text("feature_coming_soon", "هذه الميزة قيد التطوير")
        )
    
    def delete_order(self):
        """حذف طلب الشراء"""
        selected_rows = self.orders_table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_order", "هل تريد حذف طلب الشراء المحدد؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # TODO: تنفيذ حذف طلب الشراء
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("order_deleted", "تم حذف طلب الشراء")
            )
            self.load_orders()

class PurchaseOrderDialog(QWidget):
    """نافذة إضافة/تعديل طلب شراء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr.get_text("add_purchase_order", "إضافة طلب شراء"))
        self.setMinimumSize(800, 600)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("add_purchase_order", "إضافة طلب شراء"))
        layout.addWidget(header)
        
        # نموذج البيانات الأساسية
        form_group = QGroupBox(tr.get_text("basic_info", "المعلومات الأساسية"))
        form_layout = QFormLayout(form_group)
        
        # رقم الطلب
        self.order_number_input = StyledLineEdit()
        self.order_number_input.setText("PO-" + datetime.now().strftime("%Y%m%d%H%M"))
        form_layout.addRow(StyledLabel(tr.get_text("order_number", "رقم الطلب")), self.order_number_input)
        
        # المورد
        self.supplier_combo = StyledComboBox()
        # TODO: تحميل الموردين
        form_layout.addRow(StyledLabel(tr.get_text("supplier", "المورد")), self.supplier_combo)
        
        # التاريخ المتوقع
        self.expected_date = StyledDateEdit()
        self.expected_date.setDate(QDate.currentDate().addDays(7))
        form_layout.addRow(StyledLabel(tr.get_text("expected_date", "التاريخ المتوقع")), self.expected_date)
        
        # الملاحظات
        self.notes_input = StyledTextEdit()
        form_layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات")), self.notes_input)
        
        layout.addWidget(form_group)
        
        # جدول المنتجات
        products_group = QGroupBox(tr.get_text("products", "المنتجات"))
        products_layout = QVBoxLayout(products_group)
        
        # أزرار إدارة المنتجات
        products_actions = QHBoxLayout()
        
        add_product_btn = PrimaryButton(tr.get_text("add_product", "إضافة منتج"))
        add_product_btn.clicked.connect(self.add_product)
        products_actions.addWidget(add_product_btn)
        
        remove_product_btn = DangerButton(tr.get_text("remove_product", "حذف منتج"))
        remove_product_btn.clicked.connect(self.remove_product)
        products_actions.addWidget(remove_product_btn)
        
        products_actions.addStretch()
        products_layout.addLayout(products_actions)
        
        # جدول المنتجات
        self.products_table = StyledTable()
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels([
            tr.get_text("product", "المنتج"),
            tr.get_text("quantity", "الكمية"),
            tr.get_text("unit_price", "سعر الوحدة"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("notes", "ملاحظات")
        ])
        products_layout.addWidget(self.products_table)
        
        layout.addWidget(products_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.clicked.connect(self.close)
        buttons_layout.addWidget(cancel_btn)
        
        buttons_layout.addStretch()
        
        save_btn = PrimaryButton(tr.get_text("save", "حفظ"))
        save_btn.clicked.connect(self.save_order)
        buttons_layout.addWidget(save_btn)
        
        layout.addLayout(buttons_layout)
    
    def add_product(self):
        """إضافة منتج للطلب"""
        # TODO: تنفيذ إضافة منتج
        pass
    
    def remove_product(self):
        """حذف منتج من الطلب"""
        # TODO: تنفيذ حذف منتج
        pass
    
    def save_order(self):
        """حفظ طلب الشراء"""
        # TODO: تنفيذ حفظ طلب الشراء
        QMessageBox.information(
            self,
            tr.get_text("success", "نجاح"),
            tr.get_text("order_saved", "تم حفظ طلب الشراء")
        )
        self.close()
