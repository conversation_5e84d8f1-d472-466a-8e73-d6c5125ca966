#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير البيانات التجريبية
يقوم بإنشاء بيانات تجريبية واقعية لجميع وحدات النظام
"""

import random
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.orm import Session

from src.database import get_db
from src.models import (
    User, Customer, Supplier, Product, ProductCategory,
    ExpenseCategory, Employee, Department, Position,
    TreasuryAccount, AccountType, Invoice, InvoiceItem,
    InvoiceType, InvoiceStatus, Expense, PaymentStatus,
    TreasuryTransaction, TransactionType, TransactionStatus
)
from src.utils.logger import log_info, log_error


class SampleDataManager:
    """مدير البيانات التجريبية"""

    def __init__(self):
        self.db = next(get_db())

    def create_all_sample_data(self):
        """إنشاء جميع البيانات التجريبية"""
        try:
            log_info("بدء إنشاء البيانات التجريبية...")

            # إنشاء مستخدم افتراضي أولاً
            self.create_default_user()

            # إنشاء البيانات الأساسية
            self.create_sample_categories()
            self.create_sample_customers()
            self.create_sample_suppliers()
            self.create_sample_products()

            # إنشاء بيانات الموظفين
            self.create_sample_departments()
            self.create_sample_positions()
            self.create_sample_employees()

            # إنشاء بيانات الخزينة
            self.create_sample_treasury_accounts()

            # إنشاء المعاملات
            self.create_sample_invoices()
            self.create_sample_expenses()
            self.create_sample_treasury_transactions()

            self.db.commit()
            log_info("تم إنشاء جميع البيانات التجريبية بنجاح")
            return True

        except Exception as e:
            self.db.rollback()
            log_error(f"خطأ في إنشاء البيانات التجريبية: {str(e)}")
            return False

    def create_default_user(self):
        """إنشاء مستخدم افتراضي"""
        if not self.db.query(User).filter_by(username="admin").first():
            from werkzeug.security import generate_password_hash

            admin_user = User(
                username="admin",
                email="<EMAIL>",
                password_hash=generate_password_hash("admin123"),
                full_name="مدير النظام",
                is_active=True,
                role="admin"
            )
            self.db.add(admin_user)
            self.db.flush()  # للحصول على معرف المستخدم

    def create_sample_categories(self):
        """إنشاء فئات تجريبية"""
        # فئات المنتجات
        product_categories = [
            ("إلكترونيات", "أجهزة إلكترونية ومعدات تقنية"),
            ("ملابس", "ملابس رجالية ونسائية وأطفال"),
            ("أغذية", "مواد غذائية ومشروبات"),
            ("مكتبية", "لوازم مكتبية وقرطاسية"),
            ("منزلية", "أدوات منزلية ومفروشات")
        ]

        for name, desc in product_categories:
            if not self.db.query(ProductCategory).filter_by(name=name).first():
                category = ProductCategory(name=name, description=desc)
                self.db.add(category)

        # فئات المصروفات
        expense_categories = [
            ("إيجار", "إيجار المحل والمكاتب"),
            ("مرافق", "كهرباء وماء وغاز وإنترنت"),
            ("رواتب", "رواتب الموظفين والمكافآت"),
            ("صيانة", "صيانة المعدات والأجهزة"),
            ("تسويق", "إعلانات وحملات تسويقية"),
            ("نقل", "مصاريف النقل والشحن"),
            ("أخرى", "مصاريف متنوعة")
        ]

        for name, desc in expense_categories:
            if not self.db.query(ExpenseCategory).filter_by(name=name).first():
                category = ExpenseCategory(name=name, description=desc)
                self.db.add(category)

    def create_sample_customers(self):
        """إنشاء عملاء تجريبيين"""
        customers_data = [
            ("أحمد محمد علي", "CUST001", "01012345678", "<EMAIL>", "القاهرة، مصر الجديدة"),
            ("فاطمة حسن محمود", "CUST002", "01123456789", "<EMAIL>", "الجيزة، المهندسين"),
            ("محمد عبدالله أحمد", "CUST003", "01234567890", "<EMAIL>", "الإسكندرية، سيدي جابر"),
            ("نورا سامي إبراهيم", "CUST004", "01098765432", "<EMAIL>", "القاهرة، مدينة نصر"),
            ("خالد عمر حسن", "CUST005", "01187654321", "<EMAIL>", "الجيزة، الدقي"),
            ("مريم أحمد محمد", "CUST006", "01276543210", "<EMAIL>", "القاهرة، التجمع الخامس"),
            ("يوسف محمود علي", "CUST007", "01165432109", "<EMAIL>", "الإسكندرية، العجمي"),
            ("سارة حسام الدين", "CUST008", "01054321098", "<EMAIL>", "القاهرة، الزمالك")
        ]

        for name, code, phone, email, address in customers_data:
            if not self.db.query(Customer).filter_by(code=code).first():
                customer = Customer(
                    name=name,
                    code=code,
                    phone=phone,
                    email=email,
                    address=address,
                    balance=random.uniform(-1000, 5000)
                )
                self.db.add(customer)

    def create_sample_suppliers(self):
        """إنشاء موردين تجريبيين"""
        suppliers_data = [
            ("شركة التقنية المتقدمة", "SUPP001", "02-12345678", "<EMAIL>", "القاهرة، مدينة نصر"),
            ("مؤسسة النسيج الحديث", "SUPP002", "03-23456789", "<EMAIL>", "الإسكندرية، كرموز"),
            ("شركة الأغذية الطازجة", "SUPP003", "02-34567890", "<EMAIL>", "الجيزة، أكتوبر"),
            ("مكتبة المعرفة للقرطاسية", "SUPP004", "02-45678901", "<EMAIL>", "القاهرة، وسط البلد"),
            ("شركة الأثاث العصري", "SUPP005", "02-56789012", "<EMAIL>", "القاهرة، التجمع الأول")
        ]

        for name, code, phone, email, address in suppliers_data:
            if not self.db.query(Supplier).filter_by(code=code).first():
                supplier = Supplier(
                    name=name,
                    code=code,
                    phone=phone,
                    email=email,
                    address=address,
                    balance=random.uniform(-2000, 10000)
                )
                self.db.add(supplier)

    def create_sample_products(self):
        """إنشاء منتجات تجريبية"""
        # الحصول على الفئات
        categories = self.db.query(ProductCategory).all()
        suppliers = self.db.query(Supplier).all()

        if not categories or not suppliers:
            return

        products_data = [
            # إلكترونيات
            ("لابتوب ديل", "LAP001", 15000, 18000, 10, "لابتوب ديل انسبايرون 15"),
            ("هاتف سامسونج", "PHN001", 8000, 10000, 25, "هاتف سامسونج جالاكسي"),
            ("سماعات بلوتوث", "HDP001", 500, 750, 50, "سماعات لاسلكية عالية الجودة"),

            # ملابس
            ("قميص قطني", "SHT001", 150, 250, 100, "قميص قطني رجالي"),
            ("فستان صيفي", "DRS001", 300, 500, 30, "فستان صيفي نسائي"),
            ("حذاء رياضي", "SHO001", 400, 650, 40, "حذاء رياضي للجري"),

            # أغذية
            ("أرز بسمتي", "RIC001", 25, 35, 200, "أرز بسمتي فاخر كيس 5 كيلو"),
            ("زيت طبخ", "OIL001", 45, 60, 150, "زيت طبخ نباتي 1 لتر"),
            ("سكر أبيض", "SUG001", 20, 30, 300, "سكر أبيض كيس 2 كيلو"),

            # مكتبية
            ("دفتر ملاحظات", "NOT001", 15, 25, 500, "دفتر ملاحظات 100 ورقة"),
            ("قلم حبر جاف", "PEN001", 5, 10, 1000, "قلم حبر جاف أزرق"),
            ("مجلد ملفات", "FLD001", 20, 35, 200, "مجلد ملفات بلاستيك"),

            # منزلية
            ("طقم أكواب", "CUP001", 80, 120, 60, "طقم أكواب زجاج 6 قطع"),
            ("مفرش سرير", "BED001", 200, 350, 25, "مفرش سرير قطني مزدوج"),
            ("مكنسة كهربائية", "VAC001", 800, 1200, 15, "مكنسة كهربائية قوية")
        ]

        for name, code, cost, price, stock, desc in products_data:
            if not self.db.query(Product).filter_by(code=code).first():
                # اختيار فئة ومورد عشوائي
                category = random.choice(categories)
                supplier = random.choice(suppliers)

                try:
                    product = Product(
                        name=name,
                        code=code,
                        purchase_price=cost,
                        selling_price=price,
                        quantity=stock,
                        description=desc,
                        category_id=category.id,
                        supplier_id=supplier.id
                    )
                    self.db.add(product)
                    self.db.flush()  # للتأكد من الحفظ
                except Exception as e:
                    log_error(f"خطأ في إنشاء المنتج {name}: {str(e)}")
                    continue

    def create_sample_departments(self):
        """إنشاء أقسام تجريبية"""
        departments_data = [
            ("المبيعات", "قسم المبيعات والتسويق"),
            ("المحاسبة", "قسم المحاسبة والمالية"),
            ("المخازن", "قسم إدارة المخازن والمخزون"),
            ("خدمة العملاء", "قسم خدمة العملاء والدعم الفني"),
            ("الإدارة", "الإدارة العليا والتخطيط")
        ]

        for name, desc in departments_data:
            if not self.db.query(Department).filter_by(name=name).first():
                department = Department(name=name, description=desc)
                self.db.add(department)

    def create_sample_positions(self):
        """إنشاء مناصب تجريبية"""
        positions_data = [
            ("مدير عام", "المدير العام للشركة", 15000),
            ("مدير مبيعات", "مدير قسم المبيعات", 8000),
            ("محاسب", "محاسب رئيسي", 6000),
            ("موظف مبيعات", "موظف مبيعات", 4000),
            ("أمين مخزن", "أمين مخزن", 3500),
            ("موظف خدمة عملاء", "موظف خدمة عملاء", 3000),
            ("سائق", "سائق توصيل", 2500)
        ]

        for title, desc, salary in positions_data:
            if not self.db.query(Position).filter_by(title=title).first():
                position = Position(
                    title=title,
                    description=desc,
                    base_salary=salary
                )
                self.db.add(position)

    def create_sample_employees(self):
        """إنشاء موظفين تجريبيين"""
        departments = self.db.query(Department).all()
        positions = self.db.query(Position).all()

        if not departments or not positions:
            return

        employees_data = [
            ("أحمد", "محمد السيد", "EMP001", "01012345678", "<EMAIL>", "مدير عام"),
            ("سارة", "أحمد علي", "EMP002", "01123456789", "<EMAIL>", "مدير مبيعات"),
            ("محمد", "حسن محمود", "EMP003", "01234567890", "<EMAIL>", "محاسب"),
            ("فاطمة", "عبدالله", "EMP004", "01098765432", "<EMAIL>", "موظف مبيعات"),
            ("خالد", "عمر حسن", "EMP005", "01187654321", "<EMAIL>", "أمين مخزن"),
            ("نورا", "سامي إبراهيم", "EMP006", "01276543210", "<EMAIL>", "موظف خدمة عملاء"),
            ("يوسف", "محمود علي", "EMP007", "01165432109", "<EMAIL>", "سائق")
        ]

        for first_name, last_name, emp_id, phone, email, position_title in employees_data:
            if not self.db.query(Employee).filter_by(employee_id=emp_id).first():
                # البحث عن المنصب والقسم
                position = self.db.query(Position).filter_by(title=position_title).first()
                department = random.choice(departments)

                if position:
                    try:
                        employee = Employee(
                            employee_id=emp_id,
                            first_name=first_name,
                            last_name=last_name,
                            phone=phone,
                            email=email,
                            position_id=position.id,
                            department_id=department.id,
                            hire_date=datetime.now() - timedelta(days=random.randint(30, 1000)),
                            salary=position.base_salary + random.randint(-500, 1000)
                        )
                        self.db.add(employee)
                        self.db.flush()
                    except Exception as e:
                        log_error(f"خطأ في إنشاء الموظف {first_name} {last_name}: {str(e)}")
                        continue

    def create_sample_treasury_accounts(self):
        """إنشاء حسابات خزينة تجريبية"""
        accounts_data = [
            ("الخزينة الرئيسية", AccountType.CASH, "EGP", 50000, "الخزينة النقدية الرئيسية"),
            ("البنك الأهلي المصري", AccountType.BANK, "EGP", 150000, "حساب جاري بالبنك الأهلي"),
            ("بنك مصر", AccountType.BANK, "EGP", 80000, "حساب توفير ببنك مصر"),
            ("صندوق صغير", AccountType.CASH, "EGP", 5000, "صندوق نقدية صغير للمصاريف اليومية"),
            ("حساب بالدولار", AccountType.BANK, "USD", 10000, "حساب بالعملة الأجنبية")
        ]

        for name, acc_type, currency, balance, desc in accounts_data:
            if not self.db.query(TreasuryAccount).filter_by(name=name).first():
                account = TreasuryAccount(
                    name=name,
                    account_type=acc_type,
                    currency=currency,
                    balance=balance,
                    description=desc,
                    is_active=True
                )
                self.db.add(account)

    def create_sample_invoices(self):
        """إنشاء فواتير تجريبية"""
        customers = self.db.query(Customer).all()
        suppliers = self.db.query(Supplier).all()
        products = self.db.query(Product).all()

        if not customers or not suppliers or not products:
            return

        # إنشاء فواتير مبيعات
        for i in range(20):
            customer = random.choice(customers)
            invoice_date = datetime.now() - timedelta(days=random.randint(1, 90))

            invoice = Invoice(
                invoice_number=f"SAL-{2024}-{1000 + i}",
                invoice_type=InvoiceType.SALES,
                invoice_date=invoice_date,
                customer_id=customer.id,
                status=random.choice(list(InvoiceStatus)),
                total_amount=0,
                notes=f"فاتورة مبيعات للعميل {customer.name}"
            )
            self.db.add(invoice)
            self.db.flush()  # للحصول على معرف الفاتورة

            # إضافة عناصر الفاتورة
            total = 0
            num_items = random.randint(1, 5)
            for j in range(num_items):
                product = random.choice(products)
                quantity = random.randint(1, 10)
                unit_price = product.selling_price
                item_total = quantity * unit_price
                total += item_total

                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=product.id,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=item_total
                )
                self.db.add(invoice_item)

            invoice.total_amount = total

        # إنشاء فواتير مشتريات
        for i in range(15):
            supplier = random.choice(suppliers)
            invoice_date = datetime.now() - timedelta(days=random.randint(1, 60))

            invoice = Invoice(
                invoice_number=f"PUR-{2024}-{2000 + i}",
                invoice_type=InvoiceType.PURCHASE,
                invoice_date=invoice_date,
                supplier_id=supplier.id,
                status=random.choice(list(InvoiceStatus)),
                total_amount=0,
                notes=f"فاتورة مشتريات من المورد {supplier.name}"
            )
            self.db.add(invoice)
            self.db.flush()

            # إضافة عناصر الفاتورة
            total = 0
            num_items = random.randint(1, 8)
            for j in range(num_items):
                product = random.choice(products)
                quantity = random.randint(5, 50)
                unit_price = product.purchase_price
                item_total = quantity * unit_price
                total += item_total

                invoice_item = InvoiceItem(
                    invoice_id=invoice.id,
                    product_id=product.id,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=item_total
                )
                self.db.add(invoice_item)

            invoice.total_amount = total

    def create_sample_expenses(self):
        """إنشاء مصروفات تجريبية"""
        categories = self.db.query(ExpenseCategory).all()

        if not categories:
            return

        expenses_data = [
            ("إيجار المحل - ديسمبر", "إيجار", 8000, "إيجار المحل لشهر ديسمبر"),
            ("فاتورة كهرباء", "مرافق", 1200, "فاتورة استهلاك الكهرباء"),
            ("فاتورة مياه", "مرافق", 300, "فاتورة استهلاك المياه"),
            ("راتب أحمد محمد", "رواتب", 8000, "راتب شهر ديسمبر"),
            ("راتب سارة أحمد", "رواتب", 6000, "راتب شهر ديسمبر"),
            ("صيانة الكمبيوتر", "صيانة", 500, "صيانة جهاز الكمبيوتر الرئيسي"),
            ("إعلان فيسبوك", "تسويق", 800, "حملة إعلانية على فيسبوك"),
            ("بنزين السيارة", "نقل", 600, "تعبئة وقود سيارة التوصيل"),
            ("لوازم مكتبية", "أخرى", 250, "شراء أوراق وأقلام"),
            ("تنظيف المحل", "أخرى", 400, "خدمة تنظيف أسبوعية")
        ]

        for title, cat_name, amount, desc in expenses_data:
            # البحث عن الفئة
            category = self.db.query(ExpenseCategory).filter_by(name=cat_name).first()

            if category:
                # تحديد نوع المصروف بناءً على الفئة
                from src.models.expense import ExpenseType
                expense_type_mapping = {
                    "إيجار": ExpenseType.RENT,
                    "مرافق": ExpenseType.UTILITIES,
                    "رواتب": ExpenseType.SALARIES,
                    "صيانة": ExpenseType.MAINTENANCE,
                    "تسويق": ExpenseType.MARKETING,
                    "نقل": ExpenseType.TRANSPORTATION,
                    "أخرى": ExpenseType.OTHER
                }

                expense_type = expense_type_mapping.get(cat_name, ExpenseType.OTHER)

                expense = Expense(
                    title=title,
                    description=desc,
                    expense_date=datetime.now() - timedelta(days=random.randint(1, 30)),
                    category=expense_type,
                    amount=amount,
                    total_amount=amount,
                    paid_amount=amount if random.choice([True, False]) else 0,
                    status=PaymentStatus.PAID if random.choice([True, False]) else PaymentStatus.PENDING,
                    category_id=category.id,
                    created_by_id=self.db.query(User).first().id
                )
                self.db.add(expense)

    def create_sample_treasury_transactions(self):
        """إنشاء معاملات خزينة تجريبية"""
        accounts = self.db.query(TreasuryAccount).all()

        if not accounts:
            return

        # معاملات متنوعة
        transactions_data = [
            ("إيداع نقدي", TransactionType.DEPOSIT, 10000, "إيداع نقدي من المبيعات"),
            ("سحب للمصاريف", TransactionType.WITHDRAWAL, 2000, "سحب نقدي للمصاريف اليومية"),
            ("تحويل بنكي وارد", TransactionType.DEPOSIT, 15000, "تحويل من عميل"),
            ("دفع فاتورة", TransactionType.WITHDRAWAL, 1200, "دفع فاتورة كهرباء"),
            ("إيداع شيك", TransactionType.DEPOSIT, 8500, "إيداع شيك من عميل"),
            ("سحب راتب", TransactionType.WITHDRAWAL, 6000, "صرف راتب موظف"),
            ("إيداع مبيعات", TransactionType.DEPOSIT, 5500, "إيداع حصيلة مبيعات اليوم"),
            ("دفع مورد", TransactionType.WITHDRAWAL, 12000, "دفع مستحقات مورد"),
            ("عمولة بنكية", TransactionType.WITHDRAWAL, 50, "عمولة تحويل بنكي"),
            ("فوائد حساب", TransactionType.DEPOSIT, 200, "فوائد حساب التوفير")
        ]

        for desc, trans_type, amount, notes in transactions_data:
            account = random.choice(accounts)

            transaction = TreasuryTransaction(
                account_id=account.id,
                transaction_type=trans_type,
                amount=amount,
                description=desc,
                notes=notes,
                transaction_date=datetime.now() - timedelta(days=random.randint(1, 60)),
                status=TransactionStatus.COMPLETED
            )
            self.db.add(transaction)

    def clear_all_data(self):
        """مسح جميع البيانات التجريبية"""
        try:
            log_info("بدء مسح البيانات التجريبية...")

            # مسح البيانات بالترتيب الصحيح (العلاقات أولاً)
            self.db.query(TreasuryTransaction).delete()
            self.db.query(InvoiceItem).delete()
            self.db.query(Invoice).delete()
            self.db.query(Expense).delete()
            self.db.query(Employee).delete()
            self.db.query(Product).delete()
            self.db.query(TreasuryAccount).delete()
            self.db.query(Customer).delete()
            self.db.query(Supplier).delete()
            self.db.query(Department).delete()
            self.db.query(Position).delete()
            self.db.query(ProductCategory).delete()
            self.db.query(ExpenseCategory).delete()

            self.db.commit()
            log_info("تم مسح جميع البيانات التجريبية بنجاح")
            return True

        except Exception as e:
            self.db.rollback()
            log_error(f"خطأ في مسح البيانات التجريبية: {str(e)}")
            return False

    def get_data_summary(self):
        """الحصول على ملخص البيانات الموجودة"""
        try:
            summary = {
                'customers': self.db.query(Customer).count(),
                'suppliers': self.db.query(Supplier).count(),
                'products': self.db.query(Product).count(),
                'employees': self.db.query(Employee).count(),
                'invoices': self.db.query(Invoice).count(),
                'expenses': self.db.query(Expense).count(),
                'treasury_accounts': self.db.query(TreasuryAccount).count(),
                'treasury_transactions': self.db.query(TreasuryTransaction).count(),
                'product_categories': self.db.query(ProductCategory).count(),
                'expense_categories': self.db.query(ExpenseCategory).count(),
                'departments': self.db.query(Department).count(),
                'positions': self.db.query(Position).count()
            }
            return summary

        except Exception as e:
            log_error(f"خطأ في الحصول على ملخص البيانات: {str(e)}")
            return {}


def create_sample_data():
    """دالة مساعدة لإنشاء البيانات التجريبية"""
    manager = SampleDataManager()
    return manager.create_all_sample_data()


def clear_sample_data():
    """دالة مساعدة لمسح البيانات التجريبية"""
    manager = SampleDataManager()
    return manager.clear_all_data()


def get_sample_data_summary():
    """دالة مساعدة للحصول على ملخص البيانات"""
    manager = SampleDataManager()
    return manager.get_data_summary()