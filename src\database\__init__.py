#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from src.database.db_config import db_config
from sqlalchemy.orm import Session

def get_db() -> Session:
    """
    الحصول على جلسة قاعدة البيانات
    يستخدم كمولد للحصول على جلسة جديدة
    """
    db = db_config.SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """
    تهيئة قاعدة البيانات
    يقوم بإنشاء جميع الجداول وإعداد البيانات الأولية إذا لزم الأمر
    """
    try:
        # إنشاء الجداول
        db_config.create_database()
        
        # إنشاء جلسة
        db = next(get_db())
        
        # إضافة مستخدم المسؤول الافتراضي إذا لم يكن موجوداً
        from src.models import User
        User.create_admin_user(db)
        
        # يمكن إضافة المزيد من البيانات الأولية هنا
        
        return True
    except Exception as e:
        from src.utils import log_error
        log_error(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
        return False

__all__ = ['db_config', 'get_db', 'init_db']