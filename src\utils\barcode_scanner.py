#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام دعم الباركود والماسح الضوئي
يدعم:
- مسح الباركود بالكاميرا
- ماسح ضوئي USB
- إدخال الباركود يدوياً
- أنواع باركود متعددة
"""

import cv2
import numpy as np
from pyzbar import pyzbar
import threading
import time
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt5.QtWidgets import QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
from PyQt5.QtGui import QPixmap, QImage
import qtawesome as qta

from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config

class BarcodeScanner(QObject):
    """ماسح الباركود"""
    
    # إشارات
    barcode_detected = pyqtSignal(str, str)  # barcode_data, barcode_type
    scanner_error = pyqtSignal(str)
    scanner_status_changed = pyqtSignal(bool)  # is_active
    
    def __init__(self):
        super().__init__()
        self.is_scanning = False
        self.camera = None
        self.scan_thread = None
        self.camera_index = config.get_setting('barcode_camera_index', 0)
        
    def start_camera_scan(self):
        """بدء مسح الباركود بالكاميرا"""
        try:
            if self.is_scanning:
                return
                
            self.camera = cv2.VideoCapture(self.camera_index)
            if not self.camera.isOpened():
                self.scanner_error.emit(tr.get_text("camera_not_found", "لم يتم العثور على الكاميرا"))
                return
                
            self.is_scanning = True
            self.scanner_status_changed.emit(True)
            
            # بدء خيط المسح
            self.scan_thread = threading.Thread(target=self._scan_loop, daemon=True)
            self.scan_thread.start()
            
            log_info("تم بدء مسح الباركود بالكاميرا")
            
        except Exception as e:
            log_error(f"خطأ في بدء مسح الباركود: {str(e)}")
            self.scanner_error.emit(str(e))
    
    def stop_camera_scan(self):
        """إيقاف مسح الباركود"""
        try:
            self.is_scanning = False
            self.scanner_status_changed.emit(False)
            
            if self.camera:
                self.camera.release()
                self.camera = None
                
            if self.scan_thread:
                self.scan_thread.join(timeout=2)
                self.scan_thread = None
                
            log_info("تم إيقاف مسح الباركود")
            
        except Exception as e:
            log_error(f"خطأ في إيقاف مسح الباركود: {str(e)}")
    
    def _scan_loop(self):
        """حلقة مسح الباركود"""
        try:
            while self.is_scanning and self.camera:
                ret, frame = self.camera.read()
                if not ret:
                    continue
                    
                # البحث عن باركود في الإطار
                barcodes = pyzbar.decode(frame)
                
                for barcode in barcodes:
                    # فك تشفير البيانات
                    barcode_data = barcode.data.decode('utf-8')
                    barcode_type = barcode.type
                    
                    # إرسال إشارة الاكتشاف
                    self.barcode_detected.emit(barcode_data, barcode_type)
                    
                    log_info(f"تم اكتشاف باركود: {barcode_data} ({barcode_type})")
                    
                    # توقف قصير لتجنب القراءات المتكررة
                    time.sleep(1)
                    
                # توقف قصير لتوفير الموارد
                time.sleep(0.1)
                
        except Exception as e:
            log_error(f"خطأ في حلقة مسح الباركود: {str(e)}")
            self.scanner_error.emit(str(e))
    
    def scan_from_usb(self, port=None):
        """مسح من ماسح ضوئي USB"""
        try:
            # TODO: تنفيذ دعم الماسح الضوئي USB
            # هذا يتطلب مكتبات خاصة حسب نوع الماسح
            self.scanner_error.emit(tr.get_text("usb_scanner_not_implemented", "دعم الماسح الضوئي USB غير مُنفذ بعد"))
            
        except Exception as e:
            log_error(f"خطأ في مسح USB: {str(e)}")
            self.scanner_error.emit(str(e))
    
    def validate_barcode(self, barcode_data, barcode_type):
        """التحقق من صحة الباركود"""
        try:
            # التحقق من طول الباركود
            if len(barcode_data) < 3:
                return False, tr.get_text("barcode_too_short", "الباركود قصير جداً")
            
            # التحقق من نوع الباركود المدعوم
            supported_types = ['CODE128', 'CODE39', 'EAN13', 'EAN8', 'UPCA', 'UPCE', 'QR']
            if barcode_type not in supported_types:
                return False, tr.get_text("barcode_type_not_supported", f"نوع الباركود {barcode_type} غير مدعوم")
            
            # التحقق من الأحرف المسموحة
            if barcode_type in ['EAN13', 'EAN8', 'UPCA', 'UPCE']:
                if not barcode_data.isdigit():
                    return False, tr.get_text("barcode_invalid_chars", "الباركود يحتوي على أحرف غير صالحة")
            
            return True, ""
            
        except Exception as e:
            log_error(f"خطأ في التحقق من الباركود: {str(e)}")
            return False, str(e)

class BarcodeScanDialog(QDialog):
    """نافذة مسح الباركود"""
    
    barcode_scanned = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.scanner = BarcodeScanner()
        self.setup_ui()
        self.connect_signals()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("barcode_scanner", "ماسح الباركود"))
        self.setMinimumSize(400, 300)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # العنوان
        title_label = QLabel(tr.get_text("scan_barcode_title", "مسح الباركود"))
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # منطقة عرض الكاميرا
        self.camera_label = QLabel(tr.get_text("camera_preview", "معاينة الكاميرا"))
        self.camera_label.setMinimumHeight(200)
        self.camera_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #ccc;
                border-radius: 8px;
                text-align: center;
                background-color: #f9f9f9;
            }
        """)
        layout.addWidget(self.camera_label)
        
        # حالة المسح
        self.status_label = QLabel(tr.get_text("scanner_ready", "الماسح جاهز"))
        self.status_label.setStyleSheet("color: #666; margin: 5px;")
        layout.addWidget(self.status_label)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.start_btn = QPushButton(tr.get_text("start_scanning", "بدء المسح"))
        self.start_btn.setIcon(qta.icon("fa5s.play"))
        self.start_btn.clicked.connect(self.start_scanning)
        buttons_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton(tr.get_text("stop_scanning", "إيقاف المسح"))
        self.stop_btn.setIcon(qta.icon("fa5s.stop"))
        self.stop_btn.clicked.connect(self.stop_scanning)
        self.stop_btn.setEnabled(False)
        buttons_layout.addWidget(self.stop_btn)
        
        buttons_layout.addStretch()
        
        cancel_btn = QPushButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.setIcon(qta.icon("fa5s.times"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
    def connect_signals(self):
        """ربط الإشارات"""
        self.scanner.barcode_detected.connect(self.on_barcode_detected)
        self.scanner.scanner_error.connect(self.on_scanner_error)
        self.scanner.scanner_status_changed.connect(self.on_status_changed)
        
    def start_scanning(self):
        """بدء المسح"""
        self.scanner.start_camera_scan()
        
    def stop_scanning(self):
        """إيقاف المسح"""
        self.scanner.stop_camera_scan()
        
    def on_barcode_detected(self, barcode_data, barcode_type):
        """عند اكتشاف باركود"""
        # التحقق من صحة الباركود
        is_valid, error_msg = self.scanner.validate_barcode(barcode_data, barcode_type)
        
        if is_valid:
            self.barcode_scanned.emit(barcode_data)
            self.stop_scanning()
            self.accept()
        else:
            self.status_label.setText(f"{tr.get_text('invalid_barcode', 'باركود غير صالح')}: {error_msg}")
            self.status_label.setStyleSheet("color: red; margin: 5px;")
            
    def on_scanner_error(self, error_msg):
        """عند حدوث خطأ في المسح"""
        self.status_label.setText(f"{tr.get_text('scanner_error', 'خطأ في الماسح')}: {error_msg}")
        self.status_label.setStyleSheet("color: red; margin: 5px;")
        
        QMessageBox.warning(
            self,
            tr.get_text("scanner_error", "خطأ في الماسح"),
            error_msg
        )
        
    def on_status_changed(self, is_active):
        """عند تغيير حالة المسح"""
        if is_active:
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.status_label.setText(tr.get_text("scanning_active", "المسح نشط..."))
            self.status_label.setStyleSheet("color: green; margin: 5px;")
        else:
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.status_label.setText(tr.get_text("scanner_ready", "الماسح جاهز"))
            self.status_label.setStyleSheet("color: #666; margin: 5px;")
            
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.stop_scanning()
        super().closeEvent(event)

class BarcodeGenerator:
    """مولد الباركود"""
    
    @staticmethod
    def generate_barcode(data, barcode_type='CODE128'):
        """توليد باركود"""
        try:
            from barcode import get_barcode_class
            from barcode.writer import ImageWriter
            import io
            
            # اختيار نوع الباركود
            barcode_class = get_barcode_class(barcode_type)
            
            # إنشاء الباركود
            barcode = barcode_class(data, writer=ImageWriter())
            
            # تحويل إلى صورة
            buffer = io.BytesIO()
            barcode.write(buffer)
            buffer.seek(0)
            
            return buffer.getvalue()
            
        except Exception as e:
            log_error(f"خطأ في توليد الباركود: {str(e)}")
            return None
    
    @staticmethod
    def generate_qr_code(data):
        """توليد QR Code"""
        try:
            import qrcode
            import io
            
            # إنشاء QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # تحويل إلى صورة
            img = qr.make_image(fill_color="black", back_color="white")
            
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            
            return buffer.getvalue()
            
        except Exception as e:
            log_error(f"خطأ في توليد QR Code: {str(e)}")
            return None

class BarcodeUtils:
    """أدوات الباركود المساعدة"""
    
    @staticmethod
    def is_valid_ean13(barcode):
        """التحقق من صحة EAN-13"""
        if len(barcode) != 13 or not barcode.isdigit():
            return False
            
        # حساب checksum
        odd_sum = sum(int(barcode[i]) for i in range(0, 12, 2))
        even_sum = sum(int(barcode[i]) for i in range(1, 12, 2))
        checksum = (10 - ((odd_sum + even_sum * 3) % 10)) % 10
        
        return checksum == int(barcode[12])
    
    @staticmethod
    def is_valid_ean8(barcode):
        """التحقق من صحة EAN-8"""
        if len(barcode) != 8 or not barcode.isdigit():
            return False
            
        # حساب checksum
        odd_sum = sum(int(barcode[i]) for i in range(0, 7, 2))
        even_sum = sum(int(barcode[i]) for i in range(1, 7, 2))
        checksum = (10 - ((odd_sum * 3 + even_sum) % 10)) % 10
        
        return checksum == int(barcode[7])
    
    @staticmethod
    def format_barcode_display(barcode_data, barcode_type):
        """تنسيق عرض الباركود"""
        if barcode_type in ['EAN13', 'EAN8']:
            # إضافة فواصل للقراءة السهلة
            if len(barcode_data) == 13:
                return f"{barcode_data[:1]}-{barcode_data[1:7]}-{barcode_data[7:12]}-{barcode_data[12:]}"
            elif len(barcode_data) == 8:
                return f"{barcode_data[:4]}-{barcode_data[4:]}"
        
        return barcode_data
