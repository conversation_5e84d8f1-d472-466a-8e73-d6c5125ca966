#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة لوحة التحكم الرئيسية (Dashboard)
تعرض البطاقات والإحصائيات وتوفر التنقل بين أقسام البرنامج
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QLabel,
    QSizePolicy, QSpacerItem, QPushButton, QFrame, QToolButton,
    QComboBox, QMenu, QAction, QGraphicsDropShadowEffect
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QPoint, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor, QPainter, QPen, QPainterPath

import qtawesome as qta

from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.utils import translation_manager as tr, log_info, log_error, config
from src.database import get_db
from src.models import (
    Invoice, Product, Customer, Supplier, Expense, Income, Payment
)
from src.ui.widgets.charts import create_sales_chart, create_expenses_pie_chart
from datetime import datetime, timedelta

class ModuleCard(QFrame):
    """
    بطاقة وحدة في لوحة التحكم
    تعرض معلومات الوحدة مع أيقونة ووصف
    """

    clicked = pyqtSignal()

    def __init__(self, title, icon_name=None, module_name=None, parent=None, data=None):
        super().__init__(parent)
        self.title = title
        self.icon_name = icon_name
        self.module_name = module_name
        self.data = data or {}
        self.color = get_module_color(module_name) if module_name else get_ui_color('card', 'dark')

        # إعداد تأثير الظل
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(15)
        self.shadow.setColor(QColor(0, 0, 0, 80))
        self.shadow.setOffset(0, 2)
        self.setGraphicsEffect(self.shadow)

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين خصائص البطاقة
        self.setFrameShape(QFrame.StyledPanel)
        self.setCursor(Qt.PointingHandCursor)
        self.setMinimumSize(250, 180)
        self.setMaximumSize(350, 250)

        # تعيين نمط البطاقة
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border-radius: 12px;
                border: none;
            }}
            QFrame:hover {{
                border: 2px solid white;
            }}
            QLabel {{
                background-color: transparent;
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # الأيقونة في الأعلى
        if self.icon_name:
            icon_layout = QHBoxLayout()
            icon_layout.setContentsMargins(0, 0, 0, 0)

            # إنشاء أيقونة باستخدام qtawesome
            icon_label = QLabel()
            icon = qta.icon(self.icon_name, color='white')
            pixmap = icon.pixmap(32, 32)
            icon_label.setPixmap(pixmap)

            # وضع الأيقونة في الجانب المناسب حسب اللغة
            if tr.get_direction() == 'rtl':
                icon_layout.addStretch()
                icon_layout.addWidget(icon_label, alignment=Qt.AlignLeft)
            else:
                icon_layout.addWidget(icon_label, alignment=Qt.AlignRight)
                icon_layout.addStretch()

            layout.addLayout(icon_layout)

        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: white;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # عرض البيانات إذا كانت متوفرة
        if self.data:
            data_layout = QVBoxLayout()
            data_layout.setSpacing(5)

            for key, value in self.data.items():
                data_label = QLabel(f"{key}: {value}")
                data_label.setStyleSheet(f"""
                    font-size: {get_font_size('normal')};
                    color: white;
                """)
                data_label.setAlignment(Qt.AlignCenter)
                data_layout.addWidget(data_label)

            layout.addLayout(data_layout)
        # الوصف
        elif self.module_name:
            descriptions = {
                "inventory": tr.get_text("inventory_desc", "إدارة المخزون والمنتجات"),
                "treasury": tr.get_text("treasury_desc", "إدارة الخزينة والمدفوعات"),
                "invoices": tr.get_text("invoices_desc", "إدارة الفواتير والمبيعات"),
                "definitions": tr.get_text("definitions_desc", "إعدادات وتعريفات النظام"),
                "sales_report": tr.get_text("sales_report_desc", "تقارير المبيعات اليومية"),
                "expenses_report": tr.get_text("expenses_report_desc", "تقارير المصروفات اليومية"),
                "treasury_report": tr.get_text("treasury_report_desc", "تقارير الخزينة اليومية"),
                "chat": tr.get_text("chat_desc", "التواصل مع الفريق"),
                "recent_sales": tr.get_text("recent_sales_desc", "آخر المبيعات")
            }

            description = descriptions.get(self.module_name, "")
            if description:
                desc_label = QLabel(description)
                desc_label.setStyleSheet(f"""
                    font-size: {get_font_size('normal')};
                    color: white;
                """)
                desc_label.setWordWrap(True)
                desc_label.setAlignment(Qt.AlignCenter)
                layout.addWidget(desc_label)

        # مساحة متمددة
        layout.addStretch()

    def mousePressEvent(self, event):
        """معالجة حدث الضغط بالماوس"""
        super().mousePressEvent(event)
        self.clicked.emit()

class StatCard(QFrame):
    """
    بطاقة إحصائية في لوحة التحكم
    تعرض إحصائية مع عنوان وقيمة وأيقونة
    """

    clicked = pyqtSignal()

    def __init__(self, title, value, icon_name=None, color=None, parent=None, module_name=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.icon_name = icon_name
        self.module_name = module_name
        self.color = color or get_ui_color('card', 'dark')

        # إعداد تأثير الظل
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(15)
        self.shadow.setColor(QColor(0, 0, 0, 80))
        self.shadow.setOffset(0, 2)
        self.setGraphicsEffect(self.shadow)

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين خصائص البطاقة
        self.setFrameShape(QFrame.StyledPanel)
        self.setMinimumSize(200, 120)
        self.setCursor(Qt.PointingHandCursor)

        # تعيين نمط البطاقة
        self.setStyleSheet(f"""
            QFrame {{
                background-color: {self.color};
                border-radius: 12px;
                border: none;
            }}
            QFrame:hover {{
                border: 2px solid white;
            }}
            QLabel {{
                background-color: transparent;
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # العنوان والأيقونة
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)

        # العنوان
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            font-weight: bold;
            color: white;
        """)

        # الأيقونة
        if self.icon_name:
            icon_label = QLabel()
            icon = qta.icon(self.icon_name, color='white')
            pixmap = icon.pixmap(24, 24)
            icon_label.setPixmap(pixmap)

            # وضع العناصر حسب اتجاه اللغة
            if tr.get_direction() == 'rtl':
                header_layout.addWidget(title_label)
                header_layout.addStretch()
                header_layout.addWidget(icon_label)
            else:
                header_layout.addWidget(icon_label)
                header_layout.addStretch()
                header_layout.addWidget(title_label)
        else:
            header_layout.addWidget(title_label)

        layout.addLayout(header_layout)

        # القيمة
        value_label = QLabel(str(self.value))
        value_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: white;
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

    def mousePressEvent(self, event):
        """معالجة حدث الضغط بالماوس"""
        super().mousePressEvent(event)
        self.clicked.emit()

class DashboardView(QWidget):
    """
    واجهة لوحة التحكم الرئيسية
    """

    # إشارات للتنقل بين الوحدات
    module_selected = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # محتوى منطقة التمرير
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)

        # العنوان
        header_layout = QHBoxLayout()

        welcome_label = QLabel(tr.get_text("dashboard_welcome", "مرحباً بك في لوحة التحكم"))
        welcome_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        header_layout.addWidget(welcome_label)

        date_label = QLabel(datetime.now().strftime("%Y-%m-%d"))
        date_label.setStyleSheet(f"""
            font-size: {get_font_size('normal')};
            color: {get_ui_color('text_secondary', 'dark')};
        """)
        date_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        header_layout.addWidget(date_label)

        scroll_layout.addLayout(header_layout)

        # بطاقات الإحصائيات
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(15)

        # سيتم إضافة بطاقات الإحصائيات لاحقاً في دالة load_data
        self.stats_layout = stats_layout
        scroll_layout.addLayout(stats_layout)

        # عنوان الوحدات
        modules_header = QLabel(tr.get_text("modules", "الوحدات"))
        modules_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        scroll_layout.addWidget(modules_header)

        # بطاقات الوحدات
        modules_layout = QHBoxLayout()
        modules_layout.setSpacing(15)

        # الصف الأول من الوحدات
        self.create_module_cards(modules_layout)
        scroll_layout.addLayout(modules_layout)

        # عنوان التقارير
        reports_header = QLabel(tr.get_text("reports", "التقارير"))
        reports_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        scroll_layout.addWidget(reports_header)

        # بطاقات التقارير
        reports_layout = QHBoxLayout()
        reports_layout.setSpacing(15)

        # إنشاء بطاقات التقارير
        self.create_report_cards(reports_layout)
        scroll_layout.addLayout(reports_layout)

        # عنوان الرسوم البيانية
        charts_header = QLabel(tr.get_text("analytics", "التحليلات"))
        charts_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            font-weight: bold;
            color: {get_ui_color('text', 'dark')};
        """)
        scroll_layout.addWidget(charts_header)

        # منطقة الرسوم البيانية
        charts_layout = QHBoxLayout()
        charts_layout.setSpacing(15)

        # سيتم إضافة الرسوم البيانية لاحقاً في دالة load_data
        self.charts_layout = charts_layout
        scroll_layout.addLayout(charts_layout)

        # إضافة مساحة متمددة في النهاية
        scroll_layout.addStretch()

        # تعيين المحتوى لمنطقة التمرير
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

    def create_module_cards(self, layout):
        """إنشاء بطاقات الوحدات مع الألوان المخصصة"""
        modules = [
            ("inventory", tr.get_text("inventory", "المخزون"), "fa.cubes"),
            ("treasury", tr.get_text("treasury", "الخزينة"), "fa.money"),
            ("invoices", tr.get_text("invoices", "الفواتير"), "fa.file-text"),
            ("definitions", tr.get_text("definitions", "التعاريف الأساسية"), "fa.cogs")
        ]

        for module_name, title, icon_name in modules:
            card = ModuleCard(title, icon_name, module_name)
            card.clicked.connect(lambda m=module_name: self.module_selected.emit(m))
            layout.addWidget(card)

    def create_report_cards(self, layout):
        """إنشاء بطاقات التقارير مع الألوان المخصصة"""
        reports = [
            ("sales_report", tr.get_text("sales_report", "تقرير المبيعات اليومي"), "fa.chart-line"),
            ("expenses_report", tr.get_text("expenses_report", "تقرير المصروفات اليومي"), "fa.chart-bar"),
            ("treasury_report", tr.get_text("treasury_report", "تقرير الخزينة اليومي"), "fa.university"),
            ("chat", tr.get_text("chat", "الدردشة"), "fa.comments")
        ]

        for report_name, title, icon_name in reports:
            card = ModuleCard(title, icon_name, report_name)
            card.clicked.connect(lambda r=report_name: self.module_selected.emit(r))
            layout.addWidget(card)

    def load_data(self):
        """تحميل البيانات وعرضها في لوحة التحكم"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # الحصول على تاريخ اليوم
            today = datetime.now().date()
            start_of_month = today.replace(day=1)

            # إحصائيات المبيعات اليومية
            sales_today_count = db.query(Invoice).filter(
                Invoice.invoice_date == today,
                Invoice.invoice_type == 'sales'
            ).count()

            # إجمالي مبيعات اليوم
            sales_today_total = db.query(Invoice).filter(
                Invoice.invoice_date == today,
                Invoice.invoice_type == 'sales'
            ).with_entities(Invoice.total_amount).all()
            sales_today_amount = sum([sale[0] or 0 for sale in sales_today_total])

            # إحصائيات المشتريات اليومية
            purchases_today_count = db.query(Invoice).filter(
                Invoice.invoice_date == today,
                Invoice.invoice_type == 'purchase'
            ).count()

            # إجمالي مشتريات اليوم
            purchases_today_total = db.query(Invoice).filter(
                Invoice.invoice_date == today,
                Invoice.invoice_type == 'purchase'
            ).with_entities(Invoice.total_amount).all()
            purchases_today_amount = sum([purchase[0] or 0 for purchase in purchases_today_total])

            # إحصائيات المنتجات
            products_count = db.query(Product).filter(
                Product.is_active == True
            ).count()

            # المنتجات منخفضة المخزون
            low_stock_count = db.query(Product).filter(
                Product.is_active == True,
                Product.quantity <= Product.min_quantity
            ).count()

            # إحصائيات العملاء
            customers_count = db.query(Customer).filter(
                Customer.is_active == True
            ).count()

            # إحصائيات المصروفات اليومية
            expenses_today = db.query(Expense).filter(
                Expense.expense_date == today
            ).with_entities(Expense.amount).all()
            expenses_today_amount = sum([expense[0] or 0 for expense in expenses_today])

            # إنشاء بطاقات الإحصائيات
            self.create_stat_cards(
                sales_today_count, sales_today_amount,
                purchases_today_count, purchases_today_amount,
                products_count, low_stock_count,
                customers_count, expenses_today_amount
            )

            # إنشاء الرسوم البيانية
            self.create_charts(db, today)

            log_info("تم تحميل بيانات لوحة التحكم")

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات لوحة التحكم: {str(e)}")

    def create_stat_cards(self, sales_count, sales_amount, purchases_count, purchases_amount,
                         products_count, low_stock_count, customers_count, expenses_amount):
        """إنشاء بطاقات الإحصائيات المحسنة"""
        # مسح البطاقات الحالية
        for i in reversed(range(self.stats_layout.count())):
            item = self.stats_layout.itemAt(i)
            if item.widget():
                item.widget().deleteLater()

        # تنسيق العملة
        currency = config.get_setting('default_currency', 'EGP')

        # إنشاء بطاقات الإحصائيات الجديدة مع الألوان المخصصة
        stats = [
            ("sales_today", tr.get_text("stat_sales_today", "مبيعات اليوم"),
             f"{sales_count} فاتورة\n{sales_amount:.2f} {currency}", "fa.shopping-cart", "sales_report"),
            ("expenses_today", tr.get_text("stat_expenses_today", "مصروفات اليوم"),
             f"{expenses_amount:.2f} {currency}", "fa.money", "expenses_report"),
            ("products_count", tr.get_text("stat_products_count", "المنتجات"),
             f"{products_count} منتج\n{low_stock_count} منخفض", "fa.cubes", "inventory"),
            ("customers_count", tr.get_text("stat_customers_count", "العملاء"),
             f"{customers_count} عميل", "fa.users", "definitions")
        ]

        for stat_id, title, value, icon_name, module_name in stats:
            card = StatCard(title, value, icon_name, get_module_color(module_name), module_name=module_name)
            self.stats_layout.addWidget(card)

    def create_charts(self, db, today):
        """إنشاء الرسوم البيانية"""
        try:
            # مسح الرسوم البيانية الحالية
            for i in reversed(range(self.charts_layout.count())):
                item = self.charts_layout.itemAt(i)
                if item.widget():
                    item.widget().deleteLater()

            # بيانات مبيعات الأسبوع الماضي
            week_ago = today - timedelta(days=7)
            sales_week_data = {}

            for i in range(7):
                day = week_ago + timedelta(days=i)
                day_sales = db.query(Invoice).filter(
                    Invoice.invoice_date == day,
                    Invoice.invoice_type == 'sales'
                ).with_entities(Invoice.total_amount).all()

                day_total = sum([sale[0] or 0 for sale in day_sales])
                day_name = day.strftime('%a')  # اختصار اسم اليوم
                sales_week_data[day_name] = day_total

            # إنشاء رسم بياني للمبيعات
            if any(sales_week_data.values()):
                sales_chart = create_sales_chart(sales_week_data)
                self.charts_layout.addWidget(sales_chart)

            # بيانات توزيع المصروفات حسب الفئة
            expenses_data = {}
            expense_categories = db.query(Expense).filter(
                Expense.expense_date >= today - timedelta(days=30)
            ).all()

            for expense in expense_categories:
                category = expense.category or tr.get_text("other", "أخرى")
                if category not in expenses_data:
                    expenses_data[category] = 0
                expenses_data[category] += expense.amount or 0

            # إنشاء رسم بياني دائري للمصروفات
            if any(expenses_data.values()):
                expenses_chart = create_expenses_pie_chart(expenses_data)
                self.charts_layout.addWidget(expenses_chart)

        except Exception as e:
            log_error(f"خطأ في إنشاء الرسوم البيانية: {str(e)}")
