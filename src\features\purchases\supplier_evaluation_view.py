#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة تقييم الموردين
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QPushButton, QComboBox, QDateEdit,
    QLineEdit, QLabel, QGroupBox, QFormLayout, QTextEdit, QSpinBox,
    QSlider, QProgressBar, QTabWidget
)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QBrush
import qtawesome as qta
from datetime import datetime, date, timedelta

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledComboBox, Styled<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
    StyledTable, StyledDateEdit, StyledTextEdit, StyledSpinBox
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.database import get_db
from src.models import Supplier, Invoice, InvoiceType, InvoiceStatus

class SupplierEvaluationView(QWidget):
    """واجهة تقييم الموردين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.setup_ui()
        self.load_evaluations()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("supplier_evaluation", "تقييم الموردين"))
        layout.addWidget(header)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.add_evaluation_btn = PrimaryButton(tr.get_text("add_evaluation", "إضافة تقييم"))
        self.add_evaluation_btn.setIcon(qta.icon("fa5s.star"))
        self.add_evaluation_btn.clicked.connect(self.add_evaluation)
        actions_layout.addWidget(self.add_evaluation_btn)
        
        self.view_details_btn = StyledButton(tr.get_text("view_details", "عرض التفاصيل"))
        self.view_details_btn.setIcon(qta.icon("fa5s.eye"))
        self.view_details_btn.clicked.connect(self.view_details)
        self.view_details_btn.setEnabled(False)
        actions_layout.addWidget(self.view_details_btn)
        
        self.generate_report_btn = SecondaryButton(tr.get_text("generate_report", "إنشاء تقرير"))
        self.generate_report_btn.setIcon(qta.icon("fa5s.file-alt"))
        self.generate_report_btn.clicked.connect(self.generate_report)
        actions_layout.addWidget(self.generate_report_btn)
        
        actions_layout.addStretch()
        
        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(qta.icon("fa5s.sync"))
        self.refresh_btn.clicked.connect(self.load_evaluations)
        actions_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(actions_layout)
        
        # فلاتر البحث
        filters_group = QGroupBox(tr.get_text("filters", "الفلاتر"))
        filters_layout = QHBoxLayout(filters_group)
        
        # فلتر المورد
        filters_layout.addWidget(StyledLabel(tr.get_text("supplier", "المورد")))
        self.supplier_filter = StyledComboBox()
        self.load_suppliers_filter()
        self.supplier_filter.currentTextChanged.connect(self.filter_evaluations)
        filters_layout.addWidget(self.supplier_filter)
        
        # فلتر التقييم
        filters_layout.addWidget(StyledLabel(tr.get_text("rating", "التقييم")))
        self.rating_filter = StyledComboBox()
        self.rating_filter.addItems([
            tr.get_text("all_ratings", "جميع التقييمات"),
            tr.get_text("excellent", "ممتاز (5 نجوم)"),
            tr.get_text("very_good", "جيد جداً (4 نجوم)"),
            tr.get_text("good", "جيد (3 نجوم)"),
            tr.get_text("fair", "مقبول (2 نجوم)"),
            tr.get_text("poor", "ضعيف (1 نجمة)")
        ])
        self.rating_filter.currentTextChanged.connect(self.filter_evaluations)
        filters_layout.addWidget(self.rating_filter)
        
        # فلتر التاريخ
        filters_layout.addWidget(StyledLabel(tr.get_text("period", "الفترة")))
        self.period_filter = StyledComboBox()
        self.period_filter.addItems([
            tr.get_text("all_periods", "جميع الفترات"),
            tr.get_text("last_month", "الشهر الماضي"),
            tr.get_text("last_quarter", "الربع الماضي"),
            tr.get_text("last_year", "السنة الماضية")
        ])
        self.period_filter.currentTextChanged.connect(self.filter_evaluations)
        filters_layout.addWidget(self.period_filter)
        
        filters_layout.addStretch()
        
        layout.addWidget(filters_group)
        
        # جدول التقييمات
        self.evaluations_table = StyledTable()
        self.evaluations_table.setColumnCount(9)
        self.evaluations_table.setHorizontalHeaderLabels([
            tr.get_text("supplier", "المورد"),
            tr.get_text("overall_rating", "التقييم العام"),
            tr.get_text("quality_rating", "جودة المنتج"),
            tr.get_text("delivery_rating", "التسليم"),
            tr.get_text("price_rating", "السعر"),
            tr.get_text("service_rating", "الخدمة"),
            tr.get_text("evaluation_date", "تاريخ التقييم"),
            tr.get_text("evaluator", "المقيم"),
            tr.get_text("notes", "ملاحظات")
        ])
        
        # تعيين خصائص الجدول
        header = self.evaluations_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(8, QHeaderView.Stretch)
        
        self.evaluations_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.evaluations_table.setSelectionMode(QTableWidget.SingleSelection)
        self.evaluations_table.setAlternatingRowColors(True)
        self.evaluations_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.evaluations_table.doubleClicked.connect(self.view_details)
        
        layout.addWidget(self.evaluations_table)
        
        # إحصائيات سريعة
        stats_group = QGroupBox(tr.get_text("quick_stats", "إحصائيات سريعة"))
        stats_layout = QHBoxLayout(stats_group)
        
        # متوسط التقييم العام
        self.avg_rating_label = StyledLabel("متوسط التقييم العام: --")
        stats_layout.addWidget(self.avg_rating_label)
        
        # عدد الموردين المقيمين
        self.evaluated_suppliers_label = StyledLabel("الموردين المقيمين: --")
        stats_layout.addWidget(self.evaluated_suppliers_label)
        
        # أفضل مورد
        self.best_supplier_label = StyledLabel("أفضل مورد: --")
        stats_layout.addWidget(self.best_supplier_label)
        
        stats_layout.addStretch()
        
        layout.addWidget(stats_group)
        
    def load_suppliers_filter(self):
        """تحميل قائمة الموردين للفلتر"""
        try:
            self.supplier_filter.clear()
            self.supplier_filter.addItem(tr.get_text("all_suppliers", "جميع الموردين"), None)
            
            suppliers = self.db.query(Supplier).filter(Supplier.is_active == True).order_by(Supplier.name).all()
            for supplier in suppliers:
                self.supplier_filter.addItem(supplier.name, supplier.id)
                
        except Exception as e:
            log_error(f"خطأ في تحميل الموردين: {str(e)}")
    
    def load_evaluations(self):
        """تحميل تقييمات الموردين"""
        try:
            # TODO: تحميل التقييمات من قاعدة البيانات
            # هذا مثال تجريبي
            sample_evaluations = [
                {
                    'supplier': 'مورد الإلكترونيات المحدود',
                    'overall_rating': 4.5,
                    'quality_rating': 5.0,
                    'delivery_rating': 4.0,
                    'price_rating': 4.0,
                    'service_rating': 5.0,
                    'evaluation_date': '2024-01-15',
                    'evaluator': 'أحمد محمد',
                    'notes': 'مورد ممتاز، جودة عالية وخدمة ممتازة'
                },
                {
                    'supplier': 'شركة المواد الغذائية',
                    'overall_rating': 3.5,
                    'quality_rating': 4.0,
                    'delivery_rating': 3.0,
                    'price_rating': 4.0,
                    'service_rating': 3.0,
                    'evaluation_date': '2024-01-10',
                    'evaluator': 'فاطمة أحمد',
                    'notes': 'جودة جيدة لكن التسليم يحتاج تحسين'
                },
                {
                    'supplier': 'مورد الأثاث العصري',
                    'overall_rating': 4.8,
                    'quality_rating': 5.0,
                    'delivery_rating': 5.0,
                    'price_rating': 4.5,
                    'service_rating': 4.5,
                    'evaluation_date': '2024-01-08',
                    'evaluator': 'محمد علي',
                    'notes': 'مورد ممتاز في جميع النواحي'
                }
            ]
            
            self.evaluations_table.setRowCount(len(sample_evaluations))
            
            total_rating = 0
            for row, evaluation in enumerate(sample_evaluations):
                # المورد
                supplier_item = QTableWidgetItem(evaluation['supplier'])
                supplier_item.setFlags(supplier_item.flags() & ~Qt.ItemIsEditable)
                self.evaluations_table.setItem(row, 0, supplier_item)
                
                # التقييم العام
                overall_rating = evaluation['overall_rating']
                rating_item = QTableWidgetItem(self.format_rating(overall_rating))
                rating_item.setFlags(rating_item.flags() & ~Qt.ItemIsEditable)
                rating_item.setTextAlignment(Qt.AlignCenter)
                
                # تلوين حسب التقييم
                if overall_rating >= 4.5:
                    rating_item.setBackground(QBrush(QColor(40, 167, 69, 50)))  # أخضر
                elif overall_rating >= 3.5:
                    rating_item.setBackground(QBrush(QColor(255, 193, 7, 50)))  # أصفر
                else:
                    rating_item.setBackground(QBrush(QColor(220, 53, 69, 50)))  # أحمر
                    
                self.evaluations_table.setItem(row, 1, rating_item)
                
                # تقييم الجودة
                quality_item = QTableWidgetItem(self.format_rating(evaluation['quality_rating']))
                quality_item.setFlags(quality_item.flags() & ~Qt.ItemIsEditable)
                quality_item.setTextAlignment(Qt.AlignCenter)
                self.evaluations_table.setItem(row, 2, quality_item)
                
                # تقييم التسليم
                delivery_item = QTableWidgetItem(self.format_rating(evaluation['delivery_rating']))
                delivery_item.setFlags(delivery_item.flags() & ~Qt.ItemIsEditable)
                delivery_item.setTextAlignment(Qt.AlignCenter)
                self.evaluations_table.setItem(row, 3, delivery_item)
                
                # تقييم السعر
                price_item = QTableWidgetItem(self.format_rating(evaluation['price_rating']))
                price_item.setFlags(price_item.flags() & ~Qt.ItemIsEditable)
                price_item.setTextAlignment(Qt.AlignCenter)
                self.evaluations_table.setItem(row, 4, price_item)
                
                # تقييم الخدمة
                service_item = QTableWidgetItem(self.format_rating(evaluation['service_rating']))
                service_item.setFlags(service_item.flags() & ~Qt.ItemIsEditable)
                service_item.setTextAlignment(Qt.AlignCenter)
                self.evaluations_table.setItem(row, 5, service_item)
                
                # تاريخ التقييم
                date_item = QTableWidgetItem(evaluation['evaluation_date'])
                date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
                self.evaluations_table.setItem(row, 6, date_item)
                
                # المقيم
                evaluator_item = QTableWidgetItem(evaluation['evaluator'])
                evaluator_item.setFlags(evaluator_item.flags() & ~Qt.ItemIsEditable)
                self.evaluations_table.setItem(row, 7, evaluator_item)
                
                # الملاحظات
                notes_item = QTableWidgetItem(evaluation['notes'])
                notes_item.setFlags(notes_item.flags() & ~Qt.ItemIsEditable)
                self.evaluations_table.setItem(row, 8, notes_item)
                
                total_rating += overall_rating
            
            # تحديث الإحصائيات
            if sample_evaluations:
                avg_rating = total_rating / len(sample_evaluations)
                self.avg_rating_label.setText(f"متوسط التقييم العام: {self.format_rating(avg_rating)}")
                self.evaluated_suppliers_label.setText(f"الموردين المقيمين: {len(sample_evaluations)}")
                
                # أفضل مورد
                best_evaluation = max(sample_evaluations, key=lambda x: x['overall_rating'])
                self.best_supplier_label.setText(f"أفضل مورد: {best_evaluation['supplier']}")
                
        except Exception as e:
            log_error(f"خطأ في تحميل التقييمات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_evaluations", "حدث خطأ أثناء تحميل التقييمات")
            )
    
    def format_rating(self, rating):
        """تنسيق التقييم مع النجوم"""
        stars = "★" * int(rating) + "☆" * (5 - int(rating))
        return f"{rating:.1f} {stars}"
    
    def filter_evaluations(self):
        """فلترة التقييمات"""
        # TODO: تنفيذ فلترة التقييمات
        pass
    
    def on_selection_changed(self):
        """عند تغيير التحديد"""
        selected_rows = self.evaluations_table.selectionModel().selectedRows()
        has_selection = len(selected_rows) > 0
        
        self.view_details_btn.setEnabled(has_selection)
    
    def add_evaluation(self):
        """إضافة تقييم جديد"""
        dialog = SupplierEvaluationDialog(self)
        if dialog.exec_() == dialog.Accepted:
            self.load_evaluations()
    
    def view_details(self):
        """عرض تفاصيل التقييم"""
        selected_rows = self.evaluations_table.selectionModel().selectedRows()
        if not selected_rows:
            return
            
        # TODO: تنفيذ عرض تفاصيل التقييم
        QMessageBox.information(
            self,
            tr.get_text("info", "معلومات"),
            tr.get_text("feature_coming_soon", "هذه الميزة قيد التطوير")
        )
    
    def generate_report(self):
        """إنشاء تقرير التقييمات"""
        # TODO: تنفيذ إنشاء تقرير التقييمات
        QMessageBox.information(
            self,
            tr.get_text("info", "معلومات"),
            tr.get_text("feature_coming_soon", "هذه الميزة قيد التطوير")
        )

class SupplierEvaluationDialog(QWidget):
    """نافذة إضافة تقييم مورد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(tr.get_text("add_supplier_evaluation", "إضافة تقييم مورد"))
        self.setMinimumSize(600, 500)
        self.db = next(get_db())
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("add_supplier_evaluation", "إضافة تقييم مورد"))
        layout.addWidget(header)
        
        # نموذج البيانات الأساسية
        form_group = QGroupBox(tr.get_text("basic_info", "المعلومات الأساسية"))
        form_layout = QFormLayout(form_group)
        
        # المورد
        self.supplier_combo = StyledComboBox()
        self.load_suppliers()
        form_layout.addRow(StyledLabel(tr.get_text("supplier", "المورد")), self.supplier_combo)
        
        # تاريخ التقييم
        self.evaluation_date = StyledDateEdit()
        self.evaluation_date.setDate(QDate.currentDate())
        form_layout.addRow(StyledLabel(tr.get_text("evaluation_date", "تاريخ التقييم")), self.evaluation_date)
        
        layout.addWidget(form_group)
        
        # معايير التقييم
        criteria_group = QGroupBox(tr.get_text("evaluation_criteria", "معايير التقييم"))
        criteria_layout = QFormLayout(criteria_group)
        
        # جودة المنتج
        self.quality_slider = self.create_rating_slider()
        criteria_layout.addRow(StyledLabel(tr.get_text("product_quality", "جودة المنتج")), self.quality_slider)
        
        # التسليم في الوقت المحدد
        self.delivery_slider = self.create_rating_slider()
        criteria_layout.addRow(StyledLabel(tr.get_text("delivery_time", "التسليم في الوقت")), self.delivery_slider)
        
        # تنافسية الأسعار
        self.price_slider = self.create_rating_slider()
        criteria_layout.addRow(StyledLabel(tr.get_text("price_competitiveness", "تنافسية الأسعار")), self.price_slider)
        
        # جودة الخدمة
        self.service_slider = self.create_rating_slider()
        criteria_layout.addRow(StyledLabel(tr.get_text("service_quality", "جودة الخدمة")), self.service_slider)
        
        layout.addWidget(criteria_group)
        
        # الملاحظات
        notes_group = QGroupBox(tr.get_text("notes", "ملاحظات"))
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_input = StyledTextEdit()
        self.notes_input.setPlaceholderText(tr.get_text("evaluation_notes_placeholder", "أدخل ملاحظاتك حول أداء المورد..."))
        notes_layout.addWidget(self.notes_input)
        
        layout.addWidget(notes_group)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.clicked.connect(self.close)
        buttons_layout.addWidget(cancel_btn)
        
        buttons_layout.addStretch()
        
        save_btn = PrimaryButton(tr.get_text("save_evaluation", "حفظ التقييم"))
        save_btn.clicked.connect(self.save_evaluation)
        buttons_layout.addWidget(save_btn)
        
        layout.addLayout(buttons_layout)
    
    def create_rating_slider(self):
        """إنشاء شريط تمرير للتقييم"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(1)
        slider.setMaximum(5)
        slider.setValue(3)
        slider.setTickPosition(QSlider.TicksBelow)
        slider.setTickInterval(1)
        
        value_label = StyledLabel("3")
        value_label.setMinimumWidth(20)
        
        stars_label = StyledLabel("★★★☆☆")
        
        slider.valueChanged.connect(lambda v: value_label.setText(str(v)))
        slider.valueChanged.connect(lambda v: stars_label.setText("★" * v + "☆" * (5 - v)))
        
        layout.addWidget(slider)
        layout.addWidget(value_label)
        layout.addWidget(stars_label)
        
        # حفظ مرجع للشريط للوصول إليه لاحقاً
        container.slider = slider
        
        return container
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db.query(Supplier).filter(Supplier.is_active == True).order_by(Supplier.name).all()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
                
        except Exception as e:
            log_error(f"خطأ في تحميل الموردين: {str(e)}")
    
    def save_evaluation(self):
        """حفظ التقييم"""
        try:
            # التحقق من البيانات
            if self.supplier_combo.currentData() is None:
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("supplier_required", "يجب اختيار مورد")
                )
                return
            
            # TODO: حفظ التقييم في قاعدة البيانات
            supplier_id = self.supplier_combo.currentData()
            quality_rating = self.quality_slider.slider.value()
            delivery_rating = self.delivery_slider.slider.value()
            price_rating = self.price_slider.slider.value()
            service_rating = self.service_slider.slider.value()
            
            overall_rating = (quality_rating + delivery_rating + price_rating + service_rating) / 4
            
            log_info(f"تم حفظ تقييم المورد {supplier_id}: {overall_rating:.1f}")
            
            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("evaluation_saved", "تم حفظ التقييم بنجاح")
            )
            
            self.close()
            
        except Exception as e:
            log_error(f"خطأ في حفظ التقييم: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_evaluation", "حدث خطأ أثناء حفظ التقييم")
            )
