#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تشغيل مبسط لبرنامج أمين الحسابات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    print("🚀 بدء تشغيل أمين الحسابات...")

    # تهيئة قاعدة البيانات
    print("📊 تهيئة قاعدة البيانات...")
    from src.database import init_db
    init_db()
    print("✅ تم تهيئة قاعدة البيانات")

    # تشغيل التطبيق
    print("🖥️ تشغيل واجهة المستخدم...")
    from PyQt5.QtWidgets import QApplication
    from src.ui.windows.main_window import MainWindow

    app = QApplication(sys.argv)
    app.setApplicationName("أمين الحسابات")
    app.setApplicationVersion("2.0.0")

    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()

    print("✅ تم تشغيل البرنامج بنجاح!")

    # تشغيل حلقة الأحداث
    sys.exit(app.exec_())

except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {str(e)}")
    print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
    sys.exit(1)

except Exception as e:
    print(f"❌ خطأ عام: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
