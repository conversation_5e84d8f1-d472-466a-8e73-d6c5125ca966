#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from src.models.base_models import BaseModel, TimestampMixin, SoftDeleteMixin
from src.models.user import User
from src.models.product import Product, ProductStatus
from src.models.supplier import Supplier
from src.models.customer import Customer
from src.models.invoice import Invoice, InvoiceItem, InvoiceType, InvoiceStatus, PaymentMethod
from src.models.expense import Expense, PaymentStatus
from src.models.income import Income, IncomeStatus
from src.models.payment import Payment, PaymentType, PaymentMethod
from src.models.external_company import ExternalCompany, ExternalTransaction
from src.models.employee import Employee, Department, Position, Attendance, SalaryPayment, EmploymentStatus
from src.models.category import (
    ProductCategory, ExpenseCategory,
    CustomerCategory, SupplierCategory
)
from src.models.inventory import (
    InventoryMovement, MovementType,
    Warehouse, WarehouseInventory
)

__all__ = [
    # نماذج أساسية
    'BaseModel',
    'TimestampMixin',
    'SoftDeleteMixin',

    # المستخدمين
    'User',

    # المنتجات والمخزون
    'Product',
    'ProductStatus',
    'ProductCategory',
    'InventoryMovement',
    'MovementType',
    'Warehouse',
    'WarehouseInventory',

    # الموردين والعملاء
    'Supplier',
    'Customer',
    'CustomerCategory',
    'SupplierCategory',

    # الفواتير والمبيعات
    'Invoice',
    'InvoiceItem',
    'InvoiceType',
    'InvoiceStatus',

    # المصروفات والإيرادات
    'Expense',
    'PaymentStatus',
    'Income',
    'IncomeStatus',
    'ExpenseCategory',

    # المدفوعات
    'Payment',
    'PaymentType',
    'PaymentMethod',

    # الشركات الخارجية
    'ExternalCompany',
    'ExternalTransaction',

    # الموظفين
    'Employee',
    'Department',
    'Position',
    'Attendance',
    'SalaryPayment',
    'EmploymentStatus'
]