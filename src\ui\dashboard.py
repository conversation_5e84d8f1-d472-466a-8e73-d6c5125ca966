#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
لوحة المعلومات الرئيسية للتطبيق
"""

import os
import datetime
from typing import Dict, Any, List, Optional

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QFrame, QPushButton, QScrollArea, QSplitter, QTabWidget,
    QStackedWidget, QToolBar, QAction, QMenu, QStatusBar,
    QSizePolicy, QSpacerItem, QGraphicsDropShadowEffect, QGridLayout
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QTimer, QPoint
from PyQt5.QtGui import QIcon, QPixmap, QFont, QColor, QCursor

import qtawesome as qta

from src.ui.widgets.dashboard_widgets import (
    DashboardCard, UserProfileWidget, StatisticsWidget,
    NotificationsWidget, QuickAccessMenu
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, HeaderLabel
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.utils import config
from src.models import User
from src.ui.styles.theme_colors import get_module_color, get_ui_color, get_font_size
from src.ui.styles.theme_manager import ThemeManager

class Dashboard(QMainWindow):
    """لوحة المعلومات الرئيسية"""

    module_selected = pyqtSignal(str)  # إشارة عند اختيار وحدة

    def __init__(self, user: User, parent=None):
        super().__init__(parent)
        self.user = user
        self.theme_manager = ThemeManager.get_instance()
        self.setup_ui()
        self.setup_connections()
        self.load_data()

        # تحديث الوقت كل دقيقة
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_time)
        self.timer.start(60000)  # كل دقيقة

        # تحديث الوقت الأولي
        self.update_time()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("dashboard", "لوحة المعلومات"))
        self.setMinimumSize(1200, 700)

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # شريط الأدوات العلوي
        self.setup_toolbar()

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Horizontal)

        # القائمة الجانبية
        sidebar = self.create_sidebar()
        main_splitter.addWidget(sidebar)

        # منطقة المحتوى
        content_area = self.create_content_area()
        main_splitter.addWidget(content_area)

        # تعيين نسب المقسم
        main_splitter.setSizes([250, 950])
        main_splitter.setStretchFactor(0, 0)  # القائمة الجانبية لا تتمدد
        main_splitter.setStretchFactor(1, 1)  # منطقة المحتوى تتمدد

        main_layout.addWidget(main_splitter)

        # شريط الحالة
        self.setup_statusbar()

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        # إنشاء شريط الأدوات
        toolbar = QToolBar("شريط الأدوات الرئيسي")
        toolbar.setMovable(False)
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        toolbar.setStyleSheet(f"""
            QToolBar {{
                background-color: {get_ui_color('toolbar', 'dark')};
                border: none;
                spacing: 10px;
                padding: 5px;
            }}

            QToolButton {{
                color: {get_ui_color('text', 'dark')};
                background-color: transparent;
                border: none;
                padding: 5px 10px;
                border-radius: 5px;
            }}

            QToolButton:hover {{
                background-color: {get_ui_color('hover', 'dark')};
            }}

            QToolButton:checked {{
                background-color: {get_ui_color('selected', 'dark')};
                color: {get_ui_color('highlight', 'dark')};
            }}
        """)

        # إضافة شريط الأدوات
        self.addToolBar(toolbar)

        # إضافة الشعار
        logo_label = QLabel()
        logo_pixmap = QPixmap(os.path.join("resources", "images", "logo.png"))
        if not logo_pixmap.isNull():
            logo_pixmap = logo_pixmap.scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_label.setPixmap(logo_pixmap)
        else:
            # استخدام أيقونة بديلة
            logo_label.setPixmap(qta.icon("fa5s.calculator", color=get_ui_color('highlight', 'dark')).pixmap(32, 32))

        toolbar.addWidget(logo_label)

        # إضافة اسم التطبيق
        app_name_label = QLabel("أمين الحسابات")
        app_name_label.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            font-weight: bold;
            color: {get_ui_color('highlight', 'dark')};
            padding: 0 10px;
        """)
        toolbar.addWidget(app_name_label)

        # إضافة فاصل
        toolbar.addSeparator()

        # إضافة أزرار الوحدات
        self.module_actions = {}

        modules = [
            ("sales", "shopping-cart", tr.get_text("sales", "المبيعات")),
            ("purchases", "truck", tr.get_text("purchases", "المشتريات")),
            ("inventory", "boxes", tr.get_text("inventory", "المخزون")),
            ("customers", "users", tr.get_text("customers", "العملاء")),
            ("suppliers", "handshake", tr.get_text("suppliers", "الموردين")),
            ("expenses", "money-bill", tr.get_text("expenses", "المصروفات")),
            ("reports", "chart-bar", tr.get_text("reports", "التقارير"))
        ]

        for module_id, icon_name, module_name in modules:
            action = QAction(qta.icon(f"fa5s.{icon_name}", color=get_ui_color('text', 'dark')), module_name, self)
            action.setCheckable(True)
            action.setData(module_id)
            action.triggered.connect(self.on_module_selected)
            toolbar.addAction(action)
            self.module_actions[module_id] = action

        # إضافة فاصل مرن
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(spacer)

        # إضافة أزرار إضافية
        settings_action = QAction(qta.icon("fa5s.cog", color=get_ui_color('text', 'dark')), tr.get_text("settings", "الإعدادات"), self)
        settings_action.triggered.connect(self.on_settings_clicked)
        toolbar.addAction(settings_action)

        help_action = QAction(qta.icon("fa5s.question-circle", color=get_ui_color('text', 'dark')), tr.get_text("help", "المساعدة"), self)
        help_action.triggered.connect(self.on_help_clicked)
        toolbar.addAction(help_action)

        # إضافة زر الملف الشخصي
        profile_action = QAction(qta.icon("fa5s.user-circle", color=get_ui_color('text', 'dark')), self.user.full_name, self)
        profile_action.triggered.connect(self.on_profile_clicked)
        toolbar.addAction(profile_action)

    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        # إنشاء شريط الحالة
        statusbar = QStatusBar()
        statusbar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {get_ui_color('statusbar', 'dark')};
                color: {get_ui_color('text', 'dark')};
                border-top: 1px solid {get_ui_color('border', 'dark')};
            }}
        """)
        self.setStatusBar(statusbar)

        # إضافة معلومات المستخدم
        user_label = QLabel(f"{tr.get_text('user', 'المستخدم')}: {self.user.full_name}")
        statusbar.addPermanentWidget(user_label)

        # إضافة الوقت والتاريخ
        self.time_label = QLabel()
        statusbar.addPermanentWidget(self.time_label)

        # إضافة إصدار التطبيق
        version = config.get_setting("app_version", "1.0.0")
        version_label = QLabel(f"{tr.get_text('version', 'الإصدار')}: {version}")
        statusbar.addPermanentWidget(version_label)

    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        # إنشاء إطار القائمة الجانبية
        sidebar = QFrame()
        sidebar.setFrameShape(QFrame.StyledPanel)
        sidebar.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('sidebar', 'dark')};
                border-right: 1px solid {get_ui_color('border', 'dark')};
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # ويدجت معلومات المستخدم
        user_profile = UserProfileWidget(self.user)
        layout.addWidget(user_profile)

        # قائمة الوصول السريع
        quick_access = QuickAccessMenu()
        layout.addWidget(quick_access)

        # ويدجت الإشعارات
        notifications = NotificationsWidget()
        layout.addWidget(notifications)

        # إضافة فراغ في النهاية
        layout.addStretch()

        return sidebar

    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        # إنشاء إطار المحتوى
        content = QFrame()
        content.setFrameShape(QFrame.StyledPanel)
        content.setStyleSheet(f"""
            QFrame {{
                background-color: {get_ui_color('background', 'dark')};
            }}
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(content)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # العنوان
        header = HeaderLabel(tr.get_text("dashboard", "لوحة المعلومات"))
        header.setStyleSheet(f"""
            font-size: {get_font_size('header')};
            color: {get_ui_color('text', 'dark')};
        """)
        layout.addWidget(header)

        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }

            QScrollBar:vertical {
                border: none;
                background-color: #2d3436;
                width: 10px;
                margin: 0px;
            }

            QScrollBar::handle:vertical {
                background-color: #636e72;
                min-height: 20px;
                border-radius: 5px;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }

            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """)

        # محتوى منطقة التمرير
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(30)

        # إضافة ويدجت الإحصائيات
        statistics = StatisticsWidget()
        scroll_layout.addWidget(statistics)

        # إضافة عنوان قسم البطاقات
        cards_header = HeaderLabel(tr.get_text("modules", "الوحدات الرئيسية"))
        cards_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            color: {get_ui_color('text', 'dark')};
            margin-top: 10px;
        """)
        scroll_layout.addWidget(cards_header)

        # استخدام Grid Layout للبطاقات لتحسين التوزيع
        cards_grid = QGridLayout()
        cards_grid.setSpacing(25)

        # تعريف البطاقات مع الألوان المطلوبة
        cards_data = [
            # الصف الأول
            {
                "title": tr.get_text("inventory_summary", "ملخص المخزون"),
                "icon": "fa5s.boxes",
                "value": "543",
                "module_name": "inventory",
                "color": get_module_color("inventory"),  # أحمر
                "position": (0, 0)
            },
            {
                "title": tr.get_text("treasury_summary", "ملخص الخزينة"),
                "icon": "fa5s.money-check-alt",
                "value": "15,678",
                "module_name": "treasury",
                "color": get_module_color("treasury"),  # بنفسجي
                "position": (0, 1)
            },
            {
                "title": tr.get_text("invoices_summary", "ملخص الفواتير"),
                "icon": "fa5s.file-invoice-dollar",
                "value": "324",
                "module_name": "invoices",
                "color": get_module_color("invoices"),  # أزرق
                "position": (0, 2)
            },
            # الصف الثاني
            {
                "title": tr.get_text("definitions_summary", "التعاريف الأساسية"),
                "icon": "fa5s.cogs",
                "value": "45",
                "module_name": "definitions",
                "color": get_module_color("definitions"),  # أخضر
                "position": (1, 0)
            },
            {
                "title": tr.get_text("sales_report_summary", "تقرير المبيعات اليومي"),
                "icon": "fa5s.chart-line",
                "value": "12,345",
                "module_name": "sales",
                "color": get_module_color("sales_report"),  # أصفر
                "position": (1, 1)
            },
            {
                "title": tr.get_text("expenses_report_summary", "تقرير المصروفات اليومي"),
                "icon": "fa5s.money-bill",
                "value": "4,321",
                "module_name": "expenses",
                "color": get_module_color("expenses_report"),  # أخضر فاتح
                "position": (1, 2)
            },
            # الصف الثالث
            {
                "title": tr.get_text("treasury_report_summary", "تقرير الخزينة اليومي"),
                "icon": "fa5s.cash-register",
                "value": "9,876",
                "module_name": "treasury_report",
                "color": get_module_color("treasury_report"),  # أحمر غامق
                "position": (2, 0)
            },
            {
                "title": tr.get_text("chat_summary", "الدردشة"),
                "icon": "fa5s.comments",
                "value": "18",
                "module_name": "chat",
                "color": get_module_color("chat"),  # برتقالي
                "position": (2, 1)
            },
            {
                "title": tr.get_text("recent_sales_summary", "الفواتير الأخيرة"),
                "icon": "fa5s.receipt",
                "value": "7",
                "module_name": "recent_sales",
                "color": get_module_color("recent_sales"),  # أبيض
                "position": (2, 2)
            }
        ]

        # إنشاء البطاقات وإضافتها إلى التخطيط
        for card_data in cards_data:
            card = DashboardCard(
                title=card_data["title"],
                icon=qta.icon(card_data["icon"]),
                value=card_data["value"],
                color=card_data["color"]
            )

            # إضافة تأثير ظل للبطاقة
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(0, 0, 0, 80))
            shadow.setOffset(0, 5)
            card.setGraphicsEffect(shadow)

            # ربط حدث النقر
            module_name = card_data["module_name"]
            card.clicked.connect(lambda checked=False, m=module_name: self.on_card_clicked(m))

            # إضافة البطاقة إلى التخطيط الشبكي
            row, col = card_data["position"]
            cards_grid.addWidget(card, row, col)

        scroll_layout.addLayout(cards_grid)

        # إضافة قسم الإحصائيات الإضافية
        additional_stats_header = HeaderLabel(tr.get_text("additional_stats", "إحصائيات إضافية"))
        additional_stats_header.setStyleSheet(f"""
            font-size: {get_font_size('subheader')};
            color: {get_ui_color('text', 'dark')};
            margin-top: 20px;
        """)
        scroll_layout.addWidget(additional_stats_header)

        # إضافة بطاقات إضافية (يمكن إضافة المزيد حسب الحاجة)
        additional_cards = QHBoxLayout()
        additional_cards.setSpacing(25)

        # بطاقة العملاء
        customers_card = DashboardCard(
            title=tr.get_text("customers_summary", "ملخص العملاء"),
            icon=qta.icon("fa5s.users"),
            value="210",
            color="#3498db"
        )
        customers_card.clicked.connect(lambda: self.on_card_clicked("customers"))

        # إضافة تأثير ظل
        shadow1 = QGraphicsDropShadowEffect()
        shadow1.setBlurRadius(15)
        shadow1.setColor(QColor(0, 0, 0, 80))
        shadow1.setOffset(0, 5)
        customers_card.setGraphicsEffect(shadow1)

        additional_cards.addWidget(customers_card)

        # بطاقة الموردين
        suppliers_card = DashboardCard(
            title=tr.get_text("suppliers_summary", "ملخص الموردين"),
            icon=qta.icon("fa5s.handshake"),
            value="87",
            color="#e67e22"
        )
        suppliers_card.clicked.connect(lambda: self.on_card_clicked("suppliers"))

        # إضافة تأثير ظل
        shadow2 = QGraphicsDropShadowEffect()
        shadow2.setBlurRadius(15)
        shadow2.setColor(QColor(0, 0, 0, 80))
        shadow2.setOffset(0, 5)
        suppliers_card.setGraphicsEffect(shadow2)

        additional_cards.addWidget(suppliers_card)

        # بطاقة الموظفين
        employees_card = DashboardCard(
            title=tr.get_text("employees_summary", "ملخص الموظفين"),
            icon=qta.icon("fa5s.id-card"),
            value="32",
            color="#9b59b6"
        )
        employees_card.clicked.connect(lambda: self.on_card_clicked("employees"))

        # إضافة تأثير ظل
        shadow3 = QGraphicsDropShadowEffect()
        shadow3.setBlurRadius(15)
        shadow3.setColor(QColor(0, 0, 0, 80))
        shadow3.setOffset(0, 5)
        employees_card.setGraphicsEffect(shadow3)

        additional_cards.addWidget(employees_card)

        scroll_layout.addLayout(additional_cards)

        # إضافة فراغ في النهاية
        scroll_layout.addStretch()

        scroll_area.setWidget(scroll_content)
        layout.addWidget(scroll_area)

        return content

    def setup_connections(self):
        """إعداد الاتصالات"""
        pass

    def load_data(self):
        """تحميل البيانات"""
        pass

    def update_time(self):
        """تحديث الوقت والتاريخ"""
        now = datetime.datetime.now()
        time_str = now.strftime("%Y-%m-%d %H:%M")
        self.time_label.setText(time_str)

    def on_module_selected(self):
        """حدث اختيار وحدة"""
        # الحصول على الإجراء المرسل
        action = self.sender()
        if not action:
            return

        # الحصول على معرف الوحدة
        module_id = action.data()

        # إلغاء تحديد جميع الإجراءات الأخرى
        for action_id, module_action in self.module_actions.items():
            if action_id != module_id:
                module_action.setChecked(False)

        # إرسال إشارة باختيار الوحدة
        self.module_selected.emit(module_id)

    def on_card_clicked(self, module_id):
        """حدث النقر على بطاقة"""
        # تحديد الوحدة المناسبة
        if module_id in self.module_actions:
            self.module_actions[module_id].trigger()

    def on_settings_clicked(self):
        """حدث النقر على زر الإعدادات"""
        # فتح نافذة الإعدادات
        from src.ui.dialogs.settings_dialog import SettingsDialog
        dialog = SettingsDialog(self)
        dialog.exec_()

    def on_help_clicked(self):
        """حدث النقر على زر المساعدة"""
        # فتح نافذة المساعدة
        from src.ui.dialogs.help_dialog import HelpDialog
        dialog = HelpDialog(self)
        dialog.exec_()

    def on_profile_clicked(self):
        """حدث النقر على زر الملف الشخصي"""
        # فتح قائمة الملف الشخصي
        menu = QMenu(self)

        # إضافة عناصر القائمة
        profile_action = QAction(qta.icon("fa5s.user", color=get_ui_color('text', 'dark')), tr.get_text("profile", "الملف الشخصي"), self)
        profile_action.triggered.connect(self.on_view_profile)
        menu.addAction(profile_action)

        menu.addSeparator()

        logout_action = QAction(qta.icon("fa5s.sign-out-alt", color=get_ui_color('text', 'dark')), tr.get_text("logout", "تسجيل الخروج"), self)
        logout_action.triggered.connect(self.on_logout)
        menu.addAction(logout_action)

        # عرض القائمة
        menu.exec_(QCursor.pos())

    def on_view_profile(self):
        """حدث عرض الملف الشخصي"""
        # فتح نافذة الملف الشخصي
        from src.ui.dialogs.profile_dialog import ProfileDialog
        dialog = ProfileDialog(self.user, self)
        dialog.exec_()

    def on_logout(self):
        """حدث تسجيل الخروج"""
        # تأكيد تسجيل الخروج
        reply = QMessageBox.question(
            self,
            tr.get_text("confirm_logout", "تأكيد تسجيل الخروج"),
            tr.get_text("confirm_logout_message", "هل أنت متأكد من تسجيل الخروج؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # إغلاق النافذة
            self.close()

            # إرسال إشارة بتسجيل الخروج
            from src.ui.login import LoginWindow
            login_window = LoginWindow()
            login_window.show()
