#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج مرتجعات المشتريات
"""

from PyQt5.QtWidgets import (
    QMessageBox, QTableWidgetItem
)
from PyQt5.QtCore import Qt, QDateTime
from PyQt5.QtGui import QIcon
import qtawesome as qta
from datetime import datetime
from sqlalchemy import desc
from sqlalchemy.exc import SQLAlchemyError

from src.database import get_db
from src.models import (
    Invoice, InvoiceItem, Supplier, Product,
    InvoiceStatus, PaymentMethod, InvoiceType
)
from src.ui.widgets.base_widgets import (
    StyledLabel, StyledComboBox, StyledDoubleSpinBox
)
from src.utils import (
    translation_manager as tr,
    log_info, log_error,
    config
)
from src.features.invoices.base_invoice_form import BaseInvoiceForm

class PurchaseReturnForm(BaseInvoiceForm):
    """
    نموذج إنشاء وتعديل مرتجعات المشتريات
    """

    def __init__(self, invoice_id=None, parent=None):
        """
        إنشاء نموذج مرتجعات المشتريات
        :param invoice_id: معرف الفاتورة (للتعديل)، أو None (للإنشاء)
        :param parent: العنصر الأب
        """
        # تهيئة المتغيرات الإضافية
        self.suppliers = []
        self.purchase_invoices = []

        # استدعاء المنشئ الأساسي
        super().__init__(invoice_id, parent, InvoiceType.PURCHASE_RETURN)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل الموردين النشطين
            self.suppliers = self.db.query(Supplier).filter(
                Supplier.is_active == True
            ).order_by(Supplier.name).all()

            # تحميل فواتير المشتريات (للاختيار منها)
            self.purchase_invoices = self.db.query(Invoice).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.is_deleted == False
            ).order_by(desc(Invoice.invoice_date)).all()

            # تحميل المنتجات النشطة
            self.products = self.db.query(Product).filter(
                Product.is_active == True
            ).order_by(Product.name).all()

            # تحميل الفاتورة إذا كنا في وضع التعديل
            if self.is_edit_mode:
                self.invoice = self.db.query(Invoice).filter(
                    Invoice.id == self.invoice_id,
                    Invoice.invoice_type == InvoiceType.PURCHASE_RETURN,
                    Invoice.is_deleted == False
                ).first()

                if not self.invoice:
                    raise ValueError(tr.get_text("error_invoice_not_found", "الفاتورة غير موجودة"))

                # تحميل عناصر الفاتورة
                self.invoice_items = self.db.query(InvoiceItem).filter(
                    InvoiceItem.invoice_id == self.invoice_id
                ).all()

        except Exception as e:
            log_error(f"خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def get_window_title(self):
        """الحصول على عنوان النافذة"""
        return tr.get_text("edit_return", "تعديل مرتجع") if self.is_edit_mode else tr.get_text("new_return", "مرتجع جديد")

    def get_header_title(self):
        """الحصول على عنوان الرأس"""
        return tr.get_text("edit_purchase_return", "تعديل مرتجع مشتريات") if self.is_edit_mode else tr.get_text("new_purchase_return", "مرتجع مشتريات جديد")

    def setup_entity_fields(self, layout):
        """إعداد حقول المورد وفاتورة المشتريات الأصلية"""
        # المورد
        layout.addWidget(StyledLabel(tr.get_text("supplier", "المورد")), 1, 0)
        self.supplier_combo = StyledComboBox()
        self.populate_suppliers()
        layout.addWidget(self.supplier_combo, 1, 1)

        # فاتورة المشتريات الأصلية
        layout.addWidget(StyledLabel(tr.get_text("original_invoice", "الفاتورة الأصلية")), 1, 2)
        self.original_invoice_combo = StyledComboBox()
        self.populate_original_invoices()
        self.original_invoice_combo.currentIndexChanged.connect(self.on_original_invoice_changed)
        layout.addWidget(self.original_invoice_combo, 1, 3)

    def populate_suppliers(self):
        """ملء قائمة الموردين"""
        self.supplier_combo.clear()

        # إضافة خيار فارغ
        self.supplier_combo.addItem("", None)

        # إضافة الموردين
        for supplier in self.suppliers:
            self.supplier_combo.addItem(supplier.name, supplier.id)

    def populate_original_invoices(self):
        """ملء قائمة فواتير المشتريات الأصلية"""
        self.original_invoice_combo.clear()

        # إضافة خيار فارغ
        self.original_invoice_combo.addItem("", None)

        # إضافة فواتير المشتريات
        for invoice in self.purchase_invoices:
            # عرض رقم الفاتورة والتاريخ
            invoice_text = f"{invoice.invoice_number} - {invoice.invoice_date.strftime('%Y-%m-%d')}"
            if invoice.supplier:
                invoice_text += f" - {invoice.supplier.name}"

            self.original_invoice_combo.addItem(invoice_text, invoice.id)

    def on_original_invoice_changed(self):
        """معالجة تغيير فاتورة المشتريات الأصلية"""
        invoice_id = self.original_invoice_combo.currentData()
        if not invoice_id:
            return

        try:
            # البحث عن الفاتورة
            invoice = self.db.query(Invoice).filter(
                Invoice.id == invoice_id,
                Invoice.invoice_type == InvoiceType.PURCHASE
            ).first()

            if invoice:
                # تعيين المورد
                if invoice.supplier_id:
                    supplier_index = self.supplier_combo.findData(invoice.supplier_id)
                    if supplier_index >= 0:
                        self.supplier_combo.setCurrentIndex(supplier_index)

                # تحميل عناصر الفاتورة
                invoice_items = self.db.query(InvoiceItem).filter(
                    InvoiceItem.invoice_id == invoice_id
                ).all()

                # مسح جدول المنتجات
                self.products_table.setRowCount(0)

                # إضافة عناصر الفاتورة
                for item in invoice_items:
                    # إضافة صف جديد
                    row = self.products_table.rowCount()
                    self.products_table.insertRow(row)

                    # إنشاء قائمة منسدلة للمنتجات
                    product_combo = StyledComboBox()
                    for product in self.products:
                        product_combo.addItem(f"{product.name} ({product.code})", product.id)

                    # تحديد المنتج
                    product_index = product_combo.findData(item.product_id)
                    if product_index >= 0:
                        product_combo.setCurrentIndex(product_index)

                    product_combo.currentIndexChanged.connect(lambda: self.update_product_price(row))
                    self.products_table.setCellWidget(row, 0, product_combo)

                    # إنشاء حقل الكمية
                    quantity_spin = StyledDoubleSpinBox()
                    quantity_spin.setRange(0.01, item.quantity)  # لا يمكن إرجاع أكثر مما تم شراؤه
                    quantity_spin.setValue(item.quantity)
                    quantity_spin.valueChanged.connect(lambda: self.update_row_total(row))
                    self.products_table.setCellWidget(row, 1, quantity_spin)

                    # إنشاء حقل سعر الوحدة
                    price_spin = StyledDoubleSpinBox()
                    price_spin.setRange(0.01, 1000000)
                    price_spin.setValue(item.unit_price)
                    price_spin.valueChanged.connect(lambda: self.update_row_total(row))
                    self.products_table.setCellWidget(row, 2, price_spin)

                    # إنشاء حقل الخصم
                    discount_spin = StyledDoubleSpinBox()
                    discount_spin.setRange(0, 100)
                    discount_spin.setSuffix(" %")
                    discount_spin.setValue(item.discount)
                    discount_spin.valueChanged.connect(lambda: self.update_row_total(row))
                    self.products_table.setCellWidget(row, 3, discount_spin)

                    # إنشاء حقل الضريبة
                    tax_spin = StyledDoubleSpinBox()
                    tax_spin.setRange(0, 100)
                    tax_spin.setSuffix(" %")
                    tax_spin.setValue(item.tax_rate)
                    tax_spin.valueChanged.connect(lambda: self.update_row_total(row))
                    self.products_table.setCellWidget(row, 4, tax_spin)

                    # إنشاء حقل الإجمالي
                    total = item.quantity * item.unit_price
                    discount_amount = (total * item.discount) / 100
                    net_amount = total - discount_amount
                    tax_amount = (net_amount * item.tax_rate) / 100
                    row_total = net_amount + tax_amount

                    total_item = QTableWidgetItem(f"{row_total:.2f}")
                    total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)  # جعله غير قابل للتعديل
                    self.products_table.setItem(row, 5, total_item)

                # تحديث إجمالي الفاتورة
                self.calculate_totals()

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الفاتورة الأصلية: {str(e)}")

    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            # الحصول على آخر فاتورة
            last_invoice = self.db.query(Invoice).filter(
                Invoice.invoice_type == InvoiceType.PURCHASE_RETURN
            ).order_by(desc(Invoice.id)).first()

            # توليد رقم جديد
            if last_invoice:
                # استخراج الرقم من آخر فاتورة
                try:
                    last_number = int(last_invoice.invoice_number.split('-')[-1])
                    new_number = last_number + 1
                except:
                    # إذا فشل استخراج الرقم، استخدم التاريخ الحالي
                    new_number = 1
            else:
                new_number = 1

            # تنسيق الرقم الجديد
            today = datetime.now()
            invoice_number = f"PRET-{today.year}{today.month:02d}-{new_number:04d}"

            # تعيين الرقم في الحقل
            self.invoice_number_edit.setText(invoice_number)

        except Exception as e:
            log_error(f"خطأ في توليد رقم الفاتورة: {str(e)}")
            # استخدام رقم افتراضي
            today = datetime.now()
            self.invoice_number_edit.setText(f"PRET-{today.year}{today.month:02d}-0001")

    def update_product_price(self, row):
        """تحديث سعر المنتج عند اختياره"""
        try:
            # الحصول على المنتج المحدد
            product_combo = self.products_table.cellWidget(row, 0)
            product_id = product_combo.currentData()

            if product_id:
                # البحث عن المنتج
                product = next((p for p in self.products if p.id == product_id), None)

                if product:
                    # تعيين سعر المنتج
                    price_spin = self.products_table.cellWidget(row, 2)
                    price_spin.setValue(product.purchase_price)

                    # تحديث إجمالي الصف
                    self.update_row_total(row)
        except Exception as e:
            log_error(f"خطأ في تحديث سعر المنتج: {str(e)}")

    def populate_form(self):
        """ملء النموذج بالبيانات الحالية للفاتورة"""
        try:
            if not self.invoice:
                return

            # تعيين رقم الفاتورة
            self.invoice_number_edit.setText(self.invoice.invoice_number)

            # تعيين التاريخ والوقت
            self.datetime_edit.setDateTime(self.invoice.invoice_date)

            # تعيين المورد
            supplier_index = self.supplier_combo.findData(self.invoice.supplier_id)
            if supplier_index >= 0:
                self.supplier_combo.setCurrentIndex(supplier_index)

            # تعيين الفاتورة الأصلية
            if self.invoice.reference_invoice_id:
                original_invoice_index = self.original_invoice_combo.findData(self.invoice.reference_invoice_id)
                if original_invoice_index >= 0:
                    self.original_invoice_combo.setCurrentIndex(original_invoice_index)

            # تعيين طريقة الدفع
            payment_method_index = self.payment_method_combo.findData(self.invoice.payment_method.name)
            if payment_method_index >= 0:
                self.payment_method_combo.setCurrentIndex(payment_method_index)

            # تعيين الخصم والضريبة
            self.discount_spin.setValue(self.invoice.discount)
            self.tax_spin.setValue(self.invoice.tax)

            # تعيين المبلغ المدفوع
            self.paid_amount_spin.setValue(self.invoice.paid_amount)

            # تعيين الملاحظات
            if self.invoice.notes:
                self.notes_edit.setText(self.invoice.notes)

            # إضافة عناصر الفاتورة
            for item in self.invoice_items:
                # إضافة صف جديد
                row = self.products_table.rowCount()
                self.products_table.insertRow(row)

                # إنشاء قائمة منسدلة للمنتجات
                product_combo = StyledComboBox()
                for product in self.products:
                    product_combo.addItem(f"{product.name} ({product.code})", product.id)

                # تحديد المنتج
                product_index = product_combo.findData(item.product_id)
                if product_index >= 0:
                    product_combo.setCurrentIndex(product_index)

                product_combo.currentIndexChanged.connect(lambda: self.update_product_price(row))
                self.products_table.setCellWidget(row, 0, product_combo)

                # إنشاء حقل الكمية
                quantity_spin = StyledDoubleSpinBox()
                quantity_spin.setRange(0.01, 1000)
                quantity_spin.setValue(item.quantity)
                quantity_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 1, quantity_spin)

                # إنشاء حقل سعر الوحدة
                price_spin = StyledDoubleSpinBox()
                price_spin.setRange(0.01, 1000000)
                price_spin.setValue(item.unit_price)
                price_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 2, price_spin)

                # إنشاء حقل الخصم
                discount_spin = StyledDoubleSpinBox()
                discount_spin.setRange(0, 100)
                discount_spin.setSuffix(" %")
                discount_spin.setValue(item.discount)
                discount_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 3, discount_spin)

                # إنشاء حقل الضريبة
                tax_spin = StyledDoubleSpinBox()
                tax_spin.setRange(0, 100)
                tax_spin.setSuffix(" %")
                tax_spin.setValue(item.tax_rate)
                tax_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 4, tax_spin)

                # إنشاء حقل الإجمالي
                total = item.quantity * item.unit_price
                discount_amount = (total * item.discount) / 100
                net_amount = total - discount_amount
                tax_amount = (net_amount * item.tax_rate) / 100
                row_total = net_amount + tax_amount

                total_item = QTableWidgetItem(f"{row_total:.2f}")
                total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)  # جعله غير قابل للتعديل
                self.products_table.setItem(row, 5, total_item)

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

        except Exception as e:
            log_error(f"خطأ في ملء النموذج: {str(e)}")

    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return

            # بدء المعاملة
            self.db.begin()

            try:
                # إنشاء أو تحديث الفاتورة
                if self.is_edit_mode:
                    # تحديث الفاتورة الحالية
                    invoice = self.invoice
                else:
                    # إنشاء فاتورة جديدة
                    invoice = Invoice(
                        invoice_type=InvoiceType.PURCHASE_RETURN,
                        status=InvoiceStatus.COMPLETED
                    )

                # تعيين بيانات الفاتورة
                invoice.invoice_number = self.invoice_number_edit.text()
                invoice.invoice_date = self.datetime_edit.dateTime().toPyDateTime()
                invoice.supplier_id = self.supplier_combo.currentData()
                invoice.reference_invoice_id = self.original_invoice_combo.currentData()
                invoice.payment_method = PaymentMethod[self.payment_method_combo.currentData()]
                invoice.discount = self.discount_spin.value()
                invoice.tax = self.tax_spin.value()
                invoice.subtotal = float(self.subtotal_label.text())
                invoice.total = float(self.total_label.text())
                invoice.paid_amount = self.paid_amount_spin.value()
                invoice.notes = self.notes_edit.toPlainText()

                # حفظ الفاتورة
                if not self.is_edit_mode:
                    self.db.add(invoice)
                    self.db.flush()  # للحصول على معرف الفاتورة

                # حذف العناصر الحالية إذا كنا في وضع التعديل
                if self.is_edit_mode:
                    for item in self.invoice_items:
                        self.db.delete(item)
                    self.db.flush()

                # إضافة عناصر الفاتورة
                for row in range(self.products_table.rowCount()):
                    # الحصول على بيانات العنصر
                    product_combo = self.products_table.cellWidget(row, 0)
                    quantity_spin = self.products_table.cellWidget(row, 1)
                    price_spin = self.products_table.cellWidget(row, 2)
                    discount_spin = self.products_table.cellWidget(row, 3)
                    tax_spin = self.products_table.cellWidget(row, 4)

                    product_id = product_combo.currentData()
                    quantity = quantity_spin.value()
                    unit_price = price_spin.value()
                    discount = discount_spin.value()
                    tax_rate = tax_spin.value()

                    # إنشاء عنصر الفاتورة
                    invoice_item = InvoiceItem(
                        invoice_id=invoice.id,
                        product_id=product_id,
                        quantity=quantity,
                        unit_price=unit_price,
                        discount=discount,
                        tax_rate=tax_rate
                    )

                    # إضافة العنصر
                    self.db.add(invoice_item)

                    # تحديث كمية المنتج في المخزون (خصم من المخزون لأنها مرتجعات مشتريات)
                    product = self.db.query(Product).filter(Product.id == product_id).first()
                    if product:
                        product.update_quantity(-quantity, is_purchase=False)

                # حفظ التغييرات
                self.db.commit()

                # إرسال إشارة بنجاح الحفظ
                self.invoice_saved.emit(invoice.id)

                # عرض رسالة نجاح
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("invoice_saved", "تم حفظ المرتجع بنجاح")
                )

                # إغلاق النافذة
                self.accept()

            except Exception as e:
                # التراجع عن التغييرات في حالة حدوث خطأ
                self.db.rollback()
                raise e

        except Exception as e:
            log_error(f"خطأ في حفظ المرتجع: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_invoice", "حدث خطأ أثناء حفظ المرتجع")
            )

    def validate_form(self):
        """التحقق من صحة البيانات"""
        # التحقق من وجود مورد
        if not self.supplier_combo.currentData():
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_supplier", "يرجى اختيار المورد")
            )
            return False

        # التحقق من وجود منتجات
        if self.products_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("add_products", "يرجى إضافة منتج واحد على الأقل")
            )
            return False

        # التحقق من صحة الكميات والأسعار
        for row in range(self.products_table.rowCount()):
            quantity_spin = self.products_table.cellWidget(row, 1)
            price_spin = self.products_table.cellWidget(row, 2)

            if quantity_spin.value() <= 0:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("invalid_quantity", "يرجى إدخال كمية صحيحة")
                )
                return False

            if price_spin.value() <= 0:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("invalid_price", "يرجى إدخال سعر صحيح")
                )
                return False

        return True

    def generate_invoice_html(self):
        """إنشاء HTML للفاتورة"""
        # الحصول على بيانات الفاتورة
        invoice_number = self.invoice_number_edit.text()
        invoice_date = self.datetime_edit.dateTime().toString(self.get_datetime_format())
        supplier_name = self.supplier_combo.currentText()
        original_invoice = self.original_invoice_combo.currentText()

        # إنشاء HTML
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .invoice-header {{ text-align: center; margin-bottom: 20px; }}
                .invoice-details {{ margin-bottom: 20px; }}
                .invoice-table {{ width: 100%; border-collapse: collapse; }}
                .invoice-table th, .invoice-table td {{ border: 1px solid #ddd; padding: 8px; }}
                .invoice-table th {{ background-color: #f2f2f2; }}
                .invoice-totals {{ margin-top: 20px; }}
                .right-align {{ text-align: right; }}
            </style>
        </head>
        <body dir="{tr.get_current_language() == 'ar' and 'rtl' or 'ltr'}">
            <div class="invoice-header">
                <h1>{tr.get_text("purchase_return", "مرتجع مشتريات")}</h1>
            </div>

            <div class="invoice-details">
                <p><strong>{tr.get_text("invoice_number", "رقم المرتجع")}:</strong> {invoice_number}</p>
                <p><strong>{tr.get_text("date_time", "التاريخ والوقت")}:</strong> {invoice_date}</p>
                <p><strong>{tr.get_text("supplier", "المورد")}:</strong> {supplier_name}</p>
                <p><strong>{tr.get_text("original_invoice", "الفاتورة الأصلية")}:</strong> {original_invoice}</p>
            </div>

            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>{tr.get_text("product", "المنتج")}</th>
                        <th>{tr.get_text("quantity", "الكمية")}</th>
                        <th>{tr.get_text("unit_price", "سعر الوحدة")}</th>
                        <th>{tr.get_text("discount", "الخصم")}</th>
                        <th>{tr.get_text("tax", "الضريبة")}</th>
                        <th>{tr.get_text("total", "الإجمالي")}</th>
                    </tr>
                </thead>
                <tbody>
        """

        # إضافة عناصر الفاتورة
        for row in range(self.products_table.rowCount()):
            product_combo = self.products_table.cellWidget(row, 0)
            quantity_spin = self.products_table.cellWidget(row, 1)
            price_spin = self.products_table.cellWidget(row, 2)
            discount_spin = self.products_table.cellWidget(row, 3)
            tax_spin = self.products_table.cellWidget(row, 4)
            total_item = self.products_table.item(row, 5)

            product_name = product_combo.currentText()
            quantity = quantity_spin.value()
            unit_price = price_spin.value()
            discount = discount_spin.value()
            tax = tax_spin.value()
            total = total_item.text()

            html += f"""
                <tr>
                    <td>{product_name}</td>
                    <td>{quantity}</td>
                    <td>{unit_price:.2f}</td>
                    <td>{discount:.2f}%</td>
                    <td>{tax:.2f}%</td>
                    <td>{total}</td>
                </tr>
            """

        # إضافة الإجماليات
        subtotal = self.subtotal_label.text()
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()
        total = self.total_label.text()
        paid = self.paid_amount_spin.value()
        remaining = self.remaining_label.text()

        html += f"""
                </tbody>
            </table>

            <div class="invoice-totals">
                <p><strong>{tr.get_text("subtotal", "المجموع الفرعي")}:</strong> {subtotal}</p>
                <p><strong>{tr.get_text("discount", "الخصم")}:</strong> {discount:.2f}%</p>
                <p><strong>{tr.get_text("tax", "الضريبة")}:</strong> {tax:.2f}%</p>
                <p><strong>{tr.get_text("total", "الإجمالي")}:</strong> {total}</p>
                <p><strong>{tr.get_text("paid_amount", "المبلغ المدفوع")}:</strong> {paid:.2f}</p>
                <p><strong>{tr.get_text("remaining", "المتبقي")}:</strong> {remaining}</p>
            </div>

            <div class="invoice-notes">
                <p><strong>{tr.get_text("notes", "ملاحظات")}:</strong></p>
                <p>{self.notes_edit.toPlainText()}</p>
            </div>
        </body>
        </html>
        """

        return html

