#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع لنظام "أمين الحسابات"
اختبار أساسي للتأكد من عمل الميزات الرئيسية
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# تعطيل مقياس الشاشة عالي DPI
QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🔍 اختبار الاستيرادات الأساسية...")
    
    try:
        # اختبار قاعدة البيانات
        from src.database import init_db, get_db
        print("  ✅ قاعدة البيانات")
        
        # اختبار النماذج
        from src.models import User, Customer, Supplier, Product, Employee
        print("  ✅ النماذج")
        
        # اختبار الترجمات
        from src.utils import translation_manager as tr
        print("  ✅ نظام الترجمات")
        
        # اختبار الواجهات الرئيسية
        from src.ui.windows.main_window import MainWindow
        print("  ✅ النافذة الرئيسية")
        
        from src.features.dashboard.views import DashboardView
        print("  ✅ لوحة التحكم")
        
        from src.features.pos.views import POSMainView
        print("  ✅ نظام POS")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في الاستيرادات: {str(e)}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🗄️ اختبار قاعدة البيانات...")
    
    try:
        from src.database import init_db, get_db
        
        # إنشاء قاعدة البيانات
        init_db()
        print("  ✅ تم إنشاء قاعدة البيانات")
        
        # اختبار الاتصال
        db = next(get_db())
        result = db.execute("SELECT 1").fetchone()
        
        if result and result[0] == 1:
            print("  ✅ الاتصال يعمل بشكل صحيح")
            return True
        else:
            print("  ❌ فشل في اختبار الاتصال")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في قاعدة البيانات: {str(e)}")
        return False

def test_translations():
    """اختبار نظام الترجمات"""
    print("🌐 اختبار نظام الترجمات...")
    
    try:
        from src.utils import translation_manager as tr
        
        # تحميل الترجمات
        tr.load_translations()
        print("  ✅ تم تحميل الترجمات")
        
        # اختبار النصوص العربية
        arabic_text = tr.get_text("dashboard", "لوحة التحكم")
        if arabic_text:
            print(f"  ✅ النص العربي: {arabic_text}")
        
        # اختبار النصوص الإنجليزية
        tr.set_language('en')
        english_text = tr.get_text("dashboard", "Dashboard")
        if english_text:
            print(f"  ✅ النص الإنجليزي: {english_text}")
        
        # العودة للعربية
        tr.set_language('ar')
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في نظام الترجمات: {str(e)}")
        return False

def test_main_window():
    """اختبار النافذة الرئيسية"""
    print("🏠 اختبار النافذة الرئيسية...")
    
    try:
        from src.ui.windows.main_window import MainWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        print("  ✅ تم إنشاء النافذة الرئيسية")
        
        # اختبار وجود العناصر الأساسية
        has_toolbar = hasattr(main_window, 'toolbar')
        has_content = hasattr(main_window, 'content_stack')
        
        if has_toolbar:
            print("  ✅ شريط الأدوات موجود")
        if has_content:
            print("  ✅ منطقة المحتوى موجودة")
        
        return has_toolbar and has_content
        
    except Exception as e:
        print(f"  ❌ خطأ في النافذة الرئيسية: {str(e)}")
        return False

def test_pos_system():
    """اختبار نظام POS"""
    print("🛒 اختبار نظام POS...")
    
    try:
        from src.features.pos.views import POSMainView
        
        # إنشاء واجهة POS
        pos_view = POSMainView()
        print("  ✅ تم إنشاء واجهة POS")
        
        # اختبار العناصر الأساسية
        has_cart = hasattr(pos_view, 'cart_table')
        has_search = hasattr(pos_view, 'search_input')
        has_payment = hasattr(pos_view, 'payment_btn')
        
        features = []
        if has_cart:
            features.append("سلة التسوق")
        if has_search:
            features.append("البحث")
        if has_payment:
            features.append("الدفع")
        
        print(f"  ✅ الميزات المتاحة: {', '.join(features)}")
        
        return len(features) >= 2
        
    except Exception as e:
        print(f"  ❌ خطأ في نظام POS: {str(e)}")
        return False

def test_advanced_features():
    """اختبار الميزات المتقدمة"""
    print("🚀 اختبار الميزات المتقدمة...")
    
    features_working = 0
    
    # اختبار ماسح الباركود
    try:
        from src.utils.barcode_scanner import BarcodeScanner
        scanner = BarcodeScanner()
        if scanner:
            print("  ✅ ماسح الباركود")
            features_working += 1
    except:
        print("  ❌ ماسح الباركود")
    
    # اختبار درج النقود
    try:
        from src.utils.cash_drawer import CashDrawer
        cash_drawer = CashDrawer()
        if cash_drawer:
            print("  ✅ درج النقود")
            features_working += 1
    except:
        print("  ❌ درج النقود")
    
    # اختبار المعاملات المعلقة
    try:
        from src.features.pos.held_transactions import HeldTransactionsManager
        held_manager = HeldTransactionsManager()
        if held_manager:
            print("  ✅ المعاملات المعلقة")
            features_working += 1
    except:
        print("  ❌ المعاملات المعلقة")
    
    # اختبار نظام الطباعة
    try:
        from src.utils.print_manager import PrintManager
        print_manager = PrintManager()
        if print_manager:
            print("  ✅ نظام الطباعة")
            features_working += 1
    except:
        print("  ❌ نظام الطباعة")
    
    # اختبار إدارة المستخدمين
    try:
        from src.features.users.views import UserManagementView
        users_view = UserManagementView()
        if users_view:
            print("  ✅ إدارة المستخدمين")
            features_working += 1
    except:
        print("  ❌ إدارة المستخدمين")
    
    print(f"  📊 الميزات العاملة: {features_working}/5")
    return features_working >= 3

def run_quick_test():
    """تشغيل الاختبار السريع"""
    print("🧪 بدء الاختبار السريع لنظام أمين الحسابات")
    print("=" * 60)
    
    tests = [
        ("الاستيرادات الأساسية", test_basic_imports),
        ("قاعدة البيانات", test_database_connection),
        ("نظام الترجمات", test_translations),
        ("النافذة الرئيسية", test_main_window),
        ("نظام POS", test_pos_system),
        ("الميزات المتقدمة", test_advanced_features)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار السريع:")
    print(f"• إجمالي الاختبارات: {total_tests}")
    print(f"• الاختبارات الناجحة: {passed_tests}")
    print(f"• الاختبارات الفاشلة: {total_tests - passed_tests}")
    
    success_rate = (passed_tests / total_tests * 100)
    print(f"• معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🏆 تقييم النظام: ممتاز!")
    elif success_rate >= 80:
        print("🥈 تقييم النظام: جيد جداً")
    elif success_rate >= 70:
        print("🥉 تقييم النظام: جيد")
    else:
        print("⚠️ تقييم النظام: يحتاج تحسين")
    
    print(f"📅 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success_rate >= 80

def main():
    """الدالة الرئيسية"""
    try:
        print("🚀 مرحباً بك في الاختبار السريع لنظام أمين الحسابات")
        print("📝 هذا اختبار أساسي للتأكد من عمل الميزات الرئيسية")
        print()
        
        # تشغيل الاختبار السريع
        success = run_quick_test()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 الاختبار السريع مكتمل بنجاح!")
            print("💡 يمكنك الآن تشغيل الاختبار الشامل للحصول على تفاصيل أكثر")
            print("   python comprehensive_system_test.py")
        else:
            print("⚠️ تم العثور على بعض المشاكل")
            print("💡 يرجى مراجعة الأخطاء أعلاه وإصلاحها")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبار السريع: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
