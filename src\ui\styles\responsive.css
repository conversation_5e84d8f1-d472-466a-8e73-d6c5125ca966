/*
 * أنماط CSS للتصميم المتجاوب
 * يتم تطبيق هذه الأنماط على واجهة المستخدم لجعلها متوافقة مع مختلف أحجام الشاشات
 */

/* أنماط عامة */
QWidget {
    font-family: '<PERSON><PERSON><PERSON> UI', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', sans-serif;
}

/* أنماط للشاشات الكبيرة (أكبر من 1920 بكسل) */
@media (min-width: 1920px) {
    QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox, QRadioButton {
        font-size: 12pt;
    }

    QHeaderView::section {
        font-size: 12pt;
    }

    QTableView {
        font-size: 11pt;
    }

    HeaderLabel {
        font-size: 18pt;
    }
}

/* أنماط للشاشات المتوسطة (بين 1366 و 1920 بكسل) */
@media (min-width: 1366px) and (max-width: 1919px) {
    QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox, QRadioButton {
        font-size: 10pt;
    }

    QHeaderView::section {
        font-size: 10pt;
    }

    QTableView {
        font-size: 9pt;
    }

    HeaderLabel {
        font-size: 16pt;
    }
}

/* أنماط للشاشات الصغيرة (أقل من 1366 بكسل) */
@media (max-width: 1365px) {
    QLabel, QPushButton, QLineEdit, QComboBox, QCheckBox, QRadioButton {
        font-size: 9pt;
    }

    QHeaderView::section {
        font-size: 9pt;
    }

    QTableView {
        font-size: 8pt;
    }

    HeaderLabel {
        font-size: 14pt;
    }
}

/* أنماط للأزرار */
QPushButton {
    min-height: 30px;
    padding: 5px 15px;
    border-radius: 4px;
}

/* أنماط للحقول النصية */
QLineEdit, QTextEdit, QComboBox {
    min-height: 25px;
    padding: 2px 5px;
    border-radius: 4px;
}

/* أنماط للجداول */
QTableView {
    gridline-color: rgba(128, 128, 128, 100);
    selection-background-color: rgba(0, 120, 215, 150);
}

QHeaderView::section {
    padding: 5px;
    border: none;
    border-bottom: 1px solid rgba(128, 128, 128, 150);
    border-right: 1px solid rgba(128, 128, 128, 150);
}

/* أنماط للتبويبات */
QTabWidget::pane {
    border: 1px solid rgba(128, 128, 128, 150);
    border-radius: 4px;
}

QTabBar::tab {
    padding: 8px 15px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

/* أنماط للمجموعات */
QGroupBox {
    margin-top: 15px;
    font-weight: bold;
    border-radius: 4px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
    left: 10px;
}

/* أنماط للقوائم المنسدلة */
QComboBox {
    min-width: 120px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: center right;
    width: 20px;
    border-left-width: 1px;
    border-left-style: solid;
}

/* أنماط لشريط الأدوات */
QToolBar {
    min-height: 40px;
    spacing: 5px;
    padding: 2px;
}

QToolBar QToolButton {
    min-width: 40px;
    min-height: 40px;
    padding: 5px;
    border-radius: 4px;
}

/* أنماط لشريط الحالة */
QStatusBar {
    min-height: 25px;
}

/* أنماط للرسائل */
QMessageBox {
    min-width: 300px;
}

QMessageBox QPushButton {
    min-width: 80px;
    min-height: 25px;
}

/* أنماط للحوارات */
QDialog {
    min-width: 400px;
    min-height: 300px;
}

/* أنماط للتمرير */
QScrollBar:vertical {
    width: 12px;
    margin: 12px 0 12px 0;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar:horizontal {
    height: 12px;
    margin: 0 12px 0 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    min-width: 20px;
    border-radius: 6px;
}

/* أنماط للتوجيه من اليمين إلى اليسار */
QWidget[rtl="true"], QWidget[dir="rtl"] {
    /* direction property is not supported in Qt CSS */
}

QWidget[rtl="true"] QHeaderView::section, QWidget[dir="rtl"] QHeaderView::section {
    border-right: none;
    border-left: 1px solid rgba(128, 128, 128, 150);
    text-align: right;
}

QWidget[rtl="true"] QComboBox::drop-down, QWidget[dir="rtl"] QComboBox::drop-down {
    subcontrol-position: center left;
    border-left-width: 0;
    border-right-width: 1px;
    border-right-style: solid;
}

/* تعديلات إضافية للغة العربية */
QWidget[lang="ar"] {
    font-family: 'Cairo', 'Droid Arabic Kufi', 'Tahoma', sans-serif;
}

/* محاذاة النصوص في اللغة العربية */
QWidget[rtl="true"] QLabel, QWidget[dir="rtl"] QLabel,
QWidget[rtl="true"] QPushButton, QWidget[dir="rtl"] QPushButton,
QWidget[rtl="true"] QCheckBox, QWidget[dir="rtl"] QCheckBox,
QWidget[rtl="true"] QRadioButton, QWidget[dir="rtl"] QRadioButton,
QWidget[rtl="true"] QGroupBox, QWidget[dir="rtl"] QGroupBox {
    text-align: right;
}

/* محاذاة حقول الإدخال في اللغة العربية */
QWidget[rtl="true"] QLineEdit, QWidget[dir="rtl"] QLineEdit,
QWidget[rtl="true"] QTextEdit, QWidget[dir="rtl"] QTextEdit,
QWidget[rtl="true"] QPlainTextEdit, QWidget[dir="rtl"] QPlainTextEdit {
    text-align: right;
}

/* تعديلات القوائم في اللغة العربية */
QWidget[rtl="true"] QMenu::item, QWidget[dir="rtl"] QMenu::item {
    padding-right: 25px;
    padding-left: 8px;
    text-align: right;
}

/* تعديلات الجداول في اللغة العربية */
QWidget[rtl="true"] QTableView, QWidget[dir="rtl"] QTableView {
    text-align: right;
}

/* تعديلات التبويبات في اللغة العربية */
QWidget[rtl="true"] QTabBar::tab, QWidget[dir="rtl"] QTabBar::tab {
    margin-right: 0px;
    margin-left: 2px;
}

/* تعديلات مربعات المجموعة في اللغة العربية */
QWidget[rtl="true"] QGroupBox::title, QWidget[dir="rtl"] QGroupBox::title {
    subcontrol-position: top right;
    right: 10px;
    left: auto;
}
