#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
دعم طابعات نقاط البيع (POS)
يدعم:
- الطباعة الحرارية
- أحجام ورق مختلفة (58mm, 80mm)
- أوامر ESC/POS
- طباعة الباركود
"""

import os
import tempfile
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtPrintSupport import QPrinter
from PyQt5.QtGui import QTextDocument, QFont, QFontMetrics
from PyQt5.QtCore import QSizeF, QMarginsF, Qt

from src.utils import translation_manager as tr
from src.utils import config
from src.utils.logger import log_info, log_error

class POSPrinter:
    """طابعة نقاط البيع"""
    
    def __init__(self):
        """تهيئة طابعة POS"""
        self.paper_width = config.get_setting('pos_paper_width', 80)  # 58mm أو 80mm
        self.char_per_line = 32 if self.paper_width == 58 else 42
        self.font_size = config.get_setting('pos_font_size', 8)
        self.printer_name = config.get_setting('pos_printer_name', '')
        
    def setup_printer(self):
        """إعداد طابعة POS"""
        printer = QPrinter(QPrinter.HighResolution)
        
        # تعيين حجم الورق المخصص
        if self.paper_width == 58:
            # ورق 58mm
            paper_size = QSizeF(58, 200)  # الطول متغير
        else:
            # ورق 80mm
            paper_size = QSizeF(80, 200)
            
        printer.setPageSize(printer.Custom)
        printer.setPaperSize(paper_size, QPrinter.Millimeter)
        
        # هوامش صغيرة
        printer.setPageMargins(QMarginsF(2, 2, 2, 2), QPrinter.Millimeter)
        
        # تعيين اسم الطابعة
        if self.printer_name:
            printer.setPrinterName(self.printer_name)
            
        return printer
    
    def format_line(self, text, align='left', fill_char=' '):
        """تنسيق سطر للطباعة"""
        if len(text) > self.char_per_line:
            return text[:self.char_per_line]
            
        if align == 'center':
            return text.center(self.char_per_line, fill_char)
        elif align == 'right':
            return text.rjust(self.char_per_line, fill_char)
        else:
            return text.ljust(self.char_per_line, fill_char)
    
    def format_two_columns(self, left_text, right_text, separator=' '):
        """تنسيق عمودين في سطر واحد"""
        available_space = self.char_per_line - len(separator)
        left_space = available_space // 2
        right_space = available_space - left_space
        
        # قطع النص إذا كان طويلاً
        if len(left_text) > left_space:
            left_text = left_text[:left_space-2] + '..'
        if len(right_text) > right_space:
            right_text = right_text[:right_space-2] + '..'
            
        # تنسيق السطر
        left_part = left_text.ljust(left_space)
        right_part = right_text.rjust(right_space)
        
        return left_part + separator + right_part
    
    def generate_receipt_html(self, invoice_data):
        """إنشاء HTML لإيصال POS"""
        company_info = config.get_company_info()
        
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{
                    size: {self.paper_width}mm 200mm;
                    margin: 2mm;
                }}
                body {{
                    font-family: 'Courier New', monospace;
                    font-size: {self.font_size}pt;
                    line-height: 1.2;
                    margin: 0;
                    padding: 0;
                    direction: rtl;
                    text-align: center;
                }}
                .center {{ text-align: center; }}
                .left {{ text-align: left; }}
                .right {{ text-align: right; }}
                .bold {{ font-weight: bold; }}
                .line {{ border-top: 1px dashed #000; margin: 2px 0; }}
                .double-line {{ border-top: 2px solid #000; margin: 3px 0; }}
                .item-row {{ display: flex; justify-content: space-between; }}
                .barcode {{ font-family: 'Libre Barcode 39', monospace; font-size: 24pt; }}
            </style>
        </head>
        <body>
        """
        
        # رأس الإيصال
        html += f"""
            <div class="center bold">
                {company_info.get('name', tr.get_text('company_name', 'اسم الشركة'))}
            </div>
        """
        
        if company_info.get('address'):
            html += f"<div class='center'>{company_info['address']}</div>"
        if company_info.get('phone'):
            html += f"<div class='center'>{tr.get_text('phone', 'هاتف')}: {company_info['phone']}</div>"
        if company_info.get('vat'):
            html += f"<div class='center'>{tr.get_text('vat_number', 'الرقم الضريبي')}: {company_info['vat']}</div>"
            
        html += "<div class='double-line'></div>"
        
        # معلومات الفاتورة
        html += f"""
            <div class="center bold">{tr.get_text('sales_invoice', 'فاتورة مبيعات')}</div>
            <div class='line'></div>
            <div>{tr.get_text('invoice_number', 'رقم الفاتورة')}: {invoice_data.get('invoice_number', '')}</div>
            <div>{tr.get_text('date', 'التاريخ')}: {invoice_data.get('date', datetime.now().strftime('%Y-%m-%d'))}</div>
            <div>{tr.get_text('time', 'الوقت')}: {datetime.now().strftime('%H:%M')}</div>
        """
        
        if invoice_data.get('customer_name'):
            html += f"<div>{tr.get_text('customer', 'العميل')}: {invoice_data['customer_name']}</div>"
            
        html += "<div class='line'></div>"
        
        # الأصناف
        html += f"""
            <div class="bold">
                {tr.get_text('item', 'الصنف')} | {tr.get_text('qty', 'كمية')} | {tr.get_text('price', 'سعر')} | {tr.get_text('total', 'إجمالي')}
            </div>
            <div class='line'></div>
        """
        
        total_amount = 0
        for item in invoice_data.get('items', []):
            item_name = item.get('name', '')[:15]  # قطع الاسم إذا كان طويلاً
            quantity = item.get('quantity', 0)
            price = item.get('price', 0)
            item_total = quantity * price
            total_amount += item_total
            
            html += f"""
                <div class="left">
                    {item_name}
                </div>
                <div class="right">
                    {quantity} x {price:.2f} = {item_total:.2f}
                </div>
            """
            
        html += "<div class='line'></div>"
        
        # الإجماليات
        subtotal = total_amount
        tax_rate = invoice_data.get('tax_rate', 0)
        tax_amount = subtotal * (tax_rate / 100)
        discount_amount = invoice_data.get('discount_amount', 0)
        final_total = subtotal + tax_amount - discount_amount
        
        html += f"""
            <div class="right">
                {tr.get_text('subtotal', 'المجموع الفرعي')}: {subtotal:.2f}
            </div>
        """
        
        if tax_amount > 0:
            html += f"""
                <div class="right">
                    {tr.get_text('tax', 'ضريبة')} ({tax_rate}%): {tax_amount:.2f}
                </div>
            """
            
        if discount_amount > 0:
            html += f"""
                <div class="right">
                    {tr.get_text('discount', 'خصم')}: -{discount_amount:.2f}
                </div>
            """
            
        html += f"""
            <div class='double-line'></div>
            <div class="center bold">
                {tr.get_text('total_amount', 'الإجمالي النهائي')}: {final_total:.2f} {config.get_setting('default_currency', 'ج.م')}
            </div>
            <div class='double-line'></div>
        """
        
        # معلومات الدفع
        paid_amount = invoice_data.get('paid_amount', 0)
        remaining_amount = final_total - paid_amount
        
        if paid_amount > 0:
            html += f"""
                <div class="right">
                    {tr.get_text('paid_amount', 'المبلغ المدفوع')}: {paid_amount:.2f}
                </div>
            """
            
        if remaining_amount > 0:
            html += f"""
                <div class="right">
                    {tr.get_text('remaining_amount', 'المبلغ المتبقي')}: {remaining_amount:.2f}
                </div>
            """
        elif remaining_amount < 0:
            html += f"""
                <div class="right">
                    {tr.get_text('change_amount', 'المبلغ المرتجع')}: {abs(remaining_amount):.2f}
                </div>
            """
            
        # تذييل الإيصال
        html += f"""
            <div class='line'></div>
            <div class="center">
                {tr.get_text('thank_you', 'شكراً لتعاملكم معنا')}
            </div>
            <div class="center">
                {tr.get_text('visit_again', 'نتطلع لزيارتكم مرة أخرى')}
            </div>
        """
        
        # باركود (اختياري)
        if invoice_data.get('barcode'):
            html += f"""
                <div class='line'></div>
                <div class="center barcode">
                    *{invoice_data['barcode']}*
                </div>
            """
            
        html += """
        </body>
        </html>
        """
        
        return html
    
    def print_receipt(self, invoice_data, preview=True, parent=None):
        """طباعة إيصال POS"""
        try:
            # إنشاء HTML للإيصال
            html_content = self.generate_receipt_html(invoice_data)
            
            # إنشاء مستند
            document = QTextDocument()
            document.setHtml(html_content)
            
            # إعداد الطابعة
            printer = self.setup_printer()
            
            if preview:
                # عرض معاينة الطباعة
                from PyQt5.QtPrintSupport import QPrintPreviewDialog
                preview_dialog = QPrintPreviewDialog(printer, parent)
                preview_dialog.paintRequested.connect(lambda p: document.print_(p))
                
                if preview_dialog.exec_() == QPrintPreviewDialog.Accepted:
                    log_info("تمت معاينة إيصال POS")
                    return True
            else:
                # طباعة مباشرة
                document.print_(printer)
                log_info("تمت طباعة إيصال POS")
                return True
                
            return False
            
        except Exception as e:
            log_error(f"خطأ في طباعة إيصال POS: {str(e)}")
            if parent:
                QMessageBox.critical(
                    parent,
                    tr.get_text("error_title", "خطأ"),
                    tr.get_text("pos_print_error", "حدث خطأ أثناء طباعة الإيصال")
                )
            return False
    
    def test_printer(self, parent=None):
        """اختبار طابعة POS"""
        try:
            # بيانات اختبار
            test_data = {
                'invoice_number': 'TEST-001',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'customer_name': tr.get_text('test_customer', 'عميل تجريبي'),
                'items': [
                    {
                        'name': tr.get_text('test_item', 'منتج تجريبي'),
                        'quantity': 1,
                        'price': 10.00
                    }
                ],
                'tax_rate': 15,
                'discount_amount': 0,
                'paid_amount': 11.50
            }
            
            return self.print_receipt(test_data, preview=True, parent=parent)
            
        except Exception as e:
            log_error(f"خطأ في اختبار طابعة POS: {str(e)}")
            return False
