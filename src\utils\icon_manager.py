#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الأيقونات - حل مشكلة تحميل أيقونات FontAwesome
"""

from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QStyle, QApplication
import qtawesome as qta
from src.utils.logger import log_error, log_warning, log_info

class IconManager:
    """مدير الأيقونات مع دعم احتياطي"""

    _instance = None
    _icons_cache = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._initialized = True
            self._test_qtawesome()

    def _test_qtawesome(self):
        """اختبار عمل qtawesome"""
        try:
            # اختبار بسيط لتحميل أيقونة
            test_icon = qta.icon('fa5s.home')
            self._qtawesome_working = True
            log_info("qtawesome يعمل بشكل صحيح")
        except Exception as e:
            self._qtawesome_working = False
            log_warning(f"qtawesome لا يعمل، سيتم استخدام الأيقونات الاحتياطية: {str(e)}")

    def get_icon(self, icon_name, color='white', fallback_text='•'):
        """
        الحصول على أيقونة مع دعم احتياطي

        Args:
            icon_name: اسم الأيقونة (مثل 'fa5s.home')
            color: لون الأيقونة
            fallback_text: نص احتياطي إذا فشل تحميل الأيقونة
        """
        # التحقق من الكاش
        cache_key = f"{icon_name}_{color}"
        if cache_key in self._icons_cache:
            return self._icons_cache[cache_key]

        icon = None

        # محاولة استخدام qtawesome
        if self._qtawesome_working:
            try:
                icon = qta.icon(icon_name, color=color)
                self._icons_cache[cache_key] = icon
                return icon
            except Exception as e:
                log_warning(f"فشل في تحميل أيقونة {icon_name}: {str(e)}")

        # استخدام الأيقونات الاحتياطية
        icon = self._create_fallback_icon(fallback_text, color)
        self._icons_cache[cache_key] = icon
        return icon

    def _create_fallback_icon(self, text, color):
        """إنشاء أيقونة احتياطية من النص"""
        try:
            # إنشاء pixmap
            pixmap = QPixmap(16, 16)
            pixmap.fill(Qt.transparent)

            # رسم النص
            painter = QPainter(pixmap)
            painter.setPen(QColor(color))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
            painter.end()

            return QIcon(pixmap)
        except Exception as e:
            log_error(f"فشل في إنشاء أيقونة احتياطية: {str(e)}")
            # إرجاع أيقونة فارغة
            return QIcon()

    def get_standard_icon(self, standard_pixmap):
        """الحصول على أيقونة قياسية من النظام"""
        try:
            app = QApplication.instance()
            if app:
                style = app.style()
                return style.standardIcon(standard_pixmap)
        except Exception as e:
            log_error(f"فشل في الحصول على أيقونة قياسية: {str(e)}")

        return QIcon()

# إنشاء مثيل عام
icon_manager = IconManager()

def get_icon(icon_name, color='white', fallback_text='•'):
    """دالة مساعدة للحصول على الأيقونات"""
    return icon_manager.get_icon(icon_name, color, fallback_text)

def get_standard_icon(standard_pixmap):
    """دالة مساعدة للحصول على الأيقونات القياسية"""
    return icon_manager.get_standard_icon(standard_pixmap)

# خريطة الأيقونات الشائعة مع النصوص الاحتياطية
ICON_MAP = {
    'fa5s.home': '🏠',
    'fa5s.dashboard': '📊',
    'fa5s.shopping-cart': '🛒',
    'fa5s.box': '📦',
    'fa5s.users': '👥',
    'fa5s.user': '👤',
    'fa5s.building': '🏢',
    'fa5s.chart-bar': '📈',
    'fa5s.print': '🖨️',
    'fa5s.cog': '⚙️',
    'fa5s.plus': '➕',
    'fa5s.edit': '✏️',
    'fa5s.trash': '🗑️',
    'fa5s.save': '💾',
    'fa5s.search': '🔍',
    'fa5s.refresh': '🔄',
    'fa5s.download': '⬇️',
    'fa5s.upload': '⬆️',
    'fa5s.file': '📄',
    'fa5s.folder': '📁',
    'fa5s.star': '⭐',
    'fa5s.heart': '❤️',
    'fa5s.check': '✅',
    'fa5s.times': '❌',
    'fa5s.warning': '⚠️',
    'fa5s.info': 'ℹ️',
    'fa5s.question': '❓',
    'fa5s.lock': '🔒',
    'fa5s.unlock': '🔓',
    'fa5s.key': '🔑',
    'fa5s.eye': '👁️',
    'fa5s.eye-slash': '🙈',
    'fa5s.calendar': '📅',
    'fa5s.clock': '🕐',
    'fa5s.phone': '📞',
    'fa5s.envelope': '✉️',
    'fa5s.map-marker': '📍',
    'fa5s.money-bill': '💵',
    'fa5s.credit-card': '💳',
    'fa5s.calculator': '🧮',
    'fa5s.barcode': '📊',
    'fa5s.qrcode': '📱',
    'fa5s.sync': '🔄',
    'fa5s.undo': '↶',
    'fa5s.redo': '↷',
    'fa5s.copy': '📋',
    'fa5s.cut': '✂️',
    'fa5s.paste': '📄',
    'fa5s.bold': 'B',
    'fa5s.italic': 'I',
    'fa5s.underline': 'U',
    'fa5s.align-left': '⬅️',
    'fa5s.align-center': '↔️',
    'fa5s.align-right': '➡️',
    'fa5s.list': '📝',
    'fa5s.table': '📊',
    'fa5s.image': '🖼️',
    'fa5s.video': '🎥',
    'fa5s.music': '🎵',
    'fa5s.volume-up': '🔊',
    'fa5s.volume-down': '🔉',
    'fa5s.volume-mute': '🔇',
    'fa5s.play': '▶️',
    'fa5s.pause': '⏸️',
    'fa5s.stop': '⏹️',
    'fa5s.forward': '⏩',
    'fa5s.backward': '⏪',
    'fa5s.step-forward': '⏭️',
    'fa5s.step-backward': '⏮️',
    'fa5s.random': '🔀',
    'fa5s.repeat': '🔁',
}

def get_icon_with_fallback(icon_name, color='white'):
    """الحصول على أيقونة مع نص احتياطي من الخريطة"""
    fallback_text = ICON_MAP.get(icon_name, '•')
    return get_icon(icon_name, color, fallback_text)

# تصدير الدوال المهمة
__all__ = [
    'IconManager',
    'icon_manager',
    'get_icon',
    'get_standard_icon',
    'get_icon_with_fallback',
    'ICON_MAP'
]
