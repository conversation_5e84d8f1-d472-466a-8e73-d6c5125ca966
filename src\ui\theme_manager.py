#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QSettings
from pathlib import Path
import json
import os

class ThemeManager:
    """Theme management system with RTL support"""

    _instance = None
    THEME_DARK = "dark"
    THEME_LIGHT = "light"

    @classmethod
    def get_instance(cls):
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize theme manager"""
        self.settings = QSettings('Your Company', '<PERSON>in <PERSON>')
        self.current_theme = self.settings.value('theme', self.THEME_LIGHT)
        self.current_direction = self.settings.value('direction', 'ltr')
        self._load_themes()

    def _load_themes(self):
        """Load theme stylesheets"""
        self.themes = {}
        theme_dir = Path(__file__).parent.parent / 'ui' / 'styles'

        # Load base styles (common to both themes)
        base_file = theme_dir / 'base.qss'
        with open(base_file, 'r', encoding='utf-8') as f:
            self.base_style = f.read()

        # Load responsive styles
        responsive_file = theme_dir / 'responsive.css'
        if responsive_file.exists():
            with open(responsive_file, 'r', encoding='utf-8') as f:
                self.responsive_style = f.read()
        else:
            self.responsive_style = ""

        # Load theme-specific styles
        for theme in [self.THEME_LIGHT, self.THEME_DARK]:
            theme_file = theme_dir / f'{theme}.qss'
            with open(theme_file, 'r', encoding='utf-8') as f:
                self.themes[theme] = f.read()

    def _get_direction_styles(self, direction='ltr'):
        """Get direction-specific styles"""
        return f"""
        QWidget {{
            direction: {direction};
        }}

        /* RTL-specific adjustments */
        QTreeView, QListView, QTableView {{
            {f'margin-right: 0; margin-left: 5px;' if direction == 'rtl' else 'margin-left: 0; margin-right: 5px;'}
        }}

        QToolButton, QPushButton {{
            {f'padding-right: 5px; padding-left: 15px;' if direction == 'rtl' else 'padding-left: 5px; padding-right: 15px;'}
        }}

        /* RTL adjustments for headers and labels */
        QHeaderView::section {{
            {f'text-align: right;' if direction == 'rtl' else 'text-align: left;'}
        }}

        /* RTL adjustments for combo boxes */
        QComboBox::drop-down {{
            {f'subcontrol-position: left center;' if direction == 'rtl' else 'subcontrol-position: right center;'}
            {f'left: 0px;' if direction == 'rtl' else 'right: 0px;'}
        }}

        /* RTL adjustments for menu items */
        QMenu::item {{
            {f'padding-right: 25px; padding-left: 8px;' if direction == 'rtl' else 'padding-left: 25px; padding-right: 8px;'}
        }}

        /* RTL adjustments for tab bars */
        QTabBar::tab {{
            {f'margin-right: 0px; margin-left: 2px;' if direction == 'rtl' else 'margin-left: 0px; margin-right: 2px;'}
        }}

        /* RTL adjustments for group boxes */
        QGroupBox::title {{
            {f'subcontrol-position: top right;' if direction == 'rtl' else 'subcontrol-position: top left;'}
            {f'right: 10px;' if direction == 'rtl' else 'left: 10px;'}
        }}

        /* RTL adjustments for spinboxes */
        QSpinBox::up-button, QDoubleSpinBox::up-button {{
            {f'subcontrol-position: left top;' if direction == 'rtl' else 'subcontrol-position: right top;'}
        }}

        QSpinBox::down-button, QDoubleSpinBox::down-button {{
            {f'subcontrol-position: left bottom;' if direction == 'rtl' else 'subcontrol-position: right bottom;'}
        }}

        /* RTL adjustments for line edits with icons */
        QLineEdit[iconPlacement="left"] {{
            {f'padding-right: 25px;' if direction == 'rtl' else 'padding-left: 25px;'}
        }}

        QLineEdit[iconPlacement="right"] {{
            {f'padding-left: 25px;' if direction == 'rtl' else 'padding-right: 25px;'}
        }}
        """

    def apply_theme(self, theme=None, direction=None, language=None):
        """
        Apply theme and direction to application
        :param theme: Theme name ('light' or 'dark')
        :param direction: Text direction ('ltr' or 'rtl')
        :param language: Language code ('ar' or 'en')
        """
        app = QApplication.instance()
        if not app:
            return

        # Update theme if specified
        if theme and theme in self.themes:
            self.current_theme = theme
            self.settings.setValue('theme', theme)

        # Update direction if specified or inferred from language
        if direction in ['ltr', 'rtl']:
            self.current_direction = direction
            self.settings.setValue('direction', direction)
        elif language == 'ar':
            self.current_direction = 'rtl'
            self.settings.setValue('direction', 'rtl')
        elif language == 'en':
            self.current_direction = 'ltr'
            self.settings.setValue('direction', 'ltr')

        # Set application-wide layout direction
        app.setLayoutDirection(
            Qt.RightToLeft if self.current_direction == 'rtl' else Qt.LeftToRight
        )

        # Set language attribute for all widgets
        if language in ['ar', 'en']:
            self.settings.setValue('language', language)

            # Apply language-specific font settings
            if language == 'ar':
                # Arabic font settings
                app.setProperty('lang', 'ar')
                app.setProperty('dir', 'rtl')

                # Set default font for Arabic
                font = app.font()
                font.setFamily('Cairo')
                font.setPointSize(10)  # Slightly larger for Arabic
                app.setFont(font)
            else:
                # English font settings
                app.setProperty('lang', 'en')
                app.setProperty('dir', 'ltr')

                # Set default font for English
                font = app.font()
                font.setFamily('Segoe UI')
                font.setPointSize(9)
                app.setFont(font)

        # Combine styles
        combined_style = "\n".join([
            self.base_style,
            self.responsive_style,
            self.themes[self.current_theme],
            self._get_direction_styles(self.current_direction)
        ])

        # Apply combined stylesheet
        app.setStyleSheet(combined_style)

        # Force update of all widgets
        for widget in app.allWidgets():
            widget.setProperty('dir', self.current_direction)
            if language:
                widget.setProperty('lang', language)
            widget.style().unpolish(widget)
            widget.style().polish(widget)
            widget.update()

    def toggle_theme(self):
        """Toggle between light and dark themes"""
        new_theme = self.THEME_LIGHT if self.current_theme == self.THEME_DARK else self.THEME_DARK
        self.apply_theme(new_theme)
        return new_theme

    def get_current_theme(self):
        """Get current theme name"""
        return self.current_theme

    def get_current_direction(self):
        """Get current text direction"""
        return self.current_direction

    def set_direction_from_language(self, language):
        """
        Set direction based on language
        :param language: Language code ('ar' or 'en')
        """
        if language not in ['ar', 'en']:
            return False

        direction = 'rtl' if language == 'ar' else 'ltr'
        self.apply_theme(direction=direction, language=language)
        return True