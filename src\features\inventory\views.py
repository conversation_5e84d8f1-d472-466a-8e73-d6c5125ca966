#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهات المخزون
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QFormLayout, QRadioButton, QFileDialog, QMenu, QAction,
    QToolBar, QToolButton, QSplitter, QFrame, QProgressBar,
    QCalendarWidget, QTimeEdit, QScrollArea
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QDateTime, QSize, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QColor, QBrush, QFont, QPalette
import qtawesome as qta
from datetime import datetime, timedelta
import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_

from src.database import get_db
from src.models import (
    Product, ProductCategory, ProductStatus,
    InventoryMovement, MovementType,
    Warehouse, WarehouseInventory,
    Supplier
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable, StyledDoubleSpinBox, StyledSpinBox
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info
from src.utils.print_manager import PrintManager
from src.utils.excel_manager import ExcelManager

class InventoryView(QWidget):
    """
    واجهة المخزون الرئيسية
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("inventory_management", "إدارة المخزون"))
        layout.addWidget(header)

        # شريط الأدوات
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))

        # زر تحديث
        refresh_action = QAction(qta.icon('fa5s.sync'), tr.get_text("refresh", "تحديث"), self)
        refresh_action.triggered.connect(self.refresh_all)
        toolbar.addAction(refresh_action)

        # زر طباعة
        print_action = QAction(qta.icon('fa5s.print'), tr.get_text("print", "طباعة"), self)
        print_action.triggered.connect(self.print_report)
        toolbar.addAction(print_action)

        # زر تصدير
        export_action = QAction(qta.icon('fa5s.file-export'), tr.get_text("export", "تصدير"), self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        # إضافة شريط الأدوات
        layout.addWidget(toolbar)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب المنتجات
        self.products_tab = ProductsView()
        self.tabs.addTab(self.products_tab, tr.get_text("products", "المنتجات"))

        # تبويب المستودعات
        self.warehouses_tab = WarehousesView()
        self.tabs.addTab(self.warehouses_tab, tr.get_text("warehouses", "المستودعات"))

        # تبويب حركات المخزون
        self.movements_tab = InventoryMovementsView()
        self.tabs.addTab(self.movements_tab, tr.get_text("movements", "حركات المخزون"))

        # تبويب الفئات
        self.categories_tab = CategoriesView()
        self.tabs.addTab(self.categories_tab, tr.get_text("categories", "الفئات"))

        # تبويب التقارير
        self.reports_tab = InventoryReportView()
        self.tabs.addTab(self.reports_tab, tr.get_text("reports", "التقارير"))

        # تبويب الإعدادات
        self.settings_tab = InventorySettingsView()
        self.tabs.addTab(self.settings_tab, tr.get_text("settings", "الإعدادات"))

        layout.addWidget(self.tabs)

    def refresh_all(self):
        """تحديث جميع البيانات"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'load_data'):
            current_tab.load_data()

    def print_report(self):
        """طباعة تقرير"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'print_report'):
            current_tab.print_report()
        else:
            QMessageBox.information(
                self,
                tr.get_text("info", "معلومات"),
                tr.get_text("print_not_available", "الطباعة غير متاحة في هذا القسم")
            )

    def export_data(self):
        """تصدير البيانات"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'export_data'):
            current_tab.export_data()
        else:
            QMessageBox.information(
                self,
                tr.get_text("info", "معلومات"),
                tr.get_text("export_not_available", "التصدير غير متاح في هذا القسم")
            )

class ProductsView(QWidget):
    """
    واجهة المنتجات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_filter = {}
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # شريط البحث والفلترة
        filter_group = QGroupBox(tr.get_text("search_filter", "البحث والفلترة"))
        filter_layout = QHBoxLayout(filter_group)

        # حقل البحث
        self.search_input = StyledLineEdit()
        self.search_input.setPlaceholderText(tr.get_text("search_products", "البحث في المنتجات..."))
        self.search_input.textChanged.connect(self.filter_products)
        filter_layout.addWidget(QLabel(tr.get_text("search", "البحث:")))
        filter_layout.addWidget(self.search_input)

        # فلتر الفئة
        self.category_filter = StyledComboBox()
        self.category_filter.addItem(tr.get_text("all_categories", "جميع الفئات"), None)
        self.category_filter.currentTextChanged.connect(self.filter_products)
        filter_layout.addWidget(QLabel(tr.get_text("category", "الفئة:")))
        filter_layout.addWidget(self.category_filter)

        # فلتر الحالة
        self.status_filter = StyledComboBox()
        self.status_filter.addItem(tr.get_text("all_statuses", "جميع الحالات"), None)
        for status in ProductStatus:
            self.status_filter.addItem(status.value, status)
        self.status_filter.currentTextChanged.connect(self.filter_products)
        filter_layout.addWidget(QLabel(tr.get_text("status", "الحالة:")))
        filter_layout.addWidget(self.status_filter)

        # فلتر المخزون المنخفض
        self.low_stock_filter = StyledCheckBox(tr.get_text("low_stock_only", "المخزون المنخفض فقط"))
        self.low_stock_filter.stateChanged.connect(self.filter_products)
        filter_layout.addWidget(self.low_stock_filter)

        layout.addWidget(filter_group)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_product", "إضافة منتج"))
        self.add_btn.setIcon(qta.icon('fa5s.plus'))
        self.add_btn.clicked.connect(self.add_product)
        actions_layout.addWidget(self.add_btn)

        self.edit_btn = SecondaryButton(tr.get_text("edit_product", "تعديل"))
        self.edit_btn.setIcon(qta.icon('fa5s.edit'))
        self.edit_btn.clicked.connect(self.edit_product)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)

        self.delete_btn = DangerButton(tr.get_text("delete_product", "حذف"))
        self.delete_btn.setIcon(qta.icon('fa5s.trash'))
        self.delete_btn.clicked.connect(self.delete_product)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)

        actions_layout.addSeparator()

        self.adjust_stock_btn = SecondaryButton(tr.get_text("adjust_stock", "تسوية المخزون"))
        self.adjust_stock_btn.setIcon(qta.icon('fa5s.balance-scale'))
        self.adjust_stock_btn.clicked.connect(self.adjust_stock)
        self.adjust_stock_btn.setEnabled(False)
        actions_layout.addWidget(self.adjust_stock_btn)

        self.view_movements_btn = SecondaryButton(tr.get_text("view_movements", "عرض الحركات"))
        self.view_movements_btn.setIcon(qta.icon('fa5s.history'))
        self.view_movements_btn.clicked.connect(self.view_movements)
        self.view_movements_btn.setEnabled(False)
        actions_layout.addWidget(self.view_movements_btn)

        actions_layout.addStretch()

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(qta.icon('fa5s.sync'))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        self.export_btn = SecondaryButton(tr.get_text("export", "تصدير"))
        self.export_btn.setIcon(qta.icon('fa5s.file-export'))
        self.export_btn.clicked.connect(self.export_data)
        actions_layout.addWidget(self.export_btn)

        layout.addLayout(actions_layout)

        # جدول المنتجات
        self.table = StyledTable()
        self.table.setColumnCount(12)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("code", "الكود"),
            tr.get_text("name", "الاسم"),
            tr.get_text("category", "الفئة"),
            tr.get_text("quantity", "الكمية"),
            tr.get_text("unit", "الوحدة"),
            tr.get_text("purchase_price", "سعر الشراء"),
            tr.get_text("selling_price", "سعر البيع"),
            tr.get_text("profit_margin", "هامش الربح %"),
            tr.get_text("stock_value", "قيمة المخزون"),
            tr.get_text("status", "الحالة"),
            tr.get_text("supplier", "المورد"),
            tr.get_text("last_update", "آخر تحديث")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(10, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)

        # ربط الأحداث
        self.table.doubleClicked.connect(self.view_product_details)
        self.table.selectionModel().selectionChanged.connect(self.on_selection_changed)

        # إنشاء قائمة السياق
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.total_products_label = StyledLabel(tr.get_text("total_products", "إجمالي المنتجات: 0"))
        self.total_value_label = StyledLabel(tr.get_text("total_value", "إجمالي القيمة: 0"))
        self.low_stock_label = StyledLabel(tr.get_text("low_stock_count", "مخزون منخفض: 0"))

        status_layout.addWidget(self.total_products_label)
        status_layout.addStretch()
        status_layout.addWidget(self.total_value_label)
        status_layout.addStretch()
        status_layout.addWidget(self.low_stock_label)

        layout.addLayout(status_layout)

    def load_data(self):
        """تحميل بيانات المنتجات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # تحميل الفئات
            self.load_categories()

            # استعلام المنتجات
            query = db.query(Product).filter(Product.is_deleted == False)

            # تطبيق الفلاتر
            if self.current_filter.get('search'):
                search_term = f"%{self.current_filter['search']}%"
                query = query.filter(
                    or_(
                        Product.name.like(search_term),
                        Product.code.like(search_term),
                        Product.barcode.like(search_term)
                    )
                )

            if self.current_filter.get('category_id'):
                query = query.filter(Product.category_id == self.current_filter['category_id'])

            if self.current_filter.get('status'):
                query = query.filter(Product.status == self.current_filter['status'])

            if self.current_filter.get('low_stock_only'):
                query = query.filter(Product.quantity <= Product.min_quantity)

            products = query.order_by(Product.name).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)
            self.table.setSortingEnabled(False)

            total_value = 0
            low_stock_count = 0

            for product in products:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات المنتج
                self.table.setItem(row_position, 0, QTableWidgetItem(product.code))
                self.table.setItem(row_position, 1, QTableWidgetItem(product.name))
                self.table.setItem(row_position, 2, QTableWidgetItem(product.category.name if product.category else "-"))

                # الكمية مع تلوين حسب الحالة
                quantity_item = QTableWidgetItem(str(product.quantity))
                if product.check_low_stock():
                    quantity_item.setBackground(QBrush(QColor("#ffcccc")))  # أحمر فاتح
                    low_stock_count += 1
                elif product.quantity <= 0:
                    quantity_item.setBackground(QBrush(QColor("#ff6666")))  # أحمر
                self.table.setItem(row_position, 3, quantity_item)

                self.table.setItem(row_position, 4, QTableWidgetItem(product.unit))
                self.table.setItem(row_position, 5, QTableWidgetItem(f"{product.purchase_price:.2f}"))
                self.table.setItem(row_position, 6, QTableWidgetItem(f"{product.selling_price:.2f}"))
                self.table.setItem(row_position, 7, QTableWidgetItem(f"{product.calculate_profit_margin():.1f}%"))

                # قيمة المخزون
                stock_value = product.calculate_stock_value()
                total_value += stock_value
                self.table.setItem(row_position, 8, QTableWidgetItem(f"{stock_value:.2f}"))

                # الحالة مع تلوين
                status_item = QTableWidgetItem(product.get_stock_status())
                if product.status == ProductStatus.OUT_OF_STOCK:
                    status_item.setBackground(QBrush(QColor("#ff6666")))  # أحمر
                elif product.status == ProductStatus.LOW_STOCK:
                    status_item.setBackground(QBrush(QColor("#ffcc66")))  # أصفر
                elif product.status == ProductStatus.ACTIVE:
                    status_item.setBackground(QBrush(QColor("#66ff66")))  # أخضر
                self.table.setItem(row_position, 9, status_item)

                self.table.setItem(row_position, 10, QTableWidgetItem(product.supplier.name if product.supplier else "-"))
                self.table.setItem(row_position, 11, QTableWidgetItem(product.updated_at.strftime("%Y-%m-%d") if product.updated_at else "-"))

                # تخزين معرف المنتج
                self.table.item(row_position, 0).setData(Qt.UserRole, product.id)

            self.table.setSortingEnabled(True)

            # تحديث شريط الحالة
            self.total_products_label.setText(tr.get_text("total_products", f"إجمالي المنتجات: {len(products)}"))
            self.total_value_label.setText(tr.get_text("total_value", f"إجمالي القيمة: {total_value:.2f}"))
            self.low_stock_label.setText(tr.get_text("low_stock_count", f"مخزون منخفض: {low_stock_count}"))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المنتجات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def load_categories(self):
        """تحميل فئات المنتجات"""
        try:
            db = next(get_db())
            categories = db.query(ProductCategory).filter(ProductCategory.is_deleted == False).all()

            # مسح العناصر الحالية (عدا العنصر الأول)
            while self.category_filter.count() > 1:
                self.category_filter.removeItem(1)

            # إضافة الفئات
            for category in categories:
                self.category_filter.addItem(category.name, category.id)

        except Exception as e:
            log_error(f"خطأ في تحميل فئات المنتجات: {str(e)}")

    def filter_products(self):
        """تطبيق الفلاتر على المنتجات"""
        # تحديث الفلاتر الحالية
        self.current_filter = {
            'search': self.search_input.text().strip(),
            'category_id': self.category_filter.currentData(),
            'status': self.status_filter.currentData(),
            'low_stock_only': self.low_stock_filter.isChecked()
        }

        # إعادة تحميل البيانات
        self.load_data()

    def on_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير التحديد"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.adjust_stock_btn.setEnabled(has_selection)
        self.view_movements_btn.setEnabled(has_selection)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.table.itemAt(position) is None:
            return

        menu = QMenu(self)

        view_action = menu.addAction(qta.icon('fa5s.eye'), tr.get_text("view_details", "عرض التفاصيل"))
        view_action.triggered.connect(self.view_product_details)

        edit_action = menu.addAction(qta.icon('fa5s.edit'), tr.get_text("edit", "تعديل"))
        edit_action.triggered.connect(self.edit_product)

        menu.addSeparator()

        adjust_action = menu.addAction(qta.icon('fa5s.balance-scale'), tr.get_text("adjust_stock", "تسوية المخزون"))
        adjust_action.triggered.connect(self.adjust_stock)

        movements_action = menu.addAction(qta.icon('fa5s.history'), tr.get_text("view_movements", "عرض الحركات"))
        movements_action.triggered.connect(self.view_movements)

        menu.addSeparator()

        delete_action = menu.addAction(qta.icon('fa5s.trash'), tr.get_text("delete", "حذف"))
        delete_action.triggered.connect(self.delete_product)

        menu.exec_(self.table.mapToGlobal(position))

    def add_product(self):
        """إضافة منتج جديد"""
        from .product_dialog import ProductDialog
        dialog = ProductDialog(self)
        if dialog.exec_():
            self.load_data()

    def edit_product(self):
        """تعديل منتج"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        product_id = self.table.item(row, 0).data(Qt.UserRole)

        try:
            db = next(get_db())
            product = db.query(Product).filter(Product.id == product_id).first()
            if product:
                from .product_dialog import ProductDialog
                dialog = ProductDialog(self, product)
                if dialog.exec_():
                    self.load_data()
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المنتج: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_product", "حدث خطأ أثناء تحميل بيانات المنتج")
            )

    def delete_product(self):
        """حذف منتج"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        product_id = self.table.item(row, 0).data(Qt.UserRole)
        product_name = self.table.item(row, 1).text()

        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_product", f"هل أنت متأكد من حذف المنتج '{product_name}'؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                product = db.query(Product).filter(Product.id == product_id).first()
                if product:
                    product.is_deleted = True
                    product.deleted_at = datetime.now()
                    db.commit()
                    self.load_data()
                    log_info(f"تم حذف المنتج: {product_name}")

            except Exception as e:
                log_error(f"خطأ في حذف المنتج: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_product", "حدث خطأ أثناء حذف المنتج")
                )

    def adjust_stock(self):
        """تسوية المخزون"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        product_id = self.table.item(row, 0).data(Qt.UserRole)

        from .stock_adjustment_dialog import StockAdjustmentDialog
        dialog = StockAdjustmentDialog(self, product_id)
        if dialog.exec_():
            self.load_data()

    def view_movements(self):
        """عرض حركات المخزون للمنتج"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        product_id = self.table.item(row, 0).data(Qt.UserRole)

        from .product_movements_dialog import ProductMovementsDialog
        dialog = ProductMovementsDialog(self, product_id)
        dialog.exec_()

    def view_product_details(self):
        """عرض تفاصيل المنتج المحدد"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        product_id = self.table.item(row, 0).data(Qt.UserRole)

        from .product_details_dialog import ProductDetailsDialog
        dialog = ProductDetailsDialog(self, product_id)
        dialog.exec_()

    def export_data(self):
        """تصدير بيانات المنتجات"""
        try:
            # إنشاء بيانات التصدير
            headers = []
            for col in range(self.table.columnCount()):
                headers.append(self.table.horizontalHeaderItem(col).text())

            data = []
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # تصدير إلى Excel
            excel_manager = ExcelManager.get_instance()
            excel_manager.export_to_excel(
                headers=headers,
                data=data,
                title=tr.get_text("products_report", "تقرير المنتجات"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("export_error", "حدث خطأ أثناء تصدير البيانات")
            )

class CategoriesView(QWidget):
    """
    واجهة فئات المنتجات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

class InventoryReportView(QWidget):
    """
    واجهة تقارير المخزون
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
