#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج فاتورة المبيعات
"""

from PyQt5.QtWidgets import (
    QWidget, QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDateTimeEdit, QTextEdit, QFrame, QFormLayout,
    QDialogButtonBox
)
from PyQt5.QtCore import Qt, QDateTime, pyqtSignal, QLocale
from PyQt5.QtGui import QIcon, QFont
import qtawesome as qta
from datetime import datetime
from sqlalchemy import desc
from sqlalchemy.exc import SQLAlchemyError

from src.database import get_db
from src.models import (
    Invoice, InvoiceItem, Customer, Product,
    InvoiceStatus, PaymentMethod, InvoiceType
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable, StyledDoubleSpinBox,
    Separator
)
from src.utils import (
    translation_manager as tr,
    log_info, log_error,
    config
)

class SalesInvoiceForm(QDialog):
    """
    نموذج إنشاء وتعديل فاتورة مبيعات
    """

    # إشارة عند حفظ الفاتورة بنجاح
    invoice_saved = pyqtSignal(int)  # رقم الفاتورة

    def __init__(self, invoice_id=None, parent=None):
        """
        إنشاء نموذج فاتورة مبيعات
        :param invoice_id: معرف الفاتورة (للتعديل)، أو None (للإنشاء)
        :param parent: العنصر الأب
        """
        super().__init__(parent)

        # تهيئة المتغيرات
        self.invoice_id = invoice_id
        self.db = next(get_db())
        self.invoice = None
        self.invoice_items = []
        self.customers = []
        self.products = []
        self.is_edit_mode = invoice_id is not None

        # تحميل البيانات
        self.load_data()

        # إعداد واجهة المستخدم
        self.setup_ui()

        # ملء البيانات إذا كنا في وضع التعديل
        if self.is_edit_mode:
            self.populate_form()
        else:
            # توليد رقم فاتورة جديد
            self.generate_invoice_number()

            # تعيين التاريخ والوقت الحالي
            self.datetime_edit.setDateTime(QDateTime.currentDateTime())

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل العملاء النشطين
            self.customers = self.db.query(Customer).filter(
                Customer.is_active == True
            ).order_by(Customer.name).all()

            # تحميل المنتجات النشطة
            self.products = self.db.query(Product).filter(
                Product.is_active == True,
                Product.quantity > 0  # فقط المنتجات المتوفرة في المخزون
            ).order_by(Product.name).all()

            # تحميل الفاتورة إذا كنا في وضع التعديل
            if self.is_edit_mode:
                self.invoice = self.db.query(Invoice).filter(
                    Invoice.id == self.invoice_id,
                    Invoice.invoice_type == InvoiceType.SALES,
                    Invoice.is_deleted == False
                ).first()

                if not self.invoice:
                    raise ValueError(tr.get_text("error_invoice_not_found", "الفاتورة غير موجودة"))

                # تحميل عناصر الفاتورة
                self.invoice_items = self.db.query(InvoiceItem).filter(
                    InvoiceItem.invoice_id == self.invoice_id
                ).all()

        except Exception as e:
            log_error(f"خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(
            tr.get_text("edit_invoice", "تعديل فاتورة") if self.is_edit_mode
            else tr.get_text("new_invoice", "فاتورة جديدة")
        )
        self.setMinimumSize(800, 600)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(
            tr.get_text("edit_sales_invoice", "تعديل فاتورة مبيعات") if self.is_edit_mode
            else tr.get_text("new_sales_invoice", "فاتورة مبيعات جديدة")
        )
        main_layout.addWidget(header)

        # إطار المعلومات الأساسية
        basic_info_frame = QFrame()
        basic_info_frame.setFrameShape(QFrame.StyledPanel)
        basic_info_layout = QGridLayout(basic_info_frame)

        # رقم الفاتورة
        basic_info_layout.addWidget(StyledLabel(tr.get_text("invoice_number", "رقم الفاتورة")), 0, 0)
        self.invoice_number_edit = StyledLineEdit()
        self.invoice_number_edit.setReadOnly(True)  # رقم الفاتورة يتولد تلقائياً
        basic_info_layout.addWidget(self.invoice_number_edit, 0, 1)

        # التاريخ والوقت
        basic_info_layout.addWidget(StyledLabel(tr.get_text("date_time", "التاريخ والوقت")), 0, 2)
        self.datetime_edit = QDateTimeEdit()
        self.datetime_edit.setCalendarPopup(True)
        self.datetime_edit.setDisplayFormat(self.get_datetime_format())
        basic_info_layout.addWidget(self.datetime_edit, 0, 3)

        # العميل
        basic_info_layout.addWidget(StyledLabel(tr.get_text("customer", "العميل")), 1, 0)
        self.customer_combo = StyledComboBox()
        self.populate_customers()
        basic_info_layout.addWidget(self.customer_combo, 1, 1, 1, 3)

        main_layout.addWidget(basic_info_frame)

        # جدول المنتجات
        products_label = HeaderLabel(tr.get_text("products", "المنتجات"))
        main_layout.addWidget(products_label)

        # أزرار إضافة/حذف المنتجات
        products_actions = QHBoxLayout()

        self.add_product_btn = PrimaryButton(tr.get_text("add_product", "إضافة منتج"))
        self.add_product_btn.setIcon(qta.icon("fa5s.plus"))
        self.add_product_btn.clicked.connect(self.add_product_row)
        products_actions.addWidget(self.add_product_btn)

        self.remove_product_btn = DangerButton(tr.get_text("remove_product", "حذف منتج"))
        self.remove_product_btn.setIcon(qta.icon("fa5s.minus"))
        self.remove_product_btn.clicked.connect(self.remove_product_row)
        products_actions.addWidget(self.remove_product_btn)

        products_actions.addStretch()

        main_layout.addLayout(products_actions)

        # جدول المنتجات
        self.products_table = StyledTable()
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels([
            tr.get_text("product", "المنتج"),
            tr.get_text("quantity", "الكمية"),
            tr.get_text("unit_price", "سعر الوحدة"),
            tr.get_text("discount", "الخصم %"),
            tr.get_text("tax", "الضريبة %"),
            tr.get_text("total", "الإجمالي")
        ])

        # تعيين خصائص الجدول
        self.products_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SingleSelection)
        self.products_table.setAlternatingRowColors(True)

        main_layout.addWidget(self.products_table)

        # إطار المعلومات المالية
        financial_frame = QFrame()
        financial_frame.setFrameShape(QFrame.StyledPanel)
        financial_layout = QGridLayout(financial_frame)

        # الإجمالي
        financial_layout.addWidget(StyledLabel(tr.get_text("subtotal", "المجموع الفرعي")), 0, 0)
        self.subtotal_label = StyledLabel("0.00")
        self.subtotal_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        financial_layout.addWidget(self.subtotal_label, 0, 1)

        # الخصم
        financial_layout.addWidget(StyledLabel(tr.get_text("discount", "الخصم")), 1, 0)
        discount_layout = QHBoxLayout()
        self.discount_spin = StyledDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix(" %")
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        discount_layout.addWidget(self.discount_spin)
        financial_layout.addLayout(discount_layout, 1, 1)

        # الضريبة
        financial_layout.addWidget(StyledLabel(tr.get_text("tax", "الضريبة")), 2, 0)
        tax_layout = QHBoxLayout()
        self.tax_spin = StyledDoubleSpinBox()
        self.tax_spin.setRange(0, 100)
        self.tax_spin.setSuffix(" %")
        self.tax_spin.valueChanged.connect(self.calculate_totals)
        tax_layout.addWidget(self.tax_spin)
        financial_layout.addLayout(tax_layout, 2, 1)

        # الإجمالي النهائي
        financial_layout.addWidget(StyledLabel(tr.get_text("total", "الإجمالي")), 3, 0)
        self.total_label = StyledLabel("0.00")
        self.total_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        financial_layout.addWidget(self.total_label, 3, 1)

        # المبلغ المدفوع
        financial_layout.addWidget(StyledLabel(tr.get_text("paid_amount", "المبلغ المدفوع")), 4, 0)
        self.paid_amount_spin = StyledDoubleSpinBox()
        self.paid_amount_spin.setRange(0, 1000000)
        self.paid_amount_spin.valueChanged.connect(self.calculate_remaining)
        financial_layout.addWidget(self.paid_amount_spin, 4, 1)

        # المتبقي
        financial_layout.addWidget(StyledLabel(tr.get_text("remaining", "المتبقي")), 5, 0)
        self.remaining_label = StyledLabel("0.00")
        self.remaining_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        financial_layout.addWidget(self.remaining_label, 5, 1)

        # طريقة الدفع
        financial_layout.addWidget(StyledLabel(tr.get_text("payment_method", "طريقة الدفع")), 0, 2)
        self.payment_method_combo = StyledComboBox()
        self.populate_payment_methods()
        financial_layout.addWidget(self.payment_method_combo, 0, 3)

        # الملاحظات
        financial_layout.addWidget(StyledLabel(tr.get_text("notes", "ملاحظات")), 1, 2)
        self.notes_edit = StyledTextEdit()
        financial_layout.addWidget(self.notes_edit, 1, 3, 5, 1)

        main_layout.addWidget(financial_frame)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save_invoice", "حفظ الفاتورة"))
        self.save_btn.setIcon(qta.icon("fa5s.save"))
        self.save_btn.clicked.connect(self.save_invoice)
        actions_layout.addWidget(self.save_btn)

        self.print_btn = StyledButton(tr.get_text("print_invoice", "طباعة الفاتورة"))
        self.print_btn.setIcon(qta.icon("fa5s.print"))
        self.print_btn.clicked.connect(self.print_invoice)
        actions_layout.addWidget(self.print_btn)

        self.cancel_btn = DangerButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.setIcon(qta.icon("fa5s.times"))
        self.cancel_btn.clicked.connect(self.reject)
        actions_layout.addWidget(self.cancel_btn)

        main_layout.addLayout(actions_layout)

    def get_datetime_format(self):
        """
        الحصول على تنسيق التاريخ والوقت حسب اللغة
        """
        language = tr.get_current_language()
        if language == 'ar':
            return "yyyy/MM/dd hh:mm AP"  # تنسيق عربي
        else:
            return "dd/MM/yyyy hh:mm AP"  # تنسيق إنجليزي

    def populate_customers(self):
        """ملء قائمة العملاء"""
        self.customer_combo.clear()

        # إضافة خيار فارغ
        self.customer_combo.addItem("", None)

        # إضافة العملاء
        for customer in self.customers:
            self.customer_combo.addItem(customer.name, customer.id)

    def populate_payment_methods(self):
        """ملء قائمة طرق الدفع"""
        self.payment_method_combo.clear()

        # إضافة طرق الدفع
        for method in PaymentMethod:
            self.payment_method_combo.addItem(method.value, method.name)

    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            # الحصول على آخر فاتورة
            last_invoice = self.db.query(Invoice).filter(
                Invoice.invoice_type == InvoiceType.SALES
            ).order_by(desc(Invoice.id)).first()

            # توليد رقم جديد
            if last_invoice:
                # استخراج الرقم من آخر فاتورة
                try:
                    last_number = int(last_invoice.invoice_number.split('-')[-1])
                    new_number = last_number + 1
                except:
                    # إذا فشل استخراج الرقم، استخدم التاريخ الحالي
                    new_number = 1
            else:
                new_number = 1

            # تنسيق الرقم الجديد
            today = datetime.now()
            invoice_number = f"INV-{today.year}{today.month:02d}-{new_number:04d}"

            # تعيين الرقم في الحقل
            self.invoice_number_edit.setText(invoice_number)

        except Exception as e:
            log_error(f"خطأ في توليد رقم الفاتورة: {str(e)}")
            # استخدام رقم افتراضي
            today = datetime.now()
            self.invoice_number_edit.setText(f"INV-{today.year}{today.month:02d}-0001")

    def add_product_row(self):
        """إضافة صف منتج جديد"""
        # التحقق من وجود منتجات
        if not self.products:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("no_products_available", "لا توجد منتجات متاحة")
            )
            return

        # إضافة صف جديد
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)

        # إنشاء قائمة منسدلة للمنتجات
        product_combo = StyledComboBox()
        for product in self.products:
            product_combo.addItem(f"{product.name} ({product.code})", product.id)
        product_combo.currentIndexChanged.connect(lambda: self.update_product_price(row))
        self.products_table.setCellWidget(row, 0, product_combo)

        # إنشاء حقل الكمية
        quantity_spin = StyledDoubleSpinBox()
        quantity_spin.setRange(0.01, 1000)
        quantity_spin.setValue(1)
        quantity_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 1, quantity_spin)

        # إنشاء حقل سعر الوحدة
        price_spin = StyledDoubleSpinBox()
        price_spin.setRange(0.01, 1000000)
        price_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 2, price_spin)

        # إنشاء حقل الخصم
        discount_spin = StyledDoubleSpinBox()
        discount_spin.setRange(0, 100)
        discount_spin.setSuffix(" %")
        discount_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 3, discount_spin)

        # إنشاء حقل الضريبة
        tax_spin = StyledDoubleSpinBox()
        tax_spin.setRange(0, 100)
        tax_spin.setSuffix(" %")
        tax_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 4, tax_spin)

        # إنشاء حقل الإجمالي
        total_item = QTableWidgetItem("0.00")
        total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)  # جعله غير قابل للتعديل
        self.products_table.setItem(row, 5, total_item)

        # تحديث سعر المنتج
        self.update_product_price(row)

    def update_product_price(self, row):
        """تحديث سعر المنتج عند اختياره"""
        try:
            # الحصول على المنتج المحدد
            product_combo = self.products_table.cellWidget(row, 0)
            product_id = product_combo.currentData()

            if product_id:
                # البحث عن المنتج
                product = next((p for p in self.products if p.id == product_id), None)

                if product:
                    # تعيين سعر المنتج
                    price_spin = self.products_table.cellWidget(row, 2)
                    price_spin.setValue(product.selling_price)

                    # تحديث إجمالي الصف
                    self.update_row_total(row)
        except Exception as e:
            log_error(f"خطأ في تحديث سعر المنتج: {str(e)}")

    def update_row_total(self, row):
        """تحديث إجمالي صف المنتج"""
        try:
            # الحصول على الكمية والسعر والخصم والضريبة
            quantity_spin = self.products_table.cellWidget(row, 1)
            price_spin = self.products_table.cellWidget(row, 2)
            discount_spin = self.products_table.cellWidget(row, 3)
            tax_spin = self.products_table.cellWidget(row, 4)

            quantity = quantity_spin.value()
            price = price_spin.value()
            discount_percent = discount_spin.value()
            tax_percent = tax_spin.value()

            # حساب الإجمالي
            subtotal = quantity * price
            discount_amount = (subtotal * discount_percent) / 100
            net_amount = subtotal - discount_amount
            tax_amount = (net_amount * tax_percent) / 100
            total = net_amount + tax_amount

            # تعيين الإجمالي
            total_item = self.products_table.item(row, 5)
            total_item.setText(f"{total:.2f}")

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

        except Exception as e:
            log_error(f"خطأ في تحديث إجمالي الصف: {str(e)}")

    def remove_product_row(self):
        """حذف صف المنتج المحدد"""
        # الحصول على الصف المحدد
        current_row = self.products_table.currentRow()

        if current_row >= 0:
            # حذف الصف
            self.products_table.removeRow(current_row)

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        try:
            # حساب المجموع الفرعي
            subtotal = 0
            for row in range(self.products_table.rowCount()):
                total_item = self.products_table.item(row, 5)
                if total_item:
                    subtotal += float(total_item.text())

            # تعيين المجموع الفرعي
            self.subtotal_label.setText(f"{subtotal:.2f}")

            # حساب الخصم والضريبة
            discount_percent = self.discount_spin.value()
            tax_percent = self.tax_spin.value()

            discount_amount = (subtotal * discount_percent) / 100
            net_amount = subtotal - discount_amount
            tax_amount = (net_amount * tax_percent) / 100
            total = net_amount + tax_amount

            # تعيين الإجمالي
            self.total_label.setText(f"{total:.2f}")

            # تحديث المتبقي
            self.calculate_remaining()

        except Exception as e:
            log_error(f"خطأ في حساب الإجماليات: {str(e)}")

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        try:
            # الحصول على الإجمالي والمدفوع
            total = float(self.total_label.text())
            paid = self.paid_amount_spin.value()

            # حساب المتبقي
            remaining = total - paid

            # تعيين المتبقي
            self.remaining_label.setText(f"{remaining:.2f}")

        except Exception as e:
            log_error(f"خطأ في حساب المتبقي: {str(e)}")

    def populate_form(self):
        """ملء النموذج بالبيانات الحالية للفاتورة"""
        try:
            if not self.invoice:
                return

            # تعيين رقم الفاتورة
            self.invoice_number_edit.setText(self.invoice.invoice_number)

            # تعيين التاريخ والوقت
            self.datetime_edit.setDateTime(self.invoice.invoice_date)

            # تعيين العميل
            customer_index = self.customer_combo.findData(self.invoice.customer_id)
            if customer_index >= 0:
                self.customer_combo.setCurrentIndex(customer_index)

            # تعيين طريقة الدفع
            payment_method_index = self.payment_method_combo.findData(self.invoice.payment_method.name)
            if payment_method_index >= 0:
                self.payment_method_combo.setCurrentIndex(payment_method_index)

            # تعيين الخصم والضريبة
            self.discount_spin.setValue(self.invoice.discount)
            self.tax_spin.setValue(self.invoice.tax)

            # تعيين المبلغ المدفوع
            self.paid_amount_spin.setValue(self.invoice.paid_amount)

            # تعيين الملاحظات
            if self.invoice.notes:
                self.notes_edit.setText(self.invoice.notes)

            # إضافة عناصر الفاتورة
            for item in self.invoice_items:
                # إضافة صف جديد
                row = self.products_table.rowCount()
                self.products_table.insertRow(row)

                # إنشاء قائمة منسدلة للمنتجات
                product_combo = StyledComboBox()
                for product in self.products:
                    product_combo.addItem(f"{product.name} ({product.code})", product.id)

                # تحديد المنتج
                product_index = product_combo.findData(item.product_id)
                if product_index >= 0:
                    product_combo.setCurrentIndex(product_index)

                product_combo.currentIndexChanged.connect(lambda: self.update_product_price(row))
                self.products_table.setCellWidget(row, 0, product_combo)

                # إنشاء حقل الكمية
                quantity_spin = StyledDoubleSpinBox()
                quantity_spin.setRange(0.01, 1000)
                quantity_spin.setValue(item.quantity)
                quantity_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 1, quantity_spin)

                # إنشاء حقل سعر الوحدة
                price_spin = StyledDoubleSpinBox()
                price_spin.setRange(0.01, 1000000)
                price_spin.setValue(item.unit_price)
                price_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 2, price_spin)

                # إنشاء حقل الخصم
                discount_spin = StyledDoubleSpinBox()
                discount_spin.setRange(0, 100)
                discount_spin.setSuffix(" %")
                discount_spin.setValue(item.discount)
                discount_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 3, discount_spin)

                # إنشاء حقل الضريبة
                tax_spin = StyledDoubleSpinBox()
                tax_spin.setRange(0, 100)
                tax_spin.setSuffix(" %")
                tax_spin.setValue(item.tax_rate)
                tax_spin.valueChanged.connect(lambda: self.update_row_total(row))
                self.products_table.setCellWidget(row, 4, tax_spin)

                # إنشاء حقل الإجمالي
                total = item.quantity * item.unit_price
                discount_amount = (total * item.discount) / 100
                net_amount = total - discount_amount
                tax_amount = (net_amount * item.tax_rate) / 100
                row_total = net_amount + tax_amount

                total_item = QTableWidgetItem(f"{row_total:.2f}")
                total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)  # جعله غير قابل للتعديل
                self.products_table.setItem(row, 5, total_item)

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

        except Exception as e:
            log_error(f"خطأ في ملء النموذج: {str(e)}")

    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return

            # بدء المعاملة
            self.db.begin()

            try:
                # إنشاء أو تحديث الفاتورة
                if self.is_edit_mode:
                    # تحديث الفاتورة الحالية
                    invoice = self.invoice
                else:
                    # إنشاء فاتورة جديدة
                    invoice = Invoice(
                        invoice_type=InvoiceType.SALES,
                        status=InvoiceStatus.COMPLETED
                    )

                # تعيين بيانات الفاتورة
                invoice.invoice_number = self.invoice_number_edit.text()
                invoice.invoice_date = self.datetime_edit.dateTime().toPyDateTime()
                invoice.customer_id = self.customer_combo.currentData()
                invoice.payment_method = PaymentMethod[self.payment_method_combo.currentData()]
                invoice.discount = self.discount_spin.value()
                invoice.tax = self.tax_spin.value()
                invoice.subtotal = float(self.subtotal_label.text())
                invoice.total = float(self.total_label.text())
                invoice.paid_amount = self.paid_amount_spin.value()
                invoice.notes = self.notes_edit.toPlainText()

                # حفظ الفاتورة
                if not self.is_edit_mode:
                    self.db.add(invoice)
                    self.db.flush()  # للحصول على معرف الفاتورة

                # حذف العناصر الحالية إذا كنا في وضع التعديل
                if self.is_edit_mode:
                    for item in self.invoice_items:
                        self.db.delete(item)
                    self.db.flush()

                # إضافة عناصر الفاتورة
                for row in range(self.products_table.rowCount()):
                    # الحصول على بيانات العنصر
                    product_combo = self.products_table.cellWidget(row, 0)
                    quantity_spin = self.products_table.cellWidget(row, 1)
                    price_spin = self.products_table.cellWidget(row, 2)
                    discount_spin = self.products_table.cellWidget(row, 3)
                    tax_spin = self.products_table.cellWidget(row, 4)

                    product_id = product_combo.currentData()
                    quantity = quantity_spin.value()
                    unit_price = price_spin.value()
                    discount = discount_spin.value()
                    tax_rate = tax_spin.value()

                    # إنشاء عنصر الفاتورة
                    invoice_item = InvoiceItem(
                        invoice_id=invoice.id,
                        product_id=product_id,
                        quantity=quantity,
                        unit_price=unit_price,
                        discount=discount,
                        tax_rate=tax_rate
                    )

                    # إضافة العنصر
                    self.db.add(invoice_item)

                    # تحديث كمية المنتج في المخزون
                    product = self.db.query(Product).filter(Product.id == product_id).first()
                    if product:
                        product.update_quantity(-quantity, is_purchase=False)

                # حفظ التغييرات
                self.db.commit()

                # إرسال إشارة بنجاح الحفظ
                self.invoice_saved.emit(invoice.id)

                # عرض رسالة نجاح
                QMessageBox.information(
                    self,
                    tr.get_text("success", "نجاح"),
                    tr.get_text("invoice_saved", "تم حفظ الفاتورة بنجاح")
                )

                # إغلاق النافذة
                self.accept()

            except Exception as e:
                # التراجع عن التغييرات في حالة حدوث خطأ
                self.db.rollback()
                raise e

        except Exception as e:
            log_error(f"خطأ في حفظ الفاتورة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_saving_invoice", "حدث خطأ أثناء حفظ الفاتورة")
            )

    def validate_form(self):
        """التحقق من صحة البيانات"""
        # التحقق من وجود عميل
        if not self.customer_combo.currentData():
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_customer", "يرجى اختيار العميل")
            )
            return False

        # التحقق من وجود منتجات
        if self.products_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("add_products", "يرجى إضافة منتج واحد على الأقل")
            )
            return False

        # التحقق من صحة الكميات والأسعار
        for row in range(self.products_table.rowCount()):
            quantity_spin = self.products_table.cellWidget(row, 1)
            price_spin = self.products_table.cellWidget(row, 2)

            if quantity_spin.value() <= 0:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("invalid_quantity", "يرجى إدخال كمية صحيحة")
                )
                return False

            if price_spin.value() <= 0:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("invalid_price", "يرجى إدخال سعر صحيح")
                )
                return False

        return True

    def print_invoice(self):
        """طباعة الفاتورة"""
        # التحقق من صحة البيانات
        if not self.validate_form():
            return

        # حفظ الفاتورة أولاً إذا كانت جديدة
        if not self.is_edit_mode:
            self.save_invoice()
            return

        # طباعة الفاتورة
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
            from PyQt5.QtGui import QTextDocument

            # إنشاء مستند HTML
            document = QTextDocument()

            # إنشاء محتوى الفاتورة
            html = self.generate_invoice_html()
            document.setHtml(html)

            # إنشاء طابعة
            printer = QPrinter()

            # عرض نافذة معاينة الطباعة
            preview_dialog = QPrintPreviewDialog(printer)
            preview_dialog.paintRequested.connect(lambda p: document.print_(p))
            preview_dialog.exec_()

        except Exception as e:
            log_error(f"خطأ في طباعة الفاتورة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_printing_invoice", "حدث خطأ أثناء طباعة الفاتورة")
            )

    def generate_invoice_html(self):
        """إنشاء HTML للفاتورة"""
        # الحصول على بيانات الفاتورة
        invoice_number = self.invoice_number_edit.text()
        invoice_date = self.datetime_edit.dateTime().toString(self.get_datetime_format())
        customer_name = self.customer_combo.currentText()

        # إنشاء HTML
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .invoice-header {{ text-align: center; margin-bottom: 20px; }}
                .invoice-details {{ margin-bottom: 20px; }}
                .invoice-table {{ width: 100%; border-collapse: collapse; }}
                .invoice-table th, .invoice-table td {{ border: 1px solid #ddd; padding: 8px; }}
                .invoice-table th {{ background-color: #f2f2f2; }}
                .invoice-totals {{ margin-top: 20px; }}
                .right-align {{ text-align: right; }}
            </style>
        </head>
        <body dir="{tr.get_current_language() == 'ar' and 'rtl' or 'ltr'}">
            <div class="invoice-header">
                <h1>{tr.get_text("sales_invoice", "فاتورة مبيعات")}</h1>
            </div>

            <div class="invoice-details">
                <p><strong>{tr.get_text("invoice_number", "رقم الفاتورة")}:</strong> {invoice_number}</p>
                <p><strong>{tr.get_text("date_time", "التاريخ والوقت")}:</strong> {invoice_date}</p>
                <p><strong>{tr.get_text("customer", "العميل")}:</strong> {customer_name}</p>
            </div>

            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>{tr.get_text("product", "المنتج")}</th>
                        <th>{tr.get_text("quantity", "الكمية")}</th>
                        <th>{tr.get_text("unit_price", "سعر الوحدة")}</th>
                        <th>{tr.get_text("discount", "الخصم")}</th>
                        <th>{tr.get_text("tax", "الضريبة")}</th>
                        <th>{tr.get_text("total", "الإجمالي")}</th>
                    </tr>
                </thead>
                <tbody>
        """

        # إضافة عناصر الفاتورة
        for row in range(self.products_table.rowCount()):
            product_combo = self.products_table.cellWidget(row, 0)
            quantity_spin = self.products_table.cellWidget(row, 1)
            price_spin = self.products_table.cellWidget(row, 2)
            discount_spin = self.products_table.cellWidget(row, 3)
            tax_spin = self.products_table.cellWidget(row, 4)
            total_item = self.products_table.item(row, 5)

            product_name = product_combo.currentText()
            quantity = quantity_spin.value()
            unit_price = price_spin.value()
            discount = discount_spin.value()
            tax = tax_spin.value()
            total = total_item.text()

            html += f"""
                <tr>
                    <td>{product_name}</td>
                    <td>{quantity}</td>
                    <td>{unit_price:.2f}</td>
                    <td>{discount:.2f}%</td>
                    <td>{tax:.2f}%</td>
                    <td>{total}</td>
                </tr>
            """

        # إضافة الإجماليات
        subtotal = self.subtotal_label.text()
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()
        total = self.total_label.text()
        paid = self.paid_amount_spin.value()
        remaining = self.remaining_label.text()

        html += f"""
                </tbody>
            </table>

            <div class="invoice-totals">
                <p><strong>{tr.get_text("subtotal", "المجموع الفرعي")}:</strong> {subtotal}</p>
                <p><strong>{tr.get_text("discount", "الخصم")}:</strong> {discount:.2f}%</p>
                <p><strong>{tr.get_text("tax", "الضريبة")}:</strong> {tax:.2f}%</p>
                <p><strong>{tr.get_text("total", "الإجمالي")}:</strong> {total}</p>
                <p><strong>{tr.get_text("paid_amount", "المبلغ المدفوع")}:</strong> {paid:.2f}</p>
                <p><strong>{tr.get_text("remaining", "المتبقي")}:</strong> {remaining}</p>
            </div>

            <div class="invoice-notes">
                <p><strong>{tr.get_text("notes", "ملاحظات")}:</strong></p>
                <p>{self.notes_edit.toPlainText()}</p>
            </div>
        </body>
        </html>
        """

        return html
