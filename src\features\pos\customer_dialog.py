#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة اختيار العميل لنقاط البيع
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QLineEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
import qtawesome as qta

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLabel, HeaderLabel, StyledLineEdit, StyledTable
)
from src.ui.styles.theme_colors import get_module_color, get_ui_color
from src.utils import translation_manager as tr
from src.utils.logger import log_error
from src.database import get_db
from src.models import Customer

class CustomerSelectionDialog(QDialog):
    """نافذة اختيار العميل"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.selected_customer = None
        self.setup_ui()
        self.load_customers()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(tr.get_text("select_customer", "اختيار العميل"))
        self.setMinimumSize(700, 500)
        self.setModal(True)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("customer_selection", "اختيار العميل"))
        layout.addWidget(header)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(StyledLabel(tr.get_text("search", "البحث:")))
        
        self.search_input = StyledLineEdit()
        self.search_input.setPlaceholderText(tr.get_text("search_customers", "البحث في العملاء..."))
        self.search_input.textChanged.connect(self.search_customers)
        search_layout.addWidget(self.search_input)
        
        self.search_btn = StyledButton(tr.get_text("search", "بحث"))
        self.search_btn.setIcon(qta.icon("fa5s.search"))
        self.search_btn.clicked.connect(self.search_customers)
        search_layout.addWidget(self.search_btn)
        
        layout.addLayout(search_layout)
        
        # جدول العملاء
        self.customers_table = StyledTable()
        self.customers_table.setColumnCount(5)
        self.customers_table.setHorizontalHeaderLabels([
            tr.get_text("customer_name", "اسم العميل"),
            tr.get_text("phone", "الهاتف"),
            tr.get_text("email", "البريد الإلكتروني"),
            tr.get_text("address", "العنوان"),
            tr.get_text("balance", "الرصيد")
        ])
        
        # تعيين خصائص الجدول
        header = self.customers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.customers_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.doubleClicked.connect(self.select_customer)
        
        layout.addWidget(self.customers_table)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        # زر عميل نقدي
        self.cash_customer_btn = StyledButton(tr.get_text("cash_customer", "عميل نقدي"))
        self.cash_customer_btn.setIcon(qta.icon("fa5s.money-bill"))
        self.cash_customer_btn.clicked.connect(self.select_cash_customer)
        buttons_layout.addWidget(self.cash_customer_btn)
        
        # زر عميل جديد
        self.new_customer_btn = StyledButton(tr.get_text("new_customer", "عميل جديد"))
        self.new_customer_btn.setIcon(qta.icon("fa5s.user-plus"))
        self.new_customer_btn.clicked.connect(self.create_new_customer)
        buttons_layout.addWidget(self.new_customer_btn)
        
        buttons_layout.addStretch()
        
        # زر الإلغاء
        cancel_btn = DangerButton(tr.get_text("cancel", "إلغاء"))
        cancel_btn.setIcon(qta.icon("fa5s.times"))
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        # زر الاختيار
        self.select_btn = PrimaryButton(tr.get_text("select", "اختيار"))
        self.select_btn.setIcon(qta.icon("fa5s.check"))
        self.select_btn.clicked.connect(self.select_customer)
        self.select_btn.setEnabled(False)
        buttons_layout.addWidget(self.select_btn)
        
        layout.addLayout(buttons_layout)
        
        # ربط إشارة تغيير التحديد
        self.customers_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        
    def load_customers(self, search_term=""):
        """تحميل العملاء"""
        try:
            # استعلام العملاء
            query = self.db.query(Customer).filter(Customer.is_active == True)
            
            if search_term:
                query = query.filter(
                    Customer.name.contains(search_term) |
                    Customer.phone.contains(search_term) |
                    Customer.email.contains(search_term)
                )
            
            customers = query.order_by(Customer.name).all()
            
            # تحديث الجدول
            self.customers_table.setRowCount(len(customers))
            
            for row, customer in enumerate(customers):
                # اسم العميل
                name_item = QTableWidgetItem(customer.name or "")
                name_item.setData(Qt.UserRole, customer.id)
                name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
                self.customers_table.setItem(row, 0, name_item)
                
                # الهاتف
                phone_item = QTableWidgetItem(customer.phone or "")
                phone_item.setFlags(phone_item.flags() & ~Qt.ItemIsEditable)
                self.customers_table.setItem(row, 1, phone_item)
                
                # البريد الإلكتروني
                email_item = QTableWidgetItem(customer.email or "")
                email_item.setFlags(email_item.flags() & ~Qt.ItemIsEditable)
                self.customers_table.setItem(row, 2, email_item)
                
                # العنوان
                address_item = QTableWidgetItem(customer.address or "")
                address_item.setFlags(address_item.flags() & ~Qt.ItemIsEditable)
                self.customers_table.setItem(row, 3, address_item)
                
                # الرصيد
                balance = customer.balance or 0.0
                balance_item = QTableWidgetItem(f"{balance:.2f}")
                balance_item.setFlags(balance_item.flags() & ~Qt.ItemIsEditable)
                balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                # تلوين الرصيد
                if balance > 0:
                    balance_item.setStyleSheet(f"color: {get_module_color('treasury')};")
                elif balance < 0:
                    balance_item.setStyleSheet("color: red;")
                
                self.customers_table.setItem(row, 4, balance_item)
                
        except Exception as e:
            log_error(f"خطأ في تحميل العملاء: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_customers", "حدث خطأ أثناء تحميل العملاء")
            )
    
    def search_customers(self):
        """البحث في العملاء"""
        search_term = self.search_input.text().strip()
        self.load_customers(search_term)
    
    def on_selection_changed(self):
        """عند تغيير التحديد"""
        selected_rows = self.customers_table.selectionModel().selectedRows()
        self.select_btn.setEnabled(len(selected_rows) > 0)
    
    def select_customer(self):
        """اختيار العميل"""
        try:
            selected_rows = self.customers_table.selectionModel().selectedRows()
            
            if not selected_rows:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("no_customer_selected", "لم يتم اختيار عميل")
                )
                return
            
            row = selected_rows[0].row()
            name_item = self.customers_table.item(row, 0)
            customer_id = name_item.data(Qt.UserRole)
            
            # الحصول على العميل من قاعدة البيانات
            customer = self.db.query(Customer).filter(Customer.id == customer_id).first()
            
            if customer:
                self.selected_customer = customer
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    tr.get_text("warning", "تحذير"),
                    tr.get_text("customer_not_found", "العميل غير موجود")
                )
                
        except Exception as e:
            log_error(f"خطأ في اختيار العميل: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_selecting_customer", "حدث خطأ أثناء اختيار العميل")
            )
    
    def select_cash_customer(self):
        """اختيار عميل نقدي"""
        self.selected_customer = None
        self.accept()
    
    def create_new_customer(self):
        """إنشاء عميل جديد"""
        from src.features.pos.new_customer_dialog import NewCustomerDialog
        
        dialog = NewCustomerDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            customer_data = dialog.get_customer_data()
            
            try:
                # إنشاء العميل الجديد
                new_customer = Customer(
                    name=customer_data['name'],
                    phone=customer_data['phone'],
                    email=customer_data['email'],
                    address=customer_data['address']
                )
                
                self.db.add(new_customer)
                self.db.commit()
                
                # تحديث قائمة العملاء
                self.load_customers()
                
                # اختيار العميل الجديد
                self.selected_customer = new_customer
                self.accept()
                
            except Exception as e:
                self.db.rollback()
                log_error(f"خطأ في إنشاء عميل جديد: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_creating_customer", "حدث خطأ أثناء إنشاء العميل")
                )
    
    def get_selected_customer(self):
        """الحصول على العميل المختار"""
        return self.selected_customer
