#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة المساعدة والدعم
"""

import os
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTreeWidget, QTreeWidgetItem, QSplitter, QFrame,
    QTextBrowser, QLineEdit, QWidget, QMenu, QAction,
    QToolBar, QSizePolicy
)
from PyQt5.QtCore import Qt, QSize, QUrl
from PyQt5.QtGui import QIcon, QPixmap

import qtawesome as qta

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, StyledLabel, HeaderLabel
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.help.help_manager import HelpManager

class HelpDialog(QDialog):
    """نافذة المساعدة والدعم"""
    
    def __init__(self, parent=None, category_id=None, topic_id=None):
        super().__init__(parent)
        self.help_manager = HelpManager.get_instance()
        self.current_category_id = category_id
        self.current_topic_id = topic_id
        self.setup_ui()
        self.load_help_content()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("help_support", "المساعدة والدعم"))
        self.setMinimumSize(800, 600)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # شريط الأدوات
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # زر الصفحة الرئيسية
        home_action = QAction(qta.icon('fa5s.home'), tr.get_text("home", "الرئيسية"), self)
        home_action.triggered.connect(self.go_home)
        toolbar.addAction(home_action)
        
        # زر الرجوع
        back_action = QAction(qta.icon('fa5s.arrow-left'), tr.get_text("back", "رجوع"), self)
        back_action.triggered.connect(self.go_back)
        toolbar.addAction(back_action)
        
        # زر التقدم
        forward_action = QAction(qta.icon('fa5s.arrow-right'), tr.get_text("forward", "تقدم"), self)
        forward_action.triggered.connect(self.go_forward)
        toolbar.addAction(forward_action)
        
        toolbar.addSeparator()
        
        # زر الدعم عبر الإنترنت
        online_help_action = QAction(qta.icon('fa5s.globe'), tr.get_text("online_help", "المساعدة عبر الإنترنت"), self)
        online_help_action.triggered.connect(self.open_online_help)
        toolbar.addAction(online_help_action)
        
        # زر الاتصال بالدعم
        contact_support_action = QAction(qta.icon('fa5s.envelope'), tr.get_text("contact_support", "الاتصال بالدعم"), self)
        contact_support_action.triggered.connect(self.contact_support)
        toolbar.addAction(contact_support_action)
        
        layout.addWidget(toolbar)
        
        # حقل البحث
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(10, 10, 10, 10)
        
        search_label = QLabel(tr.get_text("search", "بحث:"))
        search_layout.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr.get_text("search_help", "البحث في المساعدة..."))
        self.search_edit.returnPressed.connect(self.search_help)
        search_layout.addWidget(self.search_edit)
        
        search_button = QPushButton(tr.get_text("search", "بحث"))
        search_button.clicked.connect(self.search_help)
        search_layout.addWidget(search_button)
        
        layout.addLayout(search_layout)
        
        # المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # شجرة المحتويات
        self.tree = QTreeWidget()
        self.tree.setHeaderHidden(True)
        self.tree.setMinimumWidth(200)
        self.tree.itemClicked.connect(self.on_tree_item_clicked)
        
        # تحميل فئات المساعدة
        self.load_help_categories()
        
        splitter.addWidget(self.tree)
        
        # عارض المحتوى
        self.content_browser = QTextBrowser()
        self.content_browser.setOpenExternalLinks(True)
        
        splitter.addWidget(self.content_browser)
        
        # تعيين نسب المقسم
        splitter.setSizes([200, 600])
        
        layout.addWidget(splitter)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(10, 5, 10, 5)
        
        self.status_label = QLabel("")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # زر الإغلاق
        close_button = QPushButton(tr.get_text("close", "إغلاق"))
        close_button.clicked.connect(self.accept)
        status_layout.addWidget(close_button)
        
        layout.addLayout(status_layout)
        
    def load_help_categories(self):
        """تحميل فئات المساعدة"""
        try:
            # مسح الشجرة
            self.tree.clear()
            
            # الحصول على فئات المساعدة
            categories = self.help_manager.get_categories()
            
            # إضافة الفئات إلى الشجرة
            for category in categories:
                category_id = category.get("id")
                category_title = self.help_manager.get_category_title(category_id)
                category_icon = category.get("icon", "fa5s.question-circle")
                
                # إنشاء عنصر الفئة
                category_item = QTreeWidgetItem(self.tree)
                category_item.setText(0, category_title)
                category_item.setIcon(0, qta.icon(category_icon))
                category_item.setData(0, Qt.UserRole, {"type": "category", "id": category_id})
                
                # إضافة المواضيع إلى الفئة
                for topic in category.get("topics", []):
                    topic_id = topic.get("id")
                    topic_title = self.help_manager.get_topic_title(category_id, topic_id)
                    
                    # إنشاء عنصر الموضوع
                    topic_item = QTreeWidgetItem(category_item)
                    topic_item.setText(0, topic_title)
                    topic_item.setData(0, Qt.UserRole, {"type": "topic", "category_id": category_id, "id": topic_id})
                    
                # توسيع الفئة إذا كانت هي الفئة الحالية
                if category_id == self.current_category_id:
                    category_item.setExpanded(True)
                    
                    # تحديد الموضوع الحالي
                    if self.current_topic_id:
                        for i in range(category_item.childCount()):
                            topic_item = category_item.child(i)
                            data = topic_item.data(0, Qt.UserRole)
                            if data.get("id") == self.current_topic_id:
                                self.tree.setCurrentItem(topic_item)
                                break
                                
        except Exception as e:
            log_error(f"خطأ في تحميل فئات المساعدة: {str(e)}")
            
    def load_help_content(self):
        """تحميل محتوى المساعدة"""
        try:
            if self.current_category_id and self.current_topic_id:
                # تحميل محتوى الموضوع
                content = self.help_manager.get_topic_content(self.current_category_id, self.current_topic_id)
                if content:
                    self.content_browser.setHtml(content)
                    
                    # تحديث شريط الحالة
                    category_title = self.help_manager.get_category_title(self.current_category_id)
                    topic_title = self.help_manager.get_topic_title(self.current_category_id, self.current_topic_id)
                    self.status_label.setText(f"{category_title} > {topic_title}")
                else:
                    # عرض رسالة خطأ
                    self.show_error_content(tr.get_text("topic_not_found", "لم يتم العثور على الموضوع"))
            else:
                # عرض الصفحة الرئيسية
                self.show_home_content()
                
        except Exception as e:
            log_error(f"خطأ في تحميل محتوى المساعدة: {str(e)}")
            self.show_error_content(str(e))
            
    def show_home_content(self):
        """عرض الصفحة الرئيسية"""
        try:
            # إنشاء محتوى الصفحة الرئيسية
            html = f"""<!DOCTYPE html>
<html dir="{tr.get_direction()}">
<head>
    <meta charset="UTF-8">
    <title>{tr.get_text("help_center", "مركز المساعدة")}</title>
    <style>
        body {{
            font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
            margin: 20px;
            padding: 0;
            color: #333;
            background-color: #fff;
            direction: {tr.get_direction()};
        }}
        
        h1 {{
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }}
        
        .categories {{
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }}
        
        .category {{
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            width: calc(50% - 20px);
            box-sizing: border-box;
        }}
        
        .category h2 {{
            color: #3498db;
            margin-top: 0;
        }}
        
        .category ul {{
            padding-left: 20px;
        }}
        
        .category li {{
            margin-bottom: 5px;
        }}
        
        .category a {{
            color: #2980b9;
            text-decoration: none;
        }}
        
        .category a:hover {{
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <h1>{tr.get_text("help_center", "مركز المساعدة")}</h1>
    
    <p>{tr.get_text("help_center_intro", "مرحباً بك في مركز المساعدة. اختر أحد المواضيع التالية للحصول على المساعدة.")}</p>
    
    <div class="categories">
"""
            
            # إضافة الفئات
            for category in self.help_manager.get_categories():
                category_id = category.get("id")
                category_title = self.help_manager.get_category_title(category_id)
                
                html += f"""
        <div class="category">
            <h2>{category_title}</h2>
            <ul>
"""
                
                # إضافة المواضيع
                for topic in category.get("topics", []):
                    topic_id = topic.get("id")
                    topic_title = self.help_manager.get_topic_title(category_id, topic_id)
                    
                    html += f"""
                <li><a href="help://{category_id}/{topic_id}">{topic_title}</a></li>
"""
                
                html += """
            </ul>
        </div>
"""
            
            html += """
    </div>
</body>
</html>
"""
            
            # عرض المحتوى
            self.content_browser.setHtml(html)
            
            # تحديث شريط الحالة
            self.status_label.setText(tr.get_text("help_center", "مركز المساعدة"))
            
        except Exception as e:
            log_error(f"خطأ في عرض الصفحة الرئيسية: {str(e)}")
            self.show_error_content(str(e))
            
    def show_error_content(self, error_message: str):
        """عرض رسالة خطأ"""
        html = f"""<!DOCTYPE html>
<html dir="{tr.get_direction()}">
<head>
    <meta charset="UTF-8">
    <title>{tr.get_text("error", "خطأ")}</title>
    <style>
        body {{
            font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
            margin: 20px;
            padding: 0;
            color: #333;
            background-color: #fff;
            direction: {tr.get_direction()};
        }}
        
        .error {{
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
        }}
        
        h1 {{
            color: #dc3545;
        }}
    </style>
</head>
<body>
    <h1>{tr.get_text("error", "خطأ")}</h1>
    
    <div class="error">
        <p>{error_message}</p>
    </div>
    
    <p>{tr.get_text("try_again", "يرجى المحاولة مرة أخرى أو الاتصال بالدعم.")}</p>
</body>
</html>
"""
        
        # عرض المحتوى
        self.content_browser.setHtml(html)
        
        # تحديث شريط الحالة
        self.status_label.setText(tr.get_text("error", "خطأ"))
        
    def on_tree_item_clicked(self, item, column):
        """حدث النقر على عنصر في الشجرة"""
        try:
            # الحصول على بيانات العنصر
            data = item.data(0, Qt.UserRole)
            if data:
                item_type = data.get("type")
                
                if item_type == "topic":
                    # تحميل محتوى الموضوع
                    self.current_category_id = data.get("category_id")
                    self.current_topic_id = data.get("id")
                    self.load_help_content()
                elif item_type == "category":
                    # توسيع أو طي الفئة
                    item.setExpanded(not item.isExpanded())
                    
        except Exception as e:
            log_error(f"خطأ في معالجة النقر على عنصر الشجرة: {str(e)}")
            
    def go_home(self):
        """الانتقال إلى الصفحة الرئيسية"""
        self.current_category_id = None
        self.current_topic_id = None
        self.show_home_content()
        
    def go_back(self):
        """الرجوع إلى الصفحة السابقة"""
        self.content_browser.backward()
        
    def go_forward(self):
        """التقدم إلى الصفحة التالية"""
        self.content_browser.forward()
        
    def open_online_help(self):
        """فتح المساعدة عبر الإنترنت"""
        self.help_manager.open_external_help()
        
    def contact_support(self):
        """الاتصال بالدعم"""
        # فتح نافذة الاتصال بالدعم
        dialog = ContactSupportDialog(self)
        dialog.exec_()
        
    def search_help(self):
        """البحث في المساعدة"""
        search_text = self.search_edit.text().strip()
        if search_text:
            # تنفيذ البحث
            self.perform_search(search_text)
            
    def perform_search(self, search_text: str):
        """تنفيذ البحث"""
        try:
            # البحث في محتوى المساعدة
            results = []
            
            for category in self.help_manager.get_categories():
                category_id = category.get("id")
                
                for topic in category.get("topics", []):
                    topic_id = topic.get("id")
                    
                    # الحصول على محتوى الموضوع
                    content = self.help_manager.get_topic_content(category_id, topic_id)
                    if content and search_text.lower() in content.lower():
                        # إضافة النتيجة
                        results.append({
                            "category_id": category_id,
                            "topic_id": topic_id,
                            "category_title": self.help_manager.get_category_title(category_id),
                            "topic_title": self.help_manager.get_topic_title(category_id, topic_id)
                        })
                        
            # عرض نتائج البحث
            self.show_search_results(search_text, results)
            
        except Exception as e:
            log_error(f"خطأ في تنفيذ البحث: {str(e)}")
            
    def show_search_results(self, search_text: str, results: list):
        """عرض نتائج البحث"""
        try:
            # إنشاء محتوى نتائج البحث
            html = f"""<!DOCTYPE html>
<html dir="{tr.get_direction()}">
<head>
    <meta charset="UTF-8">
    <title>{tr.get_text("search_results", "نتائج البحث")}</title>
    <style>
        body {{
            font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
            margin: 20px;
            padding: 0;
            color: #333;
            background-color: #fff;
            direction: {tr.get_direction()};
        }}
        
        h1 {{
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }}
        
        .results {{
            margin-top: 20px;
        }}
        
        .result {{
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }}
        
        .result h3 {{
            margin: 0;
            color: #3498db;
        }}
        
        .result p {{
            margin: 5px 0 0 0;
            color: #666;
        }}
        
        .result a {{
            color: #2980b9;
            text-decoration: none;
        }}
        
        .result a:hover {{
            text-decoration: underline;
        }}
        
        .no-results {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            color: #666;
        }}
    </style>
</head>
<body>
    <h1>{tr.get_text("search_results", "نتائج البحث")}</h1>
    
    <p>{tr.get_text("search_results_for", "نتائج البحث عن:")} <strong>{search_text}</strong></p>
    
    <div class="results">
"""
            
            if results:
                # إضافة النتائج
                for result in results:
                    html += f"""
        <div class="result">
            <h3><a href="help://{result['category_id']}/{result['topic_id']}">{result['topic_title']}</a></h3>
            <p>{result['category_title']}</p>
        </div>
"""
            else:
                # لا توجد نتائج
                html += f"""
        <div class="no-results">
            <p>{tr.get_text("no_search_results", "لم يتم العثور على نتائج للبحث.")}</p>
        </div>
"""
            
            html += """
    </div>
</body>
</html>
"""
            
            # عرض المحتوى
            self.content_browser.setHtml(html)
            
            # تحديث شريط الحالة
            self.status_label.setText(tr.get_text("search_results", "نتائج البحث"))
            
        except Exception as e:
            log_error(f"خطأ في عرض نتائج البحث: {str(e)}")

class ContactSupportDialog(QDialog):
    """نافذة الاتصال بالدعم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # عنوان النافذة
        self.setWindowTitle(tr.get_text("contact_support", "الاتصال بالدعم"))
        self.setMinimumSize(400, 300)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        
        # العنوان
        header = HeaderLabel(tr.get_text("contact_support", "الاتصال بالدعم"))
        layout.addWidget(header)
        
        # معلومات الاتصال
        info_label = StyledLabel(tr.get_text("contact_support_info", "يمكنك الاتصال بفريق الدعم من خلال:"))
        layout.addWidget(info_label)
        
        # البريد الإلكتروني
        email_layout = QHBoxLayout()
        email_icon = QLabel()
        email_icon.setPixmap(qta.icon('fa5s.envelope').pixmap(QSize(16, 16)))
        email_layout.addWidget(email_icon)
        
        email_label = StyledLabel("<EMAIL>")
        email_layout.addWidget(email_label)
        
        email_layout.addStretch()
        
        layout.addLayout(email_layout)
        
        # الهاتف
        phone_layout = QHBoxLayout()
        phone_icon = QLabel()
        phone_icon.setPixmap(qta.icon('fa5s.phone').pixmap(QSize(16, 16)))
        phone_layout.addWidget(phone_icon)
        
        phone_label = StyledLabel("+1234567890")
        phone_layout.addWidget(phone_label)
        
        phone_layout.addStretch()
        
        layout.addLayout(phone_layout)
        
        # الموقع الإلكتروني
        website_layout = QHBoxLayout()
        website_icon = QLabel()
        website_icon.setPixmap(qta.icon('fa5s.globe').pixmap(QSize(16, 16)))
        website_layout.addWidget(website_icon)
        
        website_label = StyledLabel("www.example.com/support")
        website_layout.addWidget(website_label)
        
        website_layout.addStretch()
        
        layout.addLayout(website_layout)
        
        layout.addStretch()
        
        # أزرار الإغلاق
        buttons_layout = QHBoxLayout()
        
        buttons_layout.addStretch()
        
        close_button = StyledButton(tr.get_text("close", "إغلاق"))
        close_button.clicked.connect(self.accept)
        buttons_layout.addWidget(close_button)
        
        layout.addLayout(buttons_layout)
