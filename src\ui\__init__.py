#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path
from typing import Optional
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import Qt

from src.ui.windows.login_window import LoginWindow
from src.ui.windows.main_window import MainWindow
from src.ui.windows.modern_dashboard_window import ModernDashboardWindow
from src.ui.styles import style_loader
from src.ui.utils import apply_rtl_to_application, apply_rtl_to_widget, is_rtl_language, set_locale_for_language
from src.utils import (
    translation_manager as tr,
    config,
    log_info,
    log_error
)
from src.models import User
from src.database import init_db, reset_database

class AminAlHisabat:
    """
    الفئة الرئيسية للتطبيق
    تقوم بإعداد وتشغيل التطبيق
    """

    def __init__(self):
        """تهيئة التطبيق"""
        # تعطيل مقياس الشاشة عالي DPI قبل إنشاء التطبيق
        QApplication.setAttribute(Qt.AA_DisableHighDpiScaling)

        # إنشاء تطبيق Qt إذا لم يكن موجوداً
        self.app = QApplication.instance()
        if not self.app:
            self.app = QApplication(sys.argv)

        # تعيين معلومات التطبيق
        self.app.setApplicationName("Amin Al-Hisabat")
        self.app.setApplicationVersion(config.get_setting('version', '1.0.0'))
        self.app.setOrganizationName("Your Company")
        self.app.setOrganizationDomain("yourcompany.com")

        # تحميل الترجمات
        tr.load_translations()
        log_info("تم تحميل ملفات الترجمة")

        # تحميل أيقونة التطبيق
        self._load_app_icon()

        # تطبيق النمط
        self._apply_style()

        # تطبيق اتجاه RTL حسب اللغة
        self._apply_rtl()

        # النافذة الحالية
        self.current_window: Optional[MainWindow] = None
        self.login_window: Optional[LoginWindow] = None

    def _load_app_icon(self):
        """تحميل أيقونة التطبيق"""
        try:
            icon_path = os.path.join(
                Path(__file__).parent.parent.parent,
                'assets',
                'icon.png'
            )
            if os.path.exists(icon_path):
                app_icon = QIcon()
                app_icon.addPixmap(QPixmap(icon_path))
                self.app.setWindowIcon(app_icon)
                log_info("تم تحميل أيقونة التطبيق")
        except Exception as e:
            log_error(f"خطأ في تحميل أيقونة التطبيق: {str(e)}")

    def _apply_style(self):
        """تطبيق نمط التطبيق"""
        try:
            # تحميل النمط المحفوظ
            theme = config.get_setting('theme', 'dark')  # استخدام النمط الداكن افتراضياً
            style_loader.apply_theme(self.app, theme)
            log_info(f"تم تطبيق النمط: {theme}")
        except Exception as e:
            log_error(f"خطأ في تطبيق النمط: {str(e)}")

    def _apply_rtl(self):
        """تطبيق اتجاه RTL حسب اللغة"""
        try:
            # الحصول على اللغة الحالية
            current_language = config.get_setting('language', 'ar')

            # تعيين الإعدادات المحلية للغة
            set_locale_for_language(current_language)

            # تطبيق اتجاه RTL على التطبيق
            apply_rtl_to_application(self.app)

            log_info(f"تم تطبيق إعدادات اللغة: {current_language}")
        except Exception as e:
            log_error(f"خطأ في تطبيق اتجاه RTL: {str(e)}")

    def _show_login(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            self.login_window = LoginWindow()
            self.login_window.login_successful.connect(self._handle_login_success)
            self.login_window.show()
        except Exception as e:
            log_error(f"خطأ في عرض نافذة تسجيل الدخول: {str(e)}")
            sys.exit(1)

    def _handle_login_success(self, user: User):
        """
        معالجة نجاح تسجيل الدخول
        :param user: المستخدم الذي سجل دخوله
        """
        try:
            # تحديث لغة التطبيق حسب تفضيل المستخدم
            tr.set_language(user.language)

            # تحديث نمط التطبيق حسب تفضيل المستخدم (استخدام النمط الداكن إذا لم يتم تحديد نمط)
            theme = user.theme if user.theme else 'dark'
            style_loader.apply_theme(self.app, theme)

            # تحديث اتجاه RTL حسب لغة المستخدم
            set_locale_for_language(user.language)
            apply_rtl_to_application(self.app)

            # حفظ تفضيلات المستخدم
            config.set_setting('language', user.language)
            config.set_setting('theme', theme)

            # إغلاق نافذة تسجيل الدخول
            if self.login_window:
                self.login_window.close()
                self.login_window = None

            # فتح النافذة الرئيسية (استخدام لوحة التحكم الحديثة)
            self.current_window = ModernDashboardWindow(user)

            # ربط إشارة اختيار الوحدة
            self.current_window.module_selected.connect(self._handle_module_selection)

            # تطبيق اتجاه RTL على النافذة الرئيسية
            apply_rtl_to_widget(self.current_window)

            # عرض النافذة الرئيسية
            self.current_window.show()

            log_info(f"تم تسجيل دخول المستخدم: {user.username}")

        except Exception as e:
            log_error(f"خطأ في معالجة نجاح تسجيل الدخول: {str(e)}")

    def _handle_module_selection(self, module_id: str):
        """
        معالجة اختيار وحدة من لوحة التحكم
        :param module_id: معرف الوحدة المختارة
        """
        try:
            log_info(f"تم اختيار الوحدة: {module_id}")

            # يمكن هنا تنفيذ منطق التنقل بين الوحدات المختلفة
            # على سبيل المثال، فتح نوافذ مختلفة حسب الوحدة المختارة

            # TODO: تنفيذ منطق التنقل بين الوحدات

        except Exception as e:
            log_error(f"خطأ في معالجة اختيار الوحدة: {str(e)}")

    def run(self) -> int:
        """
        تشغيل التطبيق
        :return: رمز الخروج
        """
        try:
            # التحقق من وجود قاعدة البيانات وتهيئتها
            import os
            db_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Amin Al-Hisabat', 'amin_al_hisabat.db')

            # إذا لم تكن قاعدة البيانات موجودة، قم بإنشائها
            if not os.path.exists(db_path):
                log_info("قاعدة البيانات غير موجودة، جاري إنشاؤها...")
                if not init_db():
                    log_error("فشل في تهيئة قاعدة البيانات")
                    return 1
            else:
                # محاولة تهيئة قاعدة البيانات الموجودة
                try:
                    if not init_db():
                        # إذا فشلت التهيئة، حاول إعادة تهيئة قاعدة البيانات
                        log_warning = getattr(log_info, 'warning', log_info)
                        log_warning("فشل في تهيئة قاعدة البيانات الموجودة، محاولة إعادة تهيئتها...")
                        if not reset_database():
                            log_error("فشل في إعادة تهيئة قاعدة البيانات")
                            return 1
                except Exception as db_error:
                    # إذا حدث خطأ، حاول إعادة تهيئة قاعدة البيانات
                    log_error(f"خطأ في تهيئة قاعدة البيانات: {str(db_error)}")
                    log_info("محاولة إعادة تهيئة قاعدة البيانات...")
                    if not reset_database():
                        log_error("فشل في إعادة تهيئة قاعدة البيانات")
                        return 1

            # عرض نافذة تسجيل الدخول
            self._show_login()

            # تشغيل حلقة الأحداث
            return self.app.exec_()

        except Exception as e:
            log_error(f"خطأ في تشغيل التطبيق: {str(e)}")
            return 1

def run_application() -> int:
    """
    دالة مساعدة لتشغيل التطبيق
    :return: رمز الخروج
    """
    try:
        # تعطيل مقياس الشاشة عالي DPI مرة أخرى للتأكد
        QApplication.setAttribute(Qt.AA_DisableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_Use96Dpi, True)
        
        # الحصول على مثيل التطبيق الحالي أو إنشاء واحد جديد
        app = QApplication.instance()
        if not app:
            app = AminAlHisabat()
        else:
            app = AminAlHisabat()
            
        return app.run()
    except Exception as e:
        log_error(f"خطأ في تشغيل التطبيق: {str(e)}")
        return 1