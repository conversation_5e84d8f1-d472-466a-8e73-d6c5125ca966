#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نموذج الفاتورة الأساسي المشترك بين جميع أنواع الفواتير
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDateTimeEdit, QTextEdit, QFrame, QFormLayout
)
from PyQt5.QtCore import Qt, QDateTime, pyqtSignal
from PyQt5.QtGui import QIcon
import qtawesome as qta
from datetime import datetime
from sqlalchemy import desc
from sqlalchemy.exc import SQLAlchemyError

from src.database import get_db
from src.models import (
    Invoice, InvoiceItem, Customer, Supplier, Product,
    InvoiceStatus, PaymentMethod, InvoiceType
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledDoubleSpinBox, StyledLabel, HeaderLabel,
    StyledTable, Separator
)
from src.utils import (
    translation_manager as tr,
    log_info, log_error,
    config
)

class BaseInvoiceForm(QDialog):
    """
    نموذج الفاتورة الأساسي المشترك بين جميع أنواع الفواتير
    """

    # إشارة عند حفظ الفاتورة بنجاح
    invoice_saved = pyqtSignal(int)  # رقم الفاتورة

    def __init__(self, invoice_id=None, parent=None, invoice_type=InvoiceType.SALES):
        """
        إنشاء نموذج فاتورة
        :param invoice_id: معرف الفاتورة (للتعديل)، أو None (للإنشاء)
        :param parent: العنصر الأب
        :param invoice_type: نوع الفاتورة
        """
        super().__init__(parent)

        # تهيئة المتغيرات
        self.invoice_id = invoice_id
        self.db = next(get_db())
        self.invoice = None
        self.invoice_items = []
        self.products = []
        self.is_edit_mode = invoice_id is not None
        self.invoice_type = invoice_type

        # تحميل البيانات
        self.load_data()

        # إعداد واجهة المستخدم
        self.setup_ui()

        # ملء البيانات إذا كنا في وضع التعديل
        if self.is_edit_mode:
            self.populate_form()
        else:
            # توليد رقم فاتورة جديد
            self.generate_invoice_number()

            # تعيين التاريخ والوقت الحالي
            self.datetime_edit.setDateTime(QDateTime.currentDateTime())

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def setup_ui(self):
        """إعداد واجهة المستخدم - الهيكل الأساسي المشترك"""
        # إعداد النافذة
        self.setWindowTitle(self.get_window_title())
        self.setMinimumSize(800, 600)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(self.get_header_title())
        main_layout.addWidget(header)

        # إطار المعلومات الأساسية
        basic_info_frame = QFrame()
        basic_info_frame.setFrameShape(QFrame.StyledPanel)
        basic_info_layout = QGridLayout(basic_info_frame)

        # رقم الفاتورة
        basic_info_layout.addWidget(StyledLabel(tr.get_text("invoice_number", "رقم الفاتورة")), 0, 0)
        self.invoice_number_edit = StyledLineEdit()
        self.invoice_number_edit.setReadOnly(True)  # رقم الفاتورة يتولد تلقائياً
        basic_info_layout.addWidget(self.invoice_number_edit, 0, 1)

        # التاريخ والوقت
        basic_info_layout.addWidget(StyledLabel(tr.get_text("date_time", "التاريخ والوقت")), 0, 2)
        self.datetime_edit = QDateTimeEdit()
        self.datetime_edit.setCalendarPopup(True)
        self.datetime_edit.setDisplayFormat(self.get_datetime_format())
        basic_info_layout.addWidget(self.datetime_edit, 0, 3)

        # حقول إضافية خاصة بنوع الفاتورة (العميل/المورد)
        self.setup_entity_fields(basic_info_layout)

        main_layout.addWidget(basic_info_frame)

        # جدول المنتجات
        products_label = HeaderLabel(tr.get_text("products", "المنتجات"))
        main_layout.addWidget(products_label)

        # أزرار إضافة/حذف المنتجات
        products_actions = QHBoxLayout()

        self.add_product_btn = PrimaryButton(tr.get_text("add_product", "إضافة منتج"))
        self.add_product_btn.setIcon(qta.icon("fa5s.plus"))
        self.add_product_btn.clicked.connect(self.add_product_row)
        products_actions.addWidget(self.add_product_btn)

        self.remove_product_btn = DangerButton(tr.get_text("remove_product", "حذف منتج"))
        self.remove_product_btn.setIcon(qta.icon("fa5s.minus"))
        self.remove_product_btn.clicked.connect(self.remove_product_row)
        products_actions.addWidget(self.remove_product_btn)

        products_actions.addStretch()

        main_layout.addLayout(products_actions)

        # جدول المنتجات
        self.products_table = StyledTable()
        self.setup_products_table()
        main_layout.addWidget(self.products_table)

        # إطار المعلومات المالية
        financial_frame = QFrame()
        financial_frame.setFrameShape(QFrame.StyledPanel)
        financial_layout = QGridLayout(financial_frame)

        # الإجمالي
        financial_layout.addWidget(StyledLabel(tr.get_text("subtotal", "المجموع الفرعي")), 0, 0)
        self.subtotal_label = StyledLabel("0.00")
        self.subtotal_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        financial_layout.addWidget(self.subtotal_label, 0, 1)

        # الخصم
        financial_layout.addWidget(StyledLabel(tr.get_text("discount", "الخصم")), 1, 0)
        discount_layout = QHBoxLayout()
        self.discount_spin = StyledDoubleSpinBox()
        self.discount_spin.setRange(0, 100)
        self.discount_spin.setSuffix(" %")
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        discount_layout.addWidget(self.discount_spin)
        financial_layout.addLayout(discount_layout, 1, 1)

        # الضريبة
        financial_layout.addWidget(StyledLabel(tr.get_text("tax", "الضريبة")), 2, 0)
        tax_layout = QHBoxLayout()
        self.tax_spin = StyledDoubleSpinBox()
        self.tax_spin.setRange(0, 100)
        self.tax_spin.setSuffix(" %")
        self.tax_spin.valueChanged.connect(self.calculate_totals)
        tax_layout.addWidget(self.tax_spin)
        financial_layout.addLayout(tax_layout, 2, 1)

        # الإجمالي النهائي
        financial_layout.addWidget(StyledLabel(tr.get_text("total", "الإجمالي")), 3, 0)
        self.total_label = StyledLabel("0.00")
        self.total_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        financial_layout.addWidget(self.total_label, 3, 1)

        # المبلغ المدفوع
        financial_layout.addWidget(StyledLabel(tr.get_text("paid_amount", "المبلغ المدفوع")), 4, 0)
        self.paid_amount_spin = StyledDoubleSpinBox()
        self.paid_amount_spin.setRange(0, 1000000)
        self.paid_amount_spin.valueChanged.connect(self.calculate_remaining)
        financial_layout.addWidget(self.paid_amount_spin, 4, 1)

        # المتبقي
        financial_layout.addWidget(StyledLabel(tr.get_text("remaining", "المتبقي")), 5, 0)
        self.remaining_label = StyledLabel("0.00")
        self.remaining_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        financial_layout.addWidget(self.remaining_label, 5, 1)

        # طريقة الدفع
        financial_layout.addWidget(StyledLabel(tr.get_text("payment_method", "طريقة الدفع")), 0, 2)
        self.payment_method_combo = StyledComboBox()
        self.populate_payment_methods()
        financial_layout.addWidget(self.payment_method_combo, 0, 3)

        # الملاحظات
        financial_layout.addWidget(StyledLabel(tr.get_text("notes", "ملاحظات")), 1, 2)
        self.notes_edit = StyledTextEdit()
        financial_layout.addWidget(self.notes_edit, 1, 3, 5, 1)

        main_layout.addWidget(financial_frame)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.save_btn = PrimaryButton(tr.get_text("save_invoice", "حفظ الفاتورة"))
        self.save_btn.setIcon(qta.icon("fa5s.save"))
        self.save_btn.clicked.connect(self.save_invoice)
        actions_layout.addWidget(self.save_btn)

        self.print_btn = StyledButton(tr.get_text("print_invoice", "طباعة الفاتورة"))
        self.print_btn.setIcon(qta.icon("fa5s.print"))
        self.print_btn.clicked.connect(self.print_invoice)
        actions_layout.addWidget(self.print_btn)

        self.cancel_btn = DangerButton(tr.get_text("cancel", "إلغاء"))
        self.cancel_btn.setIcon(qta.icon("fa5s.times"))
        self.cancel_btn.clicked.connect(self.reject)
        actions_layout.addWidget(self.cancel_btn)

        main_layout.addLayout(actions_layout)

    def setup_entity_fields(self, layout):
        """إعداد حقول الكيان (العميل/المورد) - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def setup_products_table(self):
        """إعداد جدول المنتجات"""
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels([
            tr.get_text("product", "المنتج"),
            tr.get_text("quantity", "الكمية"),
            tr.get_text("unit_price", "سعر الوحدة"),
            tr.get_text("discount", "الخصم %"),
            tr.get_text("tax", "الضريبة %"),
            tr.get_text("total", "الإجمالي")
        ])

        # تعيين خصائص الجدول
        self.products_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SingleSelection)
        self.products_table.setAlternatingRowColors(True)

    def get_window_title(self):
        """الحصول على عنوان النافذة - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def get_header_title(self):
        """الحصول على عنوان الرأس - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def get_datetime_format(self):
        """
        الحصول على تنسيق التاريخ والوقت حسب اللغة
        """
        language = tr.get_current_language()
        if language == 'ar':
            return "yyyy/MM/dd hh:mm AP"  # تنسيق عربي
        else:
            return "dd/MM/yyyy hh:mm AP"  # تنسيق إنجليزي

    def populate_payment_methods(self):
        """ملء قائمة طرق الدفع"""
        self.payment_method_combo.clear()

        # إضافة طرق الدفع
        for method in PaymentMethod:
            self.payment_method_combo.addItem(method.value, method.name)

    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def add_product_row(self):
        """إضافة صف منتج جديد"""
        # التحقق من وجود منتجات
        if not self.products:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("no_products_available", "لا توجد منتجات متاحة")
            )
            return

        # إضافة صف جديد
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)

        # إنشاء قائمة منسدلة للمنتجات
        product_combo = StyledComboBox()
        for product in self.products:
            product_combo.addItem(f"{product.name} ({product.code})", product.id)
        product_combo.currentIndexChanged.connect(lambda: self.update_product_price(row))
        self.products_table.setCellWidget(row, 0, product_combo)

        # إنشاء حقل الكمية
        quantity_spin = StyledDoubleSpinBox()
        quantity_spin.setRange(0.01, 1000)
        quantity_spin.setValue(1)
        quantity_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 1, quantity_spin)

        # إنشاء حقل سعر الوحدة
        price_spin = StyledDoubleSpinBox()
        price_spin.setRange(0.01, 1000000)
        price_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 2, price_spin)

        # إنشاء حقل الخصم
        discount_spin = StyledDoubleSpinBox()
        discount_spin.setRange(0, 100)
        discount_spin.setSuffix(" %")
        discount_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 3, discount_spin)

        # إنشاء حقل الضريبة
        tax_spin = StyledDoubleSpinBox()
        tax_spin.setRange(0, 100)
        tax_spin.setSuffix(" %")
        tax_spin.valueChanged.connect(lambda: self.update_row_total(row))
        self.products_table.setCellWidget(row, 4, tax_spin)

        # إنشاء حقل الإجمالي
        total_item = QTableWidgetItem("0.00")
        total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)  # جعله غير قابل للتعديل
        self.products_table.setItem(row, 5, total_item)

        # تحديث سعر المنتج
        self.update_product_price(row)

    def update_product_price(self, row):
        """تحديث سعر المنتج عند اختياره - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def update_row_total(self, row):
        """تحديث إجمالي صف المنتج"""
        try:
            # الحصول على الكمية والسعر والخصم والضريبة
            quantity_spin = self.products_table.cellWidget(row, 1)
            price_spin = self.products_table.cellWidget(row, 2)
            discount_spin = self.products_table.cellWidget(row, 3)
            tax_spin = self.products_table.cellWidget(row, 4)

            quantity = quantity_spin.value()
            price = price_spin.value()
            discount_percent = discount_spin.value()
            tax_percent = tax_spin.value()

            # حساب الإجمالي
            subtotal = quantity * price
            discount_amount = (subtotal * discount_percent) / 100
            net_amount = subtotal - discount_amount
            tax_amount = (net_amount * tax_percent) / 100
            total = net_amount + tax_amount

            # تعيين الإجمالي
            total_item = self.products_table.item(row, 5)
            total_item.setText(f"{total:.2f}")

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

        except Exception as e:
            log_error(f"خطأ في تحديث إجمالي الصف: {str(e)}")

    def remove_product_row(self):
        """حذف صف المنتج المحدد"""
        # الحصول على الصف المحدد
        current_row = self.products_table.currentRow()

        if current_row >= 0:
            # حذف الصف
            self.products_table.removeRow(current_row)

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        try:
            # حساب المجموع الفرعي
            subtotal = 0
            for row in range(self.products_table.rowCount()):
                total_item = self.products_table.item(row, 5)
                if total_item:
                    subtotal += float(total_item.text())

            # تعيين المجموع الفرعي
            self.subtotal_label.setText(f"{subtotal:.2f}")

            # حساب الخصم والضريبة
            discount_percent = self.discount_spin.value()
            tax_percent = self.tax_spin.value()

            discount_amount = (subtotal * discount_percent) / 100
            net_amount = subtotal - discount_amount
            tax_amount = (net_amount * tax_percent) / 100
            total = net_amount + tax_amount

            # تعيين الإجمالي
            self.total_label.setText(f"{total:.2f}")

            # تحديث المتبقي
            self.calculate_remaining()

        except Exception as e:
            log_error(f"خطأ في حساب الإجماليات: {str(e)}")

    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        try:
            # الحصول على الإجمالي والمدفوع
            total = float(self.total_label.text())
            paid = self.paid_amount_spin.value()

            # حساب المتبقي
            remaining = total - paid

            # تعيين المتبقي
            self.remaining_label.setText(f"{remaining:.2f}")

        except Exception as e:
            log_error(f"خطأ في حساب المتبقي: {str(e)}")

    def populate_form(self):
        """ملء النموذج بالبيانات الحالية للفاتورة - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def save_invoice(self):
        """حفظ الفاتورة - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def validate_form(self):
        """التحقق من صحة البيانات - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")

    def print_invoice(self):
        """طباعة الفاتورة"""
        # التحقق من صحة البيانات
        if not self.validate_form():
            return

        # حفظ الفاتورة أولاً إذا كانت جديدة
        if not self.is_edit_mode:
            self.save_invoice()
            return

        # طباعة الفاتورة
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
            from PyQt5.QtGui import QTextDocument

            # إنشاء مستند HTML
            document = QTextDocument()

            # إنشاء محتوى الفاتورة
            html = self.generate_invoice_html()
            document.setHtml(html)

            # إنشاء طابعة
            printer = QPrinter()

            # عرض نافذة معاينة الطباعة
            preview_dialog = QPrintPreviewDialog(printer)
            preview_dialog.paintRequested.connect(lambda p: document.print_(p))
            preview_dialog.exec_()

        except Exception as e:
            log_error(f"خطأ في طباعة الفاتورة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_printing_invoice", "حدث خطأ أثناء طباعة الفاتورة")
            )

    def generate_invoice_html(self):
        """إنشاء HTML للفاتورة - يجب تنفيذها في الفئات الفرعية"""
        raise NotImplementedError("يجب تنفيذ هذه الدالة في الفئات الفرعية")
