#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QWidget, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QSpinBox, QDoubleSpinBox, QDateEdit, QLabel, QFrame,
    QMessageBox, QTableWidget, QTableWidgetItem, QToolButton,
    QCheckBox, QRadioButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QLocale
from PyQt5.QtGui import QIcon, QFont, QFontDatabase
from src.utils import translation_manager as tr
import os

# تحميل الخطوط
def load_fonts():
    """تحميل الخطوط المخصصة"""
    font_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'assets', 'fonts')

    # قائمة الخطوط المدعومة
    fonts = [
        'Cairo-Regular.ttf',
        'Cairo-Bold.ttf',
        'Cairo-SemiBold.ttf',
        'Tajawal-Regular.ttf',
        'Tajawal-Bold.ttf',
        'Tajawal-Medium.ttf',
        'Roboto-Regular.ttf',
        'Roboto-Bold.ttf',
        'Roboto-Medium.ttf'
    ]

    # تحميل الخطوط
    for font in fonts:
        font_path = os.path.join(font_dir, font)
        if os.path.exists(font_path):
            QFontDatabase.addApplicationFont(font_path)

# تحميل الخطوط عند استيراد الوحدة
load_fonts()

# الحصول على الخط المناسب حسب اللغة
def get_font_family():
    """الحصول على عائلة الخط المناسبة حسب اللغة الحالية"""
    language = tr.get_current_language()
    if language == 'ar':
        return "Cairo"
    else:
        return "Roboto"

# الحصول على حجم الخط المناسب
def get_font_size(size_name):
    """الحصول على حجم الخط المناسب"""
    sizes = {
        'small': 8,
        'normal': 10,
        'medium': 12,
        'large': 14,
        'header': 16,
        'title': 18,
        'subheader': 14
    }
    return sizes.get(size_name, 10)

class StyledButton(QPushButton):
    """زر مخصص مع تصميم موحد"""

    def __init__(self, text="", icon=None, parent=None):
        super().__init__(text, parent)
        if icon:
            self.setIcon(QIcon(icon))
        self.setCursor(Qt.PointingHandCursor)
        self.setFont(QFont(get_font_family(), get_font_size('normal')))

        # تعيين خاصية RTL حسب اللغة
        language = tr.get_current_language()
        if language == 'ar':
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LeftToRight)

class IconButton(QToolButton):
    """زر أيقونة"""

    def __init__(self, icon=None, tooltip="", parent=None):
        super().__init__(parent)
        if isinstance(icon, str):
            self.setIcon(QIcon(icon))
        elif isinstance(icon, QIcon):
            self.setIcon(icon)
        if tooltip:
            self.setToolTip(tooltip)
        self.setCursor(Qt.PointingHandCursor)
        self.setIconSize(QSize(24, 24))

class PrimaryButton(StyledButton):
    """زر رئيسي بلون مميز"""

    def __init__(self, text="", icon=None, parent=None):
        super().__init__(text, icon, parent)
        self.setProperty('class', 'primary')

class SecondaryButton(StyledButton):
    """زر ثانوي"""

    def __init__(self, text="", icon=None, parent=None):
        super().__init__(text, icon, parent)
        self.setProperty('class', 'secondary')

class DangerButton(StyledButton):
    """زر خطر بلون أحمر"""

    def __init__(self, text="", icon=None, parent=None):
        super().__init__(text, icon, parent)
        self.setProperty('class', 'danger')

class StyledLineEdit(QLineEdit):
    """حقل نص مخصص مع تصميم موحد"""

    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder)
        self.setFont(QFont(get_font_family(), get_font_size('normal')))

        # تعيين محاذاة النص حسب اللغة
        language = tr.get_current_language()
        if language == 'ar':
            self.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.setLayoutDirection(Qt.LeftToRight)

class StyledTextEdit(QTextEdit):
    """مربع نص متعدد الأسطر مع تصميم موحد"""

    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        self.setPlaceholderText(placeholder)
        self.setFont(QFont(get_font_family(), get_font_size('normal')))

        # تعيين اتجاه النص حسب اللغة
        language = tr.get_current_language()
        if language == 'ar':
            self.setLayoutDirection(Qt.RightToLeft)
            self.setAlignment(Qt.AlignRight)
        else:
            self.setLayoutDirection(Qt.LeftToRight)
            self.setAlignment(Qt.AlignLeft)

class StyledComboBox(QComboBox):
    """قائمة منسدلة مخصصة مع تصميم موحد"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFont(QFont(get_font_family(), get_font_size('normal')))

        # تعيين اتجاه النص حسب اللغة
        language = tr.get_current_language()
        if language == 'ar':
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LeftToRight)

class StyledCheckBox(QCheckBox):
    """صندوق اختيار مخصص"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setFont(QFont("Cairo", 10))
        self.setCursor(Qt.PointingHandCursor)

class StyledRadioButton(QRadioButton):
    """زر راديو مخصص"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setFont(QFont("Cairo", 10))
        self.setCursor(Qt.PointingHandCursor)

class NumericSpinBox(QSpinBox):
    """مربع أرقام صحيحة مخصص"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFont(QFont("Cairo", 10))
        self.setButtonSymbols(QSpinBox.NoButtons)
        self.setAlignment(Qt.AlignRight)

class DecimalSpinBox(QDoubleSpinBox):
    """مربع أرقام عشرية مخصص"""

    def __init__(self, parent=None, decimals=2):
        super().__init__(parent)
        self.setFont(QFont("Cairo", 10))
        self.setDecimals(decimals)
        self.setButtonSymbols(QDoubleSpinBox.NoButtons)
        self.setAlignment(Qt.AlignRight)

class StyledDoubleSpinBox(QDoubleSpinBox):
    """مربع أرقام عشرية مخصص مع تصميم موحد"""

    def __init__(self, parent=None, decimals=2):
        super().__init__(parent)
        self.setFont(QFont(get_font_family(), get_font_size('normal')))
        self.setDecimals(decimals)
        self.setButtonSymbols(QDoubleSpinBox.NoButtons)

        # تعيين محاذاة النص حسب اللغة
        language = tr.get_current_language()
        if language == 'ar':
            self.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.setLayoutDirection(Qt.LeftToRight)

class DateEdit(QDateEdit):
    """حقل تاريخ مخصص"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFont(QFont("Cairo", 10))
        self.setCalendarPopup(True)
        self.setDisplayFormat("yyyy-MM-dd")

class StyledDateEdit(DateEdit):
    """حقل تاريخ مخصص مع تصميم موحد"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setProperty('class', 'styled')

class StyledLabel(QLabel):
    """تسمية مخصصة مع تصميم موحد"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setFont(QFont(get_font_family(), get_font_size('normal')))

        # تعيين اتجاه النص حسب اللغة
        language = tr.get_current_language()
        if language == 'ar':
            self.setLayoutDirection(Qt.RightToLeft)
            self.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        else:
            self.setLayoutDirection(Qt.LeftToRight)
            self.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

class HeaderLabel(StyledLabel):
    """تسمية عنوان مع خط كبير"""

    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        font = QFont(get_font_family(), get_font_size('header'))
        font.setBold(True)
        self.setFont(font)

class Separator(QFrame):
    """خط فاصل أفقي"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.HLine)
        self.setFrameShadow(QFrame.Sunken)

class StyledTable(QTableWidget):
    """جدول مخصص مع تصميم وميزات موحدة"""

    row_clicked = pyqtSignal(int)  # إشارة عند النقر على صف

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()

    def setup_table(self):
        """إعداد خصائص الجدول"""
        # التصميم العام
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)

        # الخط
        header_font = QFont(get_font_family(), get_font_size('normal'))
        header_font.setBold(True)
        self.horizontalHeader().setFont(header_font)
        self.setFont(QFont(get_font_family(), get_font_size('normal')))

        # الأحجام
        self.verticalHeader().setDefaultSectionSize(30)
        self.horizontalHeader().setDefaultSectionSize(120)
        self.verticalHeader().setVisible(False)

        # تعيين اتجاه النص حسب اللغة
        language = tr.get_current_language()
        if language == 'ar':
            self.setLayoutDirection(Qt.RightToLeft)
        else:
            self.setLayoutDirection(Qt.LeftToRight)

        # التفاعل
        self.cellClicked.connect(lambda row, _: self.row_clicked.emit(row))

    def set_headers(self, headers):
        """تعيين عناوين الأعمدة"""
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

    def add_row(self, data):
        """إضافة صف جديد"""
        row = self.rowCount()
        self.insertRow(row)
        for col, value in enumerate(data):
            item = QTableWidgetItem(str(value))
            item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, col, item)

    def clear_contents(self):
        """مسح محتويات الجدول"""
        self.setRowCount(0)

    def get_selected_row_data(self):
        """الحصول على بيانات الصف المحدد"""
        row = self.currentRow()
        if row >= 0:
            return [self.item(row, col).text()
                   for col in range(self.columnCount())]
        return None

# تصدير الأصناف المتاحة
__all__ = [
    'StyledButton',
    'IconButton',
    'PrimaryButton',
    'SecondaryButton',
    'DangerButton',
    'StyledLineEdit',
    'StyledTextEdit',
    'StyledComboBox',
    'StyledCheckBox',
    'StyledRadioButton',
    'NumericSpinBox',
    'DecimalSpinBox',
    'StyledDoubleSpinBox',
    'DateEdit',
    'StyledDateEdit',
    'StyledLabel',
    'HeaderLabel',
    'Separator',
    'StyledTable'
]