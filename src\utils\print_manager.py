#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الطباعة
يوفر وظائف لطباعة الفواتير والتقارير وتصديرها إلى PDF
"""

import os
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QFileDialog, QMessageBox
)
from PyQt5.QtPrintSupport import (
    QPrinter, QPrintDialog, QPrintPreviewDialog
)
from PyQt5.QtGui import QTextDocument, QPainter
from PyQt5.QtCore import QUrl, QSizeF, Qt, QMarginsF
# Comentamos esta importación ya que no está disponible
# from PyQt5.QtWebEngineWidgets import QWebEngineView

from src.utils import translation_manager as tr
from src.utils import config
from src.utils.logger import log_info, log_error
from src.utils.currency import CurrencyManager
from src.utils.pos_printer import POSPrinter

class PrintManager:
    """مدير الطباعة"""

    _instance = None

    @classmethod
    def get_instance(cls):
        """الحصول على نسخة وحيدة من مدير الطباعة"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """تهيئة مدير الطباعة"""
        self.currency_manager = CurrencyManager.get_instance()
        self.default_export_path = config.get_setting('export_path', os.path.expanduser('~'))
        self.pos_printer = POSPrinter()
        self.printer_type = config.get_setting('printer_type', 'normal')  # normal, pos

    def get_company_info(self):
        """الحصول على معلومات الشركة من الإعدادات"""
        return {
            'name': config.get_setting('company_name', ''),
            'address': config.get_setting('company_address', ''),
            'phone': config.get_setting('company_phone', ''),
            'email': config.get_setting('company_email', ''),
            'vat': config.get_setting('company_vat', ''),
            'cr': config.get_setting('company_cr', ''),
            'logo': config.get_setting('company_logo', '')
        }

    def format_currency(self, amount):
        """تنسيق المبلغ حسب إعدادات العملة"""
        return self.currency_manager.format_amount(amount)

    def get_html_template(self, template_name):
        """الحصول على قالب HTML"""
        template_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'templates')
        template_path = os.path.join(template_dir, f"{template_name}.html")

        if not os.path.exists(template_path):
            log_error(f"قالب الطباعة غير موجود: {template_path}")
            return None

        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            log_error(f"خطأ في قراءة قالب الطباعة: {str(e)}")
            return None

    def render_template(self, template_name, context):
        """تحويل قالب HTML باستخدام السياق المعطى"""
        template = self.get_html_template(template_name)
        if not template:
            return None

        # إضافة معلومات الشركة إلى السياق
        context['company'] = self.get_company_info()

        # إضافة التاريخ والوقت الحالي
        context['current_date'] = datetime.now().strftime('%Y-%m-%d')
        context['current_time'] = datetime.now().strftime('%H:%M:%S')

        # إضافة اتجاه النص حسب اللغة
        context['direction'] = tr.get_direction()
        context['text_align'] = tr.get_text('text_align')
        context['text_align_reverse'] = tr.get_text('text_align_reverse')

        # استبدال المتغيرات في القالب
        html = template
        for key, value in context.items():
            if isinstance(value, str):
                html = html.replace(f"{{{{{key}}}}}", value)
            elif isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    html = html.replace(f"{{{{{key}.{sub_key}}}}}", str(sub_value))

        return html

    def print_document(self, html_content, title=""):
        """طباعة مستند HTML"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setHtml(html_content)

            # إعداد الطابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(QMarginsF(10, 10, 10, 10), QPrinter.Millimeter)
            if title:
                printer.setDocName(title)

            # عرض مربع حوار الطباعة
            dialog = QPrintDialog(printer)
            if dialog.exec_() == QPrintDialog.Accepted:
                document.print_(printer)
                log_info(f"تمت طباعة المستند: {title}")
                return True

            return False

        except Exception as e:
            log_error(f"خطأ في طباعة المستند: {str(e)}")
            return False

    def print_preview(self, html_content, title="", parent=None):
        """عرض معاينة الطباعة"""
        try:
            # إنشاء مستند نصي
            document = QTextDocument()
            document.setHtml(html_content)

            # إعداد الطابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(QMarginsF(10, 10, 10, 10), QPrinter.Millimeter)
            if title:
                printer.setDocName(title)

            # عرض مربع حوار معاينة الطباعة
            preview = QPrintPreviewDialog(printer, parent)
            preview.paintRequested.connect(lambda p: document.print_(p))

            if preview.exec_() == QPrintPreviewDialog.Accepted:
                log_info(f"تمت معاينة المستند: {title}")
                return True

            return False

        except Exception as e:
            log_error(f"خطأ في معاينة المستند: {str(e)}")
            return False

    def export_to_pdf(self, html_content, title="", file_path=None, parent=None):
        """تصدير مستند HTML إلى PDF"""
        try:
            if not file_path:
                # عرض مربع حوار حفظ الملف
                file_path, _ = QFileDialog.getSaveFileName(
                    parent,
                    tr.get_text("save_pdf_file", "حفظ ملف PDF"),
                    os.path.join(self.default_export_path, f"{title}.pdf"),
                    "PDF Files (*.pdf)"
                )

                if not file_path:
                    return False

            # إنشاء مستند نصي
            document = QTextDocument()
            document.setHtml(html_content)

            # إعداد الطابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(QMarginsF(10, 10, 10, 10), QPrinter.Millimeter)

            # طباعة المستند إلى PDF
            document.print_(printer)

            log_info(f"تم تصدير المستند إلى PDF: {file_path}")

            # عرض رسالة نجاح
            if parent:
                QMessageBox.information(
                    parent,
                    tr.get_text("success_title", "نجاح"),
                    tr.get_text("pdf_export_success", "تم تصدير المستند إلى PDF بنجاح")
                )

            return True

        except Exception as e:
            log_error(f"خطأ في تصدير المستند إلى PDF: {str(e)}")

            # عرض رسالة خطأ
            if parent:
                QMessageBox.critical(
                    parent,
                    tr.get_text("error_title", "خطأ"),
                    tr.get_text("pdf_export_error", "حدث خطأ أثناء تصدير المستند إلى PDF")
                )

            return False

    def print_invoice(self, invoice_data, preview=True, export_pdf=False, parent=None):
        """طباعة فاتورة"""
        try:
            # تحويل قالب الفاتورة
            html_content = self.render_template('invoice', invoice_data)
            if not html_content:
                return False

            # عنوان المستند
            title = f"{tr.get_text('invoice', 'فاتورة')} #{invoice_data.get('invoice_number', '')}"

            if preview:
                # عرض معاينة الطباعة
                return self.print_preview(html_content, title, parent)
            elif export_pdf:
                # تصدير إلى PDF
                return self.export_to_pdf(html_content, title, None, parent)
            else:
                # طباعة مباشرة
                return self.print_document(html_content, title)

        except Exception as e:
            log_error(f"خطأ في طباعة الفاتورة: {str(e)}")
            return False

    def print_report(self, report_data, report_type, preview=True, export_pdf=False, parent=None):
        """طباعة تقرير"""
        try:
            # تحويل قالب التقرير
            html_content = self.render_template(f'report_{report_type}', report_data)
            if not html_content:
                return False

            # عنوان المستند
            title = f"{tr.get_text(f'report_{report_type}', 'تقرير')} - {report_data.get('date_range', '')}"

            if preview:
                # عرض معاينة الطباعة
                return self.print_preview(html_content, title, parent)
            elif export_pdf:
                # تصدير إلى PDF
                return self.export_to_pdf(html_content, title, None, parent)
            else:
                # طباعة مباشرة
                return self.print_document(html_content, title)

        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}")
            return False

    def print_pos_receipt(self, invoice_data, preview=True, parent=None):
        """طباعة إيصال POS"""
        try:
            return self.pos_printer.print_receipt(invoice_data, preview, parent)
        except Exception as e:
            log_error(f"خطأ في طباعة إيصال POS: {str(e)}")
            return False

    def test_pos_printer(self, parent=None):
        """اختبار طابعة POS"""
        try:
            return self.pos_printer.test_printer(parent)
        except Exception as e:
            log_error(f"خطأ في اختبار طابعة POS: {str(e)}")
            return False

    def setup_enhanced_printer(self, paper_size='A4', orientation='portrait'):
        """إعداد طابعة محسنة مع خيارات متقدمة"""
        try:
            printer = QPrinter(QPrinter.HighResolution)

            # تعيين حجم الورق
            if paper_size == 'A4':
                printer.setPageSize(QPrinter.A4)
            elif paper_size == 'A5':
                printer.setPageSize(QPrinter.A5)
            elif paper_size == 'Letter':
                printer.setPageSize(QPrinter.Letter)
            elif paper_size == 'POS_58':
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(58, 200), QPrinter.Millimeter)
            elif paper_size == 'POS_80':
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(80, 200), QPrinter.Millimeter)

            # تعيين الاتجاه
            if orientation == 'landscape':
                printer.setOrientation(QPrinter.Landscape)
            else:
                printer.setOrientation(QPrinter.Portrait)

            # تعيين الهوامش حسب نوع الطابعة
            if paper_size.startswith('POS'):
                printer.setPageMargins(QMarginsF(2, 2, 2, 2), QPrinter.Millimeter)
            else:
                printer.setPageMargins(QMarginsF(10, 10, 10, 10), QPrinter.Millimeter)

            return printer

        except Exception as e:
            log_error(f"خطأ في إعداد الطابعة المحسنة: {str(e)}")
            return None

    def get_available_printers(self):
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            from PyQt5.QtPrintSupport import QPrinterInfo
            printers = []

            for printer_info in QPrinterInfo.availablePrinters():
                printers.append({
                    'name': printer_info.printerName(),
                    'description': printer_info.description(),
                    'is_default': printer_info.isDefault(),
                    'state': printer_info.state()
                })

            return printers

        except Exception as e:
            log_error(f"خطأ في الحصول على قائمة الطابعات: {str(e)}")
            return []

    def print_with_options(self, html_content, title="", paper_size='A4',
                          orientation='portrait', preview=True, parent=None):
        """طباعة مع خيارات متقدمة"""
        try:
            # إنشاء مستند
            document = QTextDocument()
            document.setHtml(html_content)

            # إعداد الطابعة
            printer = self.setup_enhanced_printer(paper_size, orientation)
            if not printer:
                return False

            if title:
                printer.setDocName(title)

            if preview:
                # عرض معاينة الطباعة
                from PyQt5.QtPrintSupport import QPrintPreviewDialog
                preview_dialog = QPrintPreviewDialog(printer, parent)
                preview_dialog.paintRequested.connect(lambda p: document.print_(p))

                if preview_dialog.exec_() == QPrintPreviewDialog.Accepted:
                    log_info(f"تمت معاينة المستند: {title}")
                    return True
            else:
                # طباعة مباشرة
                from PyQt5.QtPrintSupport import QPrintDialog
                dialog = QPrintDialog(printer, parent)
                if dialog.exec_() == QPrintDialog.Accepted:
                    document.print_(printer)
                    log_info(f"تمت طباعة المستند: {title}")
                    return True

            return False

        except Exception as e:
            log_error(f"خطأ في الطباعة مع الخيارات: {str(e)}")
            return False
