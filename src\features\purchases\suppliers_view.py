#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهة إدارة الموردين في المشتريات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHB<PERSON>Layout, QTableWidget, QTableWidgetItem,
    QHeaderView, QMessageBox, QGroupBox, QFormLayout, QMenu, QAction
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QColor, QBrush
import qtawesome as qta
from datetime import datetime, timedelta

from sqlalchemy import func, desc, and_, or_
from src.database import get_db
from src.models import Supplier, Invoice, InvoiceType, InvoiceStatus
from src.ui.widgets.base_widgets import (
    PrimaryButton, SecondaryButton, <PERSON>Button, StyledLineEdit,
    StyledComboBox, <PERSON>d<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, StyledTable
)
from src.utils import translation_manager as tr, log_error, log_info
from src.utils.print_manager import PrintManager
from src.utils.excel_manager import ExcelManager

class PurchaseSuppliersView(QWidget):
    """واجهة إدارة الموردين في المشتريات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_filter = {}
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # شريط البحث والفلترة
        filter_group = QGroupBox(tr.get_text("search_filter", "البحث والفلترة"))
        filter_layout = QHBoxLayout(filter_group)

        # حقل البحث
        self.search_input = StyledLineEdit()
        self.search_input.setPlaceholderText(tr.get_text("search_suppliers", "البحث في الموردين..."))
        self.search_input.textChanged.connect(self.filter_suppliers)
        filter_layout.addWidget(QLabel(tr.get_text("search", "البحث:")))
        filter_layout.addWidget(self.search_input)

        # فلتر الحالة
        self.status_filter = StyledComboBox()
        self.status_filter.addItem(tr.get_text("all_statuses", "جميع الحالات"), None)
        self.status_filter.addItem(tr.get_text("active", "نشط"), True)
        self.status_filter.addItem(tr.get_text("inactive", "غير نشط"), False)
        self.status_filter.currentTextChanged.connect(self.filter_suppliers)
        filter_layout.addWidget(QLabel(tr.get_text("status", "الحالة:")))
        filter_layout.addWidget(self.status_filter)

        layout.addWidget(filter_group)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_supplier", "إضافة مورد"))
        self.add_btn.setIcon(qta.icon('fa5s.plus'))
        self.add_btn.clicked.connect(self.add_supplier)
        actions_layout.addWidget(self.add_btn)

        self.edit_btn = SecondaryButton(tr.get_text("edit_supplier", "تعديل"))
        self.edit_btn.setIcon(qta.icon('fa5s.edit'))
        self.edit_btn.clicked.connect(self.edit_supplier)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)

        self.delete_btn = DangerButton(tr.get_text("delete_supplier", "حذف"))
        self.delete_btn.setIcon(qta.icon('fa5s.trash'))
        self.delete_btn.clicked.connect(self.delete_supplier)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)

        actions_layout.addSeparator()

        self.view_invoices_btn = SecondaryButton(tr.get_text("view_invoices", "عرض الفواتير"))
        self.view_invoices_btn.setIcon(qta.icon('fa5s.file-invoice'))
        self.view_invoices_btn.clicked.connect(self.view_supplier_invoices)
        self.view_invoices_btn.setEnabled(False)
        actions_layout.addWidget(self.view_invoices_btn)

        self.supplier_statement_btn = SecondaryButton(tr.get_text("supplier_statement", "كشف حساب"))
        self.supplier_statement_btn.setIcon(qta.icon('fa5s.file-alt'))
        self.supplier_statement_btn.clicked.connect(self.generate_supplier_statement)
        self.supplier_statement_btn.setEnabled(False)
        actions_layout.addWidget(self.supplier_statement_btn)

        actions_layout.addStretch()

        self.refresh_btn = SecondaryButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.setIcon(qta.icon('fa5s.sync'))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        self.export_btn = SecondaryButton(tr.get_text("export", "تصدير"))
        self.export_btn.setIcon(qta.icon('fa5s.file-export'))
        self.export_btn.clicked.connect(self.export_data)
        actions_layout.addWidget(self.export_btn)

        layout.addLayout(actions_layout)

        # جدول الموردين
        self.table = StyledTable()
        self.table.setColumnCount(10)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("code", "الكود"),
            tr.get_text("name", "الاسم"),
            tr.get_text("company", "الشركة"),
            tr.get_text("phone", "الهاتف"),
            tr.get_text("email", "البريد الإلكتروني"),
            tr.get_text("balance", "الرصيد"),
            tr.get_text("total_purchases", "إجمالي المشتريات"),
            tr.get_text("invoices_count", "عدد الفواتير"),
            tr.get_text("last_purchase", "آخر شراء"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)

        # ربط الأحداث
        self.table.doubleClicked.connect(self.view_supplier_details)
        self.table.selectionModel().selectionChanged.connect(self.on_selection_changed)

        # إنشاء قائمة السياق
        self.table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.total_suppliers_label = StyledLabel(tr.get_text("total_suppliers", "إجمالي الموردين: 0"))
        self.active_suppliers_label = StyledLabel(tr.get_text("active_suppliers", "الموردين النشطون: 0"))
        self.total_balance_label = StyledLabel(tr.get_text("total_balance", "إجمالي الأرصدة: 0"))

        status_layout.addWidget(self.total_suppliers_label)
        status_layout.addStretch()
        status_layout.addWidget(self.active_suppliers_label)
        status_layout.addStretch()
        status_layout.addWidget(self.total_balance_label)

        layout.addLayout(status_layout)

    def load_data(self):
        """تحميل بيانات الموردين"""
        try:
            db = next(get_db())

            # بناء الاستعلام
            query = db.query(Supplier).filter(Supplier.is_deleted == False)

            # تطبيق الفلاتر
            if self.current_filter.get('search'):
                search_term = f"%{self.current_filter['search']}%"
                query = query.filter(
                    or_(
                        Supplier.name.like(search_term),
                        Supplier.code.like(search_term),
                        Supplier.company_name.like(search_term),
                        Supplier.phone.like(search_term),
                        Supplier.email.like(search_term)
                    )
                )

            if self.current_filter.get('is_active') is not None:
                query = query.filter(Supplier.is_active == self.current_filter['is_active'])

            suppliers = query.order_by(Supplier.name).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)
            self.table.setSortingEnabled(False)

            total_balance = 0
            active_count = 0

            for supplier in suppliers:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # الكود
                self.table.setItem(row_position, 0, QTableWidgetItem(supplier.code))

                # الاسم
                self.table.setItem(row_position, 1, QTableWidgetItem(supplier.name))

                # الشركة
                self.table.setItem(row_position, 2, QTableWidgetItem(supplier.company_name or "-"))

                # الهاتف
                phone = supplier.phone or supplier.mobile or "-"
                self.table.setItem(row_position, 3, QTableWidgetItem(phone))

                # البريد الإلكتروني
                self.table.setItem(row_position, 4, QTableWidgetItem(supplier.email or "-"))

                # الرصيد
                balance_item = QTableWidgetItem(f"{supplier.balance:.2f}")
                if supplier.balance > 0:
                    balance_item.setForeground(QBrush(QColor("green")))
                elif supplier.balance < 0:
                    balance_item.setForeground(QBrush(QColor("red")))
                self.table.setItem(row_position, 5, balance_item)
                total_balance += supplier.balance

                # إحصائيات المورد
                supplier_stats = self.get_supplier_statistics(db, supplier.id)

                # إجمالي المشتريات
                self.table.setItem(row_position, 6, QTableWidgetItem(f"{supplier_stats['total_purchases']:.2f}"))

                # عدد الفواتير
                self.table.setItem(row_position, 7, QTableWidgetItem(str(supplier_stats['invoices_count'])))

                # آخر شراء
                last_purchase = supplier_stats['last_purchase_date']
                last_purchase_text = last_purchase.strftime("%Y-%m-%d") if last_purchase else "-"
                self.table.setItem(row_position, 8, QTableWidgetItem(last_purchase_text))

                # الحالة
                status_item = QTableWidgetItem(tr.get_text("active", "نشط") if supplier.is_active else tr.get_text("inactive", "غير نشط"))
                if supplier.is_active:
                    status_item.setBackground(QBrush(QColor("#e8f5e8")))
                    active_count += 1
                else:
                    status_item.setBackground(QBrush(QColor("#ffeaea")))
                self.table.setItem(row_position, 9, status_item)

                # تخزين معرف المورد
                self.table.item(row_position, 0).setData(Qt.UserRole, supplier.id)

            self.table.setSortingEnabled(True)

            # تحديث شريط الحالة
            self.total_suppliers_label.setText(tr.get_text("total_suppliers", f"إجمالي الموردين: {len(suppliers)}"))
            self.active_suppliers_label.setText(tr.get_text("active_suppliers", f"الموردين النشطون: {active_count}"))
            self.total_balance_label.setText(tr.get_text("total_balance", f"إجمالي الأرصدة: {total_balance:.2f}"))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الموردين: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def get_supplier_statistics(self, db, supplier_id):
        """الحصول على إحصائيات المورد"""
        try:
            # إجمالي المشتريات
            total_purchases = db.query(func.sum(Invoice.total)).filter(
                Invoice.supplier_id == supplier_id,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            # عدد الفواتير
            invoices_count = db.query(func.count(Invoice.id)).filter(
                Invoice.supplier_id == supplier_id,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar() or 0

            # آخر تاريخ شراء
            last_purchase = db.query(func.max(Invoice.invoice_date)).filter(
                Invoice.supplier_id == supplier_id,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status != InvoiceStatus.CANCELLED
            ).scalar()

            return {
                'total_purchases': total_purchases,
                'invoices_count': invoices_count,
                'last_purchase_date': last_purchase
            }

        except Exception as e:
            log_error(f"خطأ في الحصول على إحصائيات المورد: {str(e)}")
            return {
                'total_purchases': 0,
                'invoices_count': 0,
                'last_purchase_date': None
            }

    def filter_suppliers(self):
        """تطبيق الفلاتر على الموردين"""
        self.current_filter = {
            'search': self.search_input.text().strip(),
            'is_active': self.status_filter.currentData()
        }
        self.load_data()

    def on_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير التحديد"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.view_invoices_btn.setEnabled(has_selection)
        self.supplier_statement_btn.setEnabled(has_selection)

    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        if self.table.itemAt(position) is None:
            return

        menu = QMenu(self)

        view_action = menu.addAction(qta.icon('fa5s.eye'), tr.get_text("view_details", "عرض التفاصيل"))
        view_action.triggered.connect(self.view_supplier_details)

        edit_action = menu.addAction(qta.icon('fa5s.edit'), tr.get_text("edit", "تعديل"))
        edit_action.triggered.connect(self.edit_supplier)

        menu.addSeparator()

        invoices_action = menu.addAction(qta.icon('fa5s.file-invoice'), tr.get_text("view_invoices", "عرض الفواتير"))
        invoices_action.triggered.connect(self.view_supplier_invoices)

        statement_action = menu.addAction(qta.icon('fa5s.file-alt'), tr.get_text("supplier_statement", "كشف حساب"))
        statement_action.triggered.connect(self.generate_supplier_statement)

        menu.addSeparator()

        delete_action = menu.addAction(qta.icon('fa5s.trash'), tr.get_text("delete", "حذف"))
        delete_action.triggered.connect(self.delete_supplier)

        menu.exec_(self.table.mapToGlobal(position))

    def add_supplier(self):
        """إضافة مورد جديد"""
        from .supplier_dialog import SupplierDialog
        dialog = SupplierDialog(self)
        if dialog.exec_():
            self.load_data()

    def edit_supplier(self):
        """تعديل مورد"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        supplier_id = self.table.item(row, 0).data(Qt.UserRole)

        try:
            db = next(get_db())
            supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
            if supplier:
                from .supplier_dialog import SupplierDialog
                dialog = SupplierDialog(self, supplier)
                if dialog.exec_():
                    self.load_data()
        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المورد: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_supplier", "حدث خطأ أثناء تحميل بيانات المورد")
            )

    def delete_supplier(self):
        """حذف مورد"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        supplier_id = self.table.item(row, 0).data(Qt.UserRole)
        supplier_name = self.table.item(row, 1).text()

        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_delete", "تأكيد الحذف"),
            tr.get_text("confirm_delete_supplier", f"هل أنت متأكد من حذف المورد '{supplier_name}'؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
                if supplier:
                    supplier.is_deleted = True
                    supplier.deleted_at = datetime.now()
                    db.commit()
                    self.load_data()
                    log_info(f"تم حذف المورد: {supplier_name}")

            except Exception as e:
                log_error(f"خطأ في حذف المورد: {str(e)}")
                QMessageBox.critical(
                    self,
                    tr.get_text("error", "خطأ"),
                    tr.get_text("error_deleting_supplier", "حدث خطأ أثناء حذف المورد")
                )

    def view_supplier_details(self):
        """عرض تفاصيل المورد"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        supplier_id = self.table.item(row, 0).data(Qt.UserRole)

        from .supplier_details_dialog import SupplierDetailsDialog
        dialog = SupplierDetailsDialog(self, supplier_id)
        dialog.exec_()

    def view_supplier_invoices(self):
        """عرض فواتير المورد"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        supplier_id = self.table.item(row, 0).data(Qt.UserRole)

        from .supplier_invoices_dialog import SupplierInvoicesDialog
        dialog = SupplierInvoicesDialog(self, supplier_id)
        dialog.exec_()

    def generate_supplier_statement(self):
        """إنشاء كشف حساب المورد"""
        if not self.table.selectedItems():
            return

        row = self.table.currentRow()
        supplier_id = self.table.item(row, 0).data(Qt.UserRole)

        from .supplier_statement_dialog import SupplierStatementDialog
        dialog = SupplierStatementDialog(self, supplier_id)
        dialog.exec_()

    def export_data(self):
        """تصدير بيانات الموردين"""
        try:
            # إنشاء بيانات التصدير
            headers = []
            for col in range(self.table.columnCount()):
                headers.append(self.table.horizontalHeaderItem(col).text())

            data = []
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # تصدير إلى Excel
            excel_manager = ExcelManager.get_instance()
            excel_manager.export_to_excel(
                headers=headers,
                data=data,
                title=tr.get_text("suppliers_report", "تقرير الموردين"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير البيانات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("export_error", "حدث خطأ أثناء تصدير البيانات")
            )
