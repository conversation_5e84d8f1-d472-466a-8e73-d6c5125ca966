"""
إدارة إعدادات التطبيق
"""

import os
import json
from pathlib import Path
from typing import Any, Dict, Optional

# مسار ملف الإعدادات
CONFIG_DIR = Path.home() / '.amin-alhisabat'
CONFIG_FILE = CONFIG_DIR / 'config.json'

# الإعدادات الافتراضية
DEFAULT_CONFIG = {
    'language': 'ar',
    'theme': 'light',
    'default_currency': 'EGP',
    'printer_type': 'normal',
    'default_printer': '',
    'page_size': 'A4',
    'print_margins': 10,
    'company_name': '',
    'company_logo': '',
    'company_address': '',
    'company_phone': '',
    'company_email': '',
    'company_website': '',
    'version': '2.0.0'
}

def ensure_config_dir() -> None:
    """التأكد من وجود مجلد الإعدادات"""
    if not CONFIG_DIR.exists():
        CONFIG_DIR.mkdir(parents=True)

def load_config() -> Dict[str, Any]:
    """تحميل الإعدادات من الملف"""
    ensure_config_dir()
    
    if CONFIG_FILE.exists():
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return {**DEFAULT_CONFIG, **json.load(f)}
        except Exception as e:
            print(f"خطأ في قراءة ملف الإعدادات: {str(e)}")
            return DEFAULT_CONFIG.copy()
    else:
        save_config(DEFAULT_CONFIG)
        return DEFAULT_CONFIG.copy()

def save_config(config: Dict[str, Any]) -> None:
    """حفظ الإعدادات في الملف"""
    ensure_config_dir()
    
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"خطأ في حفظ ملف الإعدادات: {str(e)}")

# النسخة العامة من الإعدادات
_config = load_config()

def get_setting(key: str, default: Any = None) -> Any:
    """
    الحصول على قيمة إعداد معين
    :param key: مفتاح الإعداد
    :param default: القيمة الافتراضية إذا لم يوجد الإعداد
    :return: قيمة الإعداد
    """
    return _config.get(key, default)

def set_setting(key: str, value: Any) -> None:
    """
    تعيين قيمة إعداد معين
    :param key: مفتاح الإعداد
    :param value: القيمة الجديدة
    """
    _config[key] = value
    save_config(_config)

def update_settings(settings: Dict[str, Any]) -> None:
    """
    تحديث مجموعة من الإعدادات
    :param settings: قاموس يحتوي على الإعدادات المراد تحديثها
    """
    _config.update(settings)
    save_config(_config)

def reset_settings() -> None:
    """إعادة تعيين جميع الإعدادات إلى القيم الافتراضية"""
    _config.clear()
    _config.update(DEFAULT_CONFIG)
    save_config(_config)

def get_all_settings() -> Dict[str, Any]:
    """الحصول على جميع الإعدادات الحالية"""
    return _config.copy()

def is_first_run() -> bool:
    """التحقق مما إذا كان هذا أول تشغيل للبرنامج"""
    return not CONFIG_FILE.exists()

def get_app_dir() -> Path:
    """الحصول على مسار مجلد التطبيق"""
    return Path(os.getenv('APP_PATH', os.path.dirname(os.path.dirname(__file__))))