#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QStackedWidget, QToolBar, QStatusBar, QLabel,
    QMenuBar, QMenu, QAction, QMessageBox, QPushButton
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon
from src.utils.icon_manager import get_icon

from src.ui.theme_manager import ThemeManager
from src.utils import translation_manager as tr, config, log_info, log_error
from src.models import User
from src.database import get_db
# تم نقل الاستيرادات إلى داخل الدوال لتجنب الاستيراد الدائري
from src.utils.icon_manager import get_icon

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""

    def __init__(self, current_user: User):
        super().__init__()
        self.current_user = current_user
        self.db = next(get_db())
        self.theme_manager = ThemeManager.get_instance()
        self.setup_ui()

        # تحميل لوحة التحكم
        self.load_dashboard()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle(tr.get_text("app_name"))
        self.setMinimumSize(1200, 800)

        # تطبيق السمة
        self.apply_theme()

        # إعداد شريط القوائم
        self.setup_menu_bar()

        # إعداد شريط الأدوات
        self.setup_toolbar()

        # إعداد المحتوى المركزي
        self.setup_central_widget()

        # إعداد شريط الحالة
        self.setup_status_bar()

        # تحميل الصفحة الرئيسية
        self.load_dashboard()

    def apply_theme(self):
        """تطبيق السمة الحالية"""
        theme = config.get_setting('theme', 'light')
        language = config.get_setting('language', 'ar')
        self.theme_manager.apply_theme(theme, 'rtl' if language == 'ar' else 'ltr')

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu(tr.get_text("menu_file"))

        # إجراءات قائمة الملف
        settings_action = QAction(
            get_icon("fa5s.cog", color="white"), tr.get_text("menu_settings"), self
        )
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)

        file_menu.addSeparator()

        logout_action = QAction(
            get_icon("fa5s.sign-out-alt", color="white"), tr.get_text("menu_logout"), self
        )
        logout_action.triggered.connect(self.handle_logout)
        file_menu.addAction(logout_action)

        exit_action = QAction(
            get_icon("fa5s.power-off", color="white"), tr.get_text("menu_exit"), self
        )
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة المظهر
        view_menu = menubar.addMenu(tr.get_text("menu_view"))

        # إجراء تبديل المظهر
        self.theme_action = QAction(
            get_icon("fa5s.moon", color="white"), tr.get_text("theme_dark"), self
        )
        self.theme_action.triggered.connect(self.toggle_theme)
        view_menu.addAction(self.theme_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu(tr.get_text("menu_help"))

        about_action = QAction(
            get_icon("fa5s.info-circle", color="white"), tr.get_text("menu_about"), self
        )
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setMovable(False)
        self.addToolBar(toolbar)

        # أزرار التنقل الرئيسية
        dashboard_action = toolbar.addAction(
            get_icon("fa5s.home", color="white"), tr.get_text("nav_dashboard")
        )
        dashboard_action.triggered.connect(self.load_dashboard)

        sales_action = toolbar.addAction(
            get_icon("fa5s.shopping-cart", color="white"), tr.get_text("nav_sales")
        )
        sales_action.triggered.connect(self.load_sales)

        # زر نقاط البيع
        pos_action = toolbar.addAction(
            get_icon("fa5s.cash-register", color="white"), tr.get_text("nav_pos", "نقاط البيع")
        )
        pos_action.triggered.connect(self.load_pos)

        purchases_action = toolbar.addAction(
            get_icon("fa5s.truck", color="white"), tr.get_text("nav_purchases")
        )
        purchases_action.triggered.connect(self.load_purchases)

        inventory_action = toolbar.addAction(
            get_icon("fa5s.boxes", color="white"), tr.get_text("nav_inventory")
        )
        inventory_action.triggered.connect(self.load_inventory)

        # إضافة زر المصروفات
        expenses_action = toolbar.addAction(
            get_icon("fa5s.money-bill-wave", color="white"), tr.get_text("nav_expenses", "المصروفات")
        )
        expenses_action.triggered.connect(self.load_expenses)

        # إضافة زر إدارة الموظفين
        employees_action = toolbar.addAction(
            get_icon("fa5s.users", color="white"), tr.get_text("nav_employees")
        )
        employees_action.triggered.connect(self.load_employees)

        attendance_action = toolbar.addAction(
            get_icon("fa5s.user-clock", color="white"), tr.get_text("nav_attendance", "الحضور والانصراف")
        )
        attendance_action.triggered.connect(self.load_attendance)

        inventory_action = toolbar.addAction(
            get_icon("fa5s.boxes", color="white"), tr.get_text("nav_inventory", "المخزون")
        )
        inventory_action.triggered.connect(self.load_inventory)

        sales_action = toolbar.addAction(
            get_icon("fa5s.shopping-cart", color="white"), tr.get_text("nav_sales", "المبيعات")
        )
        sales_action.triggered.connect(self.load_sales)

        purchases_action = toolbar.addAction(
            get_icon("fa5s.shopping-bag", color="white"), tr.get_text("nav_purchases", "المشتريات")
        )
        purchases_action.triggered.connect(self.load_purchases)

        # إضافة زر الشركات الخارجية
        external_companies_action = toolbar.addAction(
            get_icon("fa5s.building", color="white"), tr.get_text("nav_external_companies", "الشركات الخارجية")
        )
        external_companies_action.triggered.connect(self.load_external_companies)

        reports_action = toolbar.addAction(
            get_icon("fa5s.chart-bar", color="white"), tr.get_text("nav_reports")
        )
        reports_action.triggered.connect(self.load_reports)

        # فاصل
        toolbar.addSeparator()

        # أزرار إضافية للمسؤول
        if self.current_user.is_admin:
            users_action = toolbar.addAction(
                get_icon("fa5s.user-shield", color="white"), tr.get_text("nav_users")
            )
            users_action.triggered.connect(self.load_users)

            settings_action = toolbar.addAction(
                get_icon("fa5s.cog", color="white"), tr.get_text("nav_settings")
            )
            settings_action.triggered.connect(self.show_settings)

        # فاصل
        toolbar.addSeparator()

        # زر تبديل المظهر
        self.theme_button = QPushButton()
        self.theme_button.setIcon(get_icon("fa5s.moon", color="white"))
        self.theme_button.setToolTip(tr.get_text("theme_toggle"))
        self.theme_button.clicked.connect(self.toggle_theme)
        toolbar.addWidget(self.theme_button)

        # تحديث أيقونة المظهر
        self.update_theme_icon()

    def update_theme_icon(self):
        """تحديث أيقونة المظهر حسب السمة الحالية"""
        current_theme = self.theme_manager.get_current_theme()
        if current_theme == ThemeManager.THEME_DARK:
            self.theme_button.setIcon(get_icon("fa5s.sun", color="white"))
            self.theme_action.setIcon(get_icon("fa5s.sun", color="white"))
            self.theme_action.setText(tr.get_text("theme_light"))
        else:
            self.theme_button.setIcon(get_icon("fa5s.moon", color="white"))
            self.theme_action.setIcon(get_icon("fa5s.moon", color="white"))
            self.theme_action.setText(tr.get_text("theme_dark"))

    def toggle_theme(self):
        """تبديل السمة بين الفاتح والداكن"""
        new_theme = self.theme_manager.toggle_theme()
        config.set_setting('theme', new_theme)
        self.update_theme_icon()

    def setup_central_widget(self):
        """إعداد المحتوى المركزي"""
        # الحاوية الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # حاوية المحتوى المتغير
        self.content_stack = QStackedWidget()
        main_layout.addWidget(self.content_stack)

    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # معلومات المستخدم
        user_label = QLabel(f"{tr.get_text('label_user')}: {self.current_user.full_name}")
        status_bar.addPermanentWidget(user_label)

        # إصدار البرنامج
        version = config.get_setting('version', '1.0.0')
        version_label = QLabel(f"v{version}")
        status_bar.addPermanentWidget(version_label)

    def load_dashboard(self):
        """تحميل لوحة التحكم الحديثة"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء لوحة التحكم الحديثة
            from src.ui.modern_dashboard import ModernDashboard
            dashboard_view = ModernDashboard()
            dashboard_view.module_selected.connect(self.handle_module_selection)
            self.content_stack.addWidget(dashboard_view)
            self.content_stack.setCurrentWidget(dashboard_view)

            self.statusBar().showMessage(tr.get_text("msg_dashboard_loaded", "تم تحميل لوحة التحكم الحديثة"))

        except Exception as e:
            log_error(f"خطأ في تحميل لوحة التحكم: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_dashboard", "حدث خطأ أثناء تحميل لوحة التحكم")
            )

    def load_sales(self):
        """تحميل قسم المبيعات"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة المبيعات
            from src.features.sales.views import SalesView
            sales_view = SalesView()
            self.content_stack.addWidget(sales_view)
            self.content_stack.setCurrentWidget(sales_view)

            self.statusBar().showMessage(tr.get_text("msg_sales_loaded", "تم تحميل قسم المبيعات"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة المبيعات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_sales", "حدث خطأ أثناء تحميل واجهة المبيعات")
            )

    def load_pos(self):
        """تحميل نظام نقاط البيع"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة نقاط البيع
            from src.features.pos.views import POSMainView
            pos_view = POSMainView()
            self.content_stack.addWidget(pos_view)
            self.content_stack.setCurrentWidget(pos_view)

            self.statusBar().showMessage(tr.get_text("msg_pos_loaded", "تم تحميل نظام نقاط البيع"))

        except Exception as e:
            log_error(f"خطأ في تحميل نظام نقاط البيع: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_pos", "حدث خطأ أثناء تحميل نظام نقاط البيع")
            )

    def load_purchases(self):
        """تحميل قسم المشتريات"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة المشتريات
            from src.features.purchases.views import PurchasesView
            purchases_view = PurchasesView()
            self.content_stack.addWidget(purchases_view)
            self.content_stack.setCurrentWidget(purchases_view)

            self.statusBar().showMessage(tr.get_text("msg_purchases_loaded", "تم تحميل قسم المشتريات"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة المشتريات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_purchases", "حدث خطأ أثناء تحميل واجهة المشتريات")
            )

    def load_inventory(self):
        """تحميل قسم المخزون"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة المخزون
            from src.features.inventory.views import InventoryView
            inventory_view = InventoryView()
            self.content_stack.addWidget(inventory_view)
            self.content_stack.setCurrentWidget(inventory_view)

            self.statusBar().showMessage(tr.get_text("msg_inventory_loaded", "تم تحميل قسم المخزون"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة المخزون: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_inventory", "حدث خطأ أثناء تحميل واجهة المخزون")
            )

    def load_employees(self):
        """تحميل قسم إدارة الموظفين"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة إدارة الموظفين
            from src.features.hr.views import EmployeeManagementView
            employees_view = EmployeeManagementView()
            self.content_stack.addWidget(employees_view)
            self.content_stack.setCurrentWidget(employees_view)

            self.statusBar().showMessage(tr.get_text("msg_employees_loaded", "تم تحميل قسم إدارة الموظفين"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة إدارة الموظفين: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_employees", "حدث خطأ أثناء تحميل واجهة إدارة الموظفين")
            )

    def load_attendance(self):
        """تحميل قسم إدارة الحضور والانصراف"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة إدارة الحضور والانصراف
            attendance_view = AttendanceManagementView()
            self.content_stack.addWidget(attendance_view)
            self.content_stack.setCurrentWidget(attendance_view)

            self.statusBar().showMessage(tr.get_text("msg_attendance_loaded", "تم تحميل قسم إدارة الحضور والانصراف"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة إدارة الحضور والانصراف: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_attendance", "حدث خطأ أثناء تحميل واجهة إدارة الحضور والانصراف")
            )

    def load_inventory(self):
        """تحميل قسم إدارة المخزون"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة إدارة المخزون
            inventory_view = InventoryView()
            self.content_stack.addWidget(inventory_view)
            self.content_stack.setCurrentWidget(inventory_view)

            self.statusBar().showMessage(tr.get_text("msg_inventory_loaded", "تم تحميل قسم إدارة المخزون"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة إدارة المخزون: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_inventory", "حدث خطأ أثناء تحميل واجهة إدارة المخزون")
            )

    def load_sales(self):
        """تحميل قسم إدارة المبيعات"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة إدارة المبيعات
            sales_view = SalesView()
            self.content_stack.addWidget(sales_view)
            self.content_stack.setCurrentWidget(sales_view)

            self.statusBar().showMessage(tr.get_text("msg_sales_loaded", "تم تحميل قسم إدارة المبيعات"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة إدارة المبيعات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_sales", "حدث خطأ أثناء تحميل واجهة إدارة المبيعات")
            )

    def load_external_companies(self):
        """تحميل قسم الشركات الخارجية"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة الشركات الخارجية
            external_companies_view = ExternalCompanyListView()
            self.content_stack.addWidget(external_companies_view)
            self.content_stack.setCurrentWidget(external_companies_view)

            self.statusBar().showMessage(tr.get_text("msg_external_companies_loaded", "تم تحميل الشركات الخارجية"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة الشركات الخارجية: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_external_companies", "حدث خطأ أثناء تحميل واجهة الشركات الخارجية")
            )

    def load_reports(self):
        """تحميل قسم التقارير"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة التقارير
            reports_view = ReportsView()
            self.content_stack.addWidget(reports_view)
            self.content_stack.setCurrentWidget(reports_view)

            self.statusBar().showMessage(tr.get_text("msg_reports_loaded", "تم تحميل قسم التقارير"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة التقارير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_reports", "حدث خطأ أثناء تحميل واجهة التقارير")
            )

    def load_expenses(self):
        """تحميل قسم المصروفات"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة المصروفات
            expenses_view = ExpensesView()
            self.content_stack.addWidget(expenses_view)
            self.content_stack.setCurrentWidget(expenses_view)

            self.statusBar().showMessage(tr.get_text("msg_expenses_loaded", "تم تحميل قسم المصروفات"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة المصروفات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_expenses", "حدث خطأ أثناء تحميل واجهة المصروفات")
            )

    def load_users(self):
        """تحميل قسم المستخدمين"""
        try:
            # إزالة الواجهة السابقة إن وجدت
            current_widget = self.content_stack.currentWidget()
            if current_widget:
                self.content_stack.removeWidget(current_widget)
                current_widget.deleteLater()

            # إنشاء واجهة إدارة المستخدمين
            from src.features.users.views import UserManagementView
            users_view = UserManagementView()
            self.content_stack.addWidget(users_view)
            self.content_stack.setCurrentWidget(users_view)

            self.statusBar().showMessage(tr.get_text("msg_users_loaded", "تم تحميل قسم إدارة المستخدمين"))

        except Exception as e:
            log_error(f"خطأ في تحميل واجهة إدارة المستخدمين: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_users", "حدث خطأ أثناء تحميل واجهة إدارة المستخدمين")
            )

    def show_settings(self):
        """عرض نافذة الإعدادات"""
        from src.ui.dialogs.settings_dialog import SettingsDialog
        dialog = SettingsDialog(self.current_user, self)
        if dialog.exec_():
            # إعادة تطبيق السمة واللغة في حال تم تغييرها
            self.apply_theme()
            tr.load_translations()  # إعادة تحميل الترجمات
            self.retranslate_ui()  # تحديث نصوص الواجهة

    def retranslate_ui(self):
        """تحديث نصوص الواجهة"""
        self.setWindowTitle(tr.get_text("app_name"))
        # TODO: تحديث باقي نصوص الواجهة

    def show_about(self):
        """عرض نافذة حول البرنامج"""
        QMessageBox.about(
            self,
            tr.get_text("about_title"),
            tr.get_text("about_content")
        )

    def handle_logout(self):
        """معالجة تسجيل الخروج"""
        reply = QMessageBox.question(
            self,
            tr.get_text("logout_title"),
            tr.get_text("logout_confirm"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            log_info(f"تسجيل خروج المستخدم: {self.current_user.username}")
            self.close()
            # TODO: إعادة فتح نافذة تسجيل الدخول

    def handle_module_selection(self, module_name):
        """
        معالجة اختيار الوحدة من لوحة التحكم الحديثة
        :param module_name: اسم الوحدة المختارة
        """
        log_info(f"تم اختيار الوحدة: {module_name}")

        # تحميل الوحدة المناسبة
        if module_name == "inventory":
            self.load_inventory()
        elif module_name == "treasury":
            self.statusBar().showMessage(tr.get_text("msg_loading_treasury", "جاري تحميل الخزينة..."))
            QMessageBox.information(self, "معلومات", "وحدة الخزينة قيد التطوير")
        elif module_name == "invoices":
            self.load_sales()
        elif module_name == "definitions":
            self.statusBar().showMessage(tr.get_text("msg_loading_definitions", "جاري تحميل التعاريف الأساسية..."))
            QMessageBox.information(self, "معلومات", "وحدة التعاريف الأساسية قيد التطوير")
        elif module_name == "daily_sales":
            self.load_reports()
        elif module_name == "daily_expenses":
            self.statusBar().showMessage(tr.get_text("msg_loading_expenses", "جاري تحميل تقرير المصروفات..."))
            QMessageBox.information(self, "معلومات", "تقرير المصروفات اليومي قيد التطوير")
        elif module_name == "daily_treasury":
            self.statusBar().showMessage(tr.get_text("msg_loading_treasury_report", "جاري تحميل تقرير الخزينة..."))
            QMessageBox.information(self, "معلومات", "تقرير الخزينة اليومي قيد التطوير")
        elif module_name == "chat":
            self.statusBar().showMessage(tr.get_text("msg_loading_chat", "جاري تحميل الدردشة..."))
            QMessageBox.information(self, "معلومات", "وحدة الدردشة قيد التطوير")
        elif module_name == "recent_sales":
            self.load_sales()
        # الوحدات القديمة (للتوافق)
        elif module_name == "sales":
            self.load_sales()
        elif module_name == "purchases":
            self.load_purchases()
        elif module_name == "pos":
            self.load_pos()
        elif module_name == "hr":
            self.load_hr()
        elif module_name == "reports":
            self.load_reports()
        else:
            QMessageBox.information(
                self,
                "معلومات",
                f"الوحدة '{module_name}' قيد التطوير"
            )

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        if hasattr(self, 'db'):
            self.db.close()
        event.accept()