/* Base styles for Amin Al<PERSON>t */

/* General Widgets */
QWidget {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    font-size: 12px;
}

/* RTL Support */
QWidget[dir="rtl"] {
    direction: rtl;
}

/* Arabic Font Adjustments */
QWidget[lang="ar"] {
    font-family: 'Cairo', 'Droid Arabic Kufi', 'Tahoma', sans-serif;
    font-size: 13px; /* Slightly larger for Arabic */
    letter-spacing: 0px;
}

/* Buttons */
QPushButton {
    padding: 8px 16px;
    border-radius: 4px;
    min-width: 80px;
    font-weight: bold;
}

QPushButton:hover {
    opacity: 0.9;
}

QPushButton:pressed {
    opacity: 0.7;
}

QPushButton:disabled {
    opacity: 0.5;
}

/* Primary Button */
QPushButton[primary="true"] {
    background-color: #2196F3;
    color: white;
    border: none;
}

/* Danger Button */
QPushButton[danger="true"] {
    background-color: #f44336;
    color: white;
    border: none;
}

/* Input Fields */
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
    padding: 8px;
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
}

/* Combo Box */
QComboBox {
    padding: 8px;
    border-radius: 4px;
    border-width: 1px;
    border-style: solid;
    min-width: 120px;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

/* Labels */
QLabel {
    padding: 4px;
}

QLabel[header="true"] {
    font-size: 16px;
    font-weight: bold;
    padding: 8px 4px;
}

/* Tables */
QTableView {
    border-radius: 4px;
    gridline-color: #E0E0E0;
    selection-background-color: #2196F3;
    selection-color: white;
}

QTableView::item {
    padding: 8px;
}

QHeaderView::section {
    padding: 8px;
    font-weight: bold;
}

/* Scroll Bars */
QScrollBar:vertical {
    width: 12px;
    margin: 0px;
}

QScrollBar:horizontal {
    height: 12px;
    margin: 0px;
}

QScrollBar::handle {
    border-radius: 6px;
    min-height: 30px;
}

/* Tab Widget */
QTabWidget::pane {
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
}

QTabBar::tab {
    padding: 8px 16px;
    min-width: 80px;
}

/* Menu */
QMenu {
    padding: 4px;
    border-radius: 4px;
}

QMenu::item {
    padding: 8px 24px;
}

/* Separators */
QFrame[frameShape="4"] {  /* Horizontal Line */
    max-height: 1px;
    margin: 8px 0;
}

/* Dialog Boxes */
QDialog {
    min-width: 300px;
}

/* Message Boxes */
QMessageBox {
    min-width: 300px;
}

QMessageBox QPushButton {
    min-width: 100px;
}

/* Progress Bar */
QProgressBar {
    border-radius: 4px;
    text-align: center;
    min-height: 20px;
}

/* Tool Tips */
QToolTip {
    padding: 8px;
    border-radius: 4px;
}