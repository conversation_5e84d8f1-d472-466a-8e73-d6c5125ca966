#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهات المشتريات
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QFormLayout, QSplitter, QFrame, QToolBar, QAction, QMenu,
    QProgressBar, QCalendarWidget, QTimeEdit, QScrollArea
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QDateTime, QSize, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QColor, QBrush, QFont, QPalette
import qtawesome as qta
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas

from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_

from src.database import get_db
from src.models import (
    Invoice, InvoiceItem, Supplier, Product, InvoiceType,
    InvoiceStatus, PaymentMethod, InventoryMovement, MovementType
)
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable, StyledDoubleSpinBox, StyledSpinBox
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info
from src.utils.print_manager import PrintManager
from src.utils.excel_manager import ExcelManager
from .dashboard_view import PurchasesDashboardView
from .suppliers_view import PurchaseSuppliersView
from .settings_view import PurchaseSettingsView

class PurchasesView(QWidget):
    """
    واجهة المشتريات الرئيسية
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("purchases_management", "إدارة المشتريات"))
        layout.addWidget(header)

        # شريط الأدوات
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))

        # زر تحديث
        refresh_action = QAction(qta.icon('fa5s.sync'), tr.get_text("refresh", "تحديث"), self)
        refresh_action.triggered.connect(self.refresh_all)
        toolbar.addAction(refresh_action)

        # زر طباعة
        print_action = QAction(qta.icon('fa5s.print'), tr.get_text("print", "طباعة"), self)
        print_action.triggered.connect(self.print_report)
        toolbar.addAction(print_action)

        # زر تصدير
        export_action = QAction(qta.icon('fa5s.file-export'), tr.get_text("export", "تصدير"), self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        # إضافة شريط الأدوات
        layout.addWidget(toolbar)

        # علامات التبويب
        self.tabs = QTabWidget()

        # تبويب لوحة المعلومات
        self.dashboard_tab = PurchasesDashboardView()
        self.tabs.addTab(self.dashboard_tab, tr.get_text("dashboard", "لوحة المعلومات"))

        # تبويب الفواتير
        self.invoices_tab = PurchaseInvoiceView()
        self.tabs.addTab(self.invoices_tab, tr.get_text("invoices", "الفواتير"))

        # تبويب المرتجعات
        self.returns_tab = PurchaseReturnView()
        self.tabs.addTab(self.returns_tab, tr.get_text("returns", "المرتجعات"))

        # تبويب طلبات الشراء
        from .purchase_orders_view import PurchaseOrdersView
        self.orders_tab = PurchaseOrdersView()
        self.tabs.addTab(self.orders_tab, tr.get_text("purchase_orders", "طلبات الشراء"))

        # تبويب تقييم الموردين
        from .supplier_evaluation_view import SupplierEvaluationView
        self.evaluation_tab = SupplierEvaluationView()
        self.tabs.addTab(self.evaluation_tab, tr.get_text("supplier_evaluation", "تقييم الموردين"))

        # تبويب الموردين
        self.suppliers_tab = PurchaseSuppliersView()
        self.tabs.addTab(self.suppliers_tab, tr.get_text("suppliers", "الموردين"))

        # تبويب التقارير
        self.reports_tab = PurchaseReportView()
        self.tabs.addTab(self.reports_tab, tr.get_text("reports", "التقارير"))

        # تبويب الإعدادات
        self.settings_tab = PurchaseSettingsView()
        self.tabs.addTab(self.settings_tab, tr.get_text("settings", "الإعدادات"))

        layout.addWidget(self.tabs)

    def refresh_all(self):
        """تحديث جميع البيانات"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'load_data'):
            current_tab.load_data()

    def print_report(self):
        """طباعة تقرير"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'print_report'):
            current_tab.print_report()
        else:
            QMessageBox.information(
                self,
                tr.get_text("info", "معلومات"),
                tr.get_text("print_not_available", "الطباعة غير متاحة في هذا القسم")
            )

    def export_data(self):
        """تصدير البيانات"""
        current_tab = self.tabs.currentWidget()
        if hasattr(current_tab, 'export_data'):
            current_tab.export_data()
        else:
            QMessageBox.information(
                self,
                tr.get_text("info", "معلومات"),
                tr.get_text("export_not_available", "التصدير غير متاح في هذا القسم")
            )

class PurchaseInvoiceView(QWidget):
    """
    واجهة فواتير المشتريات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_invoice", "إضافة فاتورة"))
        self.add_btn.clicked.connect(self.add_invoice)
        actions_layout.addWidget(self.add_btn)

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # جدول الفواتير
        self.table = StyledTable()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("supplier", "المورد"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        # ربط حدث النقر المزدوج
        self.table.doubleClicked.connect(self.view_invoice_details)

        layout.addWidget(self.table)

    def load_data(self):
        """تحميل بيانات الفواتير"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام الفواتير
            invoices = db.query(Invoice).filter(
                Invoice.invoice_type == 'purchase',
                Invoice.is_deleted == False
            ).order_by(desc(Invoice.invoice_date)).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول

            for invoice in invoices:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات الفاتورة
                self.table.setItem(row_position, 0, QTableWidgetItem(invoice.invoice_number))
                self.table.setItem(row_position, 1, QTableWidgetItem(invoice.invoice_date.strftime("%Y-%m-%d")))

                # اسم المورد
                supplier_name = invoice.supplier.name if invoice.supplier else "-"
                self.table.setItem(row_position, 2, QTableWidgetItem(supplier_name))

                # المبالغ
                self.table.setItem(row_position, 3, QTableWidgetItem(str(invoice.total_amount)))
                self.table.setItem(row_position, 4, QTableWidgetItem(str(invoice.paid_amount)))
                self.table.setItem(row_position, 5, QTableWidgetItem(str(invoice.total_amount - invoice.paid_amount)))

                # الحالة
                status_text = tr.get_text(f"status_{invoice.status}", invoice.status)
                self.table.setItem(row_position, 6, QTableWidgetItem(status_text))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات الفواتير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def add_invoice(self):
        """إضافة فاتورة جديدة"""
        from src.features.purchases.purchase_invoice_form import PurchaseInvoiceForm

        # إنشاء نموذج فاتورة جديدة
        invoice_form = PurchaseInvoiceForm(parent=self)

        # ربط إشارة حفظ الفاتورة
        invoice_form.invoice_saved.connect(self.on_invoice_saved)

        # عرض النموذج
        invoice_form.exec_()

    def view_invoice_details(self):
        """عرض تفاصيل الفاتورة المحددة"""
        # الحصول على الصف المحدد
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_invoice", "يرجى اختيار فاتورة أولاً")
            )
            return

        # الحصول على معرف الفاتورة
        invoice_id = int(self.table.item(current_row, 0).data(Qt.UserRole))

        # فتح نموذج الفاتورة في وضع التعديل
        from src.features.purchases.purchase_invoice_form import PurchaseInvoiceForm
        invoice_form = PurchaseInvoiceForm(invoice_id=invoice_id, parent=self)

        # ربط إشارة حفظ الفاتورة
        invoice_form.invoice_saved.connect(self.on_invoice_saved)

        # عرض النموذج
        invoice_form.exec_()

    def on_invoice_saved(self, invoice_id):
        """
        معالجة حدث حفظ الفاتورة
        :param invoice_id: معرف الفاتورة المحفوظة
        """
        # تحديث البيانات
        self.load_data()

        # تحديد الفاتورة في الجدول
        for row in range(self.table.rowCount()):
            item_id = self.table.item(row, 0).data(Qt.UserRole)
            if item_id == invoice_id:
                self.table.selectRow(row)
                break

class PurchaseReturnView(QWidget):
    """
    واجهة مرتجعات المشتريات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.add_btn = PrimaryButton(tr.get_text("add_return", "إضافة مرتجع"))
        self.add_btn.clicked.connect(self.add_return)
        actions_layout.addWidget(self.add_btn)

        self.refresh_btn = StyledButton(tr.get_text("refresh", "تحديث"))
        self.refresh_btn.clicked.connect(self.load_data)
        actions_layout.addWidget(self.refresh_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # جدول المرتجعات
        self.table = StyledTable()
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("return_number", "رقم المرتجع"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("supplier", "المورد"),
            tr.get_text("original_invoice", "الفاتورة الأصلية"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        # ربط حدث النقر المزدوج
        self.table.doubleClicked.connect(self.view_return_details)

        layout.addWidget(self.table)

    def load_data(self):
        """تحميل بيانات المرتجعات"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # استعلام المرتجعات
            returns = db.query(Invoice).filter(
                Invoice.invoice_type == 'purchase_return',
                Invoice.is_deleted == False
            ).order_by(desc(Invoice.invoice_date)).all()

            # عرض البيانات في الجدول
            self.table.setRowCount(0)  # مسح الجدول

            for return_invoice in returns:
                row_position = self.table.rowCount()
                self.table.insertRow(row_position)

                # إضافة بيانات المرتجع
                return_item = QTableWidgetItem(return_invoice.invoice_number)
                return_item.setData(Qt.UserRole, return_invoice.id)
                self.table.setItem(row_position, 0, return_item)

                self.table.setItem(row_position, 1, QTableWidgetItem(return_invoice.invoice_date.strftime("%Y-%m-%d")))

                # اسم المورد
                supplier_name = return_invoice.supplier.name if return_invoice.supplier else "-"
                self.table.setItem(row_position, 2, QTableWidgetItem(supplier_name))

                # الفاتورة الأصلية
                original_invoice = "-"
                if return_invoice.reference_invoice_id:
                    original_invoice_obj = db.query(Invoice).filter(
                        Invoice.id == return_invoice.reference_invoice_id
                    ).first()
                    if original_invoice_obj:
                        original_invoice = original_invoice_obj.invoice_number

                self.table.setItem(row_position, 3, QTableWidgetItem(original_invoice))

                # المبالغ
                self.table.setItem(row_position, 4, QTableWidgetItem(str(return_invoice.total)))
                self.table.setItem(row_position, 5, QTableWidgetItem(str(return_invoice.paid_amount)))
                self.table.setItem(row_position, 6, QTableWidgetItem(str(return_invoice.total - return_invoice.paid_amount)))

                # الحالة
                status_text = tr.get_text(f"status_{return_invoice.status}", return_invoice.status)
                self.table.setItem(row_position, 7, QTableWidgetItem(status_text))

        except Exception as e:
            log_error(f"خطأ في تحميل بيانات المرتجعات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_loading_data", "حدث خطأ أثناء تحميل البيانات")
            )

    def add_return(self):
        """إضافة مرتجع جديد"""
        from src.features.purchases.purchase_return_form import PurchaseReturnForm

        # إنشاء نموذج مرتجع جديد
        return_form = PurchaseReturnForm(parent=self)

        # ربط إشارة حفظ المرتجع
        return_form.invoice_saved.connect(self.on_return_saved)

        # عرض النموذج
        return_form.exec_()

    def view_return_details(self):
        """عرض تفاصيل المرتجع المحدد"""
        # الحصول على الصف المحدد
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.warning(
                self,
                tr.get_text("warning", "تحذير"),
                tr.get_text("select_return", "يرجى اختيار مرتجع أولاً")
            )
            return

        # الحصول على معرف المرتجع
        return_id = int(self.table.item(current_row, 0).data(Qt.UserRole))

        # فتح نموذج المرتجع في وضع التعديل
        from src.features.purchases.purchase_return_form import PurchaseReturnForm
        return_form = PurchaseReturnForm(invoice_id=return_id, parent=self)

        # ربط إشارة حفظ المرتجع
        return_form.invoice_saved.connect(self.on_return_saved)

        # عرض النموذج
        return_form.exec_()

    def on_return_saved(self, return_id):
        """
        معالجة حدث حفظ المرتجع
        :param return_id: معرف المرتجع المحفوظ
        """
        # تحديث البيانات
        self.load_data()

        # تحديد المرتجع في الجدول
        for row in range(self.table.rowCount()):
            item_id = self.table.item(row, 0).data(Qt.UserRole)
            if item_id == return_id:
                self.table.selectRow(row)
                break

class PurchaseReportView(QWidget):
    """
    واجهة تقارير المشتريات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # أزرار الإجراءات
        actions_layout = QHBoxLayout()

        self.generate_report_btn = PrimaryButton(tr.get_text("generate_report", "إنشاء تقرير"))
        self.generate_report_btn.clicked.connect(self.show_report_form)
        actions_layout.addWidget(self.generate_report_btn)

        actions_layout.addStretch()

        layout.addLayout(actions_layout)

        # وصف التقارير المتاحة
        reports_group = QGroupBox(tr.get_text("available_reports", "التقارير المتاحة"))
        reports_layout = QVBoxLayout(reports_group)

        # تقرير فواتير المشتريات
        purchases_report_btn = StyledButton(tr.get_text("purchase_invoices_report", "تقرير فواتير المشتريات"))
        purchases_report_btn.clicked.connect(lambda: self.show_report_form(report_type="purchases"))
        reports_layout.addWidget(purchases_report_btn)

        # تقرير مرتجعات المشتريات
        returns_report_btn = StyledButton(tr.get_text("purchase_returns_report", "تقرير مرتجعات المشتريات"))
        returns_report_btn.clicked.connect(lambda: self.show_report_form(report_type="returns"))
        reports_layout.addWidget(returns_report_btn)

        # تقرير جميع معاملات المشتريات
        all_report_btn = StyledButton(tr.get_text("all_transactions_report", "تقرير جميع معاملات المشتريات"))
        all_report_btn.clicked.connect(lambda: self.show_report_form(report_type="all"))
        reports_layout.addWidget(all_report_btn)

        # تقرير المشتريات حسب المورد
        supplier_report_btn = StyledButton(tr.get_text("purchases_by_supplier_report", "تقرير المشتريات حسب المورد"))
        supplier_report_btn.clicked.connect(lambda: self.show_report_form(report_type="supplier"))
        reports_layout.addWidget(supplier_report_btn)

        # تقرير المشتريات حسب المنتج
        product_report_btn = StyledButton(tr.get_text("purchases_by_product_report", "تقرير المشتريات حسب المنتج"))
        product_report_btn.clicked.connect(lambda: self.show_report_form(report_type="product"))
        reports_layout.addWidget(product_report_btn)

        layout.addWidget(reports_group)

        # إضافة وصف للتقارير
        description_label = StyledLabel(tr.get_text(
            "reports_description",
            "يمكنك إنشاء تقارير مفصلة عن المشتريات والمرتجعات، وتصفية النتائج حسب التاريخ والمورد والحالة. "
            "كما يمكنك تصدير التقارير إلى Excel أو CSV وطباعتها."
        ))
        description_label.setWordWrap(True)
        layout.addWidget(description_label)

        # إضافة مساحة فارغة
        layout.addStretch()

    def show_report_form(self, report_type=None):
        """
        عرض نموذج التقرير
        :param report_type: نوع التقرير (purchases, returns, all, supplier, product)
        """
        from src.features.purchases.purchase_report_form import PurchaseReportForm

        # إنشاء نموذج التقرير
        report_form = PurchaseReportForm(parent=self)

        # تعيين نوع التقرير إذا تم تحديده
        if report_type:
            if report_type == "purchases":
                report_form.purchases_radio.setChecked(True)
            elif report_type == "returns":
                report_form.returns_radio.setChecked(True)
            elif report_type == "all":
                report_form.all_radio.setChecked(True)

        # عرض النموذج
        report_form.exec_()
