"""
تقارير إدارة الموظفين
- تقرير بيانات الموظفين
- تقرير الرواتب الشهري
- تقرير الحضور والانصراف
- تقرير الإجازات
"""

import os
from datetime import datetime, date
import pandas as pd
from sqlalchemy import extract
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from src.database import get_db
from src.utils.config import get_setting
from src.utils import translation_manager as tr
from .models import Employee, Attendance, Leave, Salary

class EmployeeReports:
    """مولد تقارير الموظفين"""
    
    def __init__(self):
        # تسجيل الخطوط العربية
        font_path = os.path.join(os.path.dirname(__file__), "../../../assets/fonts/Cairo-Regular.ttf")
        pdfmetrics.registerFont(TTFont('Arabic', font_path))
        
        self.arabic_style = ParagraphStyle(
            'Arabic',
            fontName='Arabic',
            fontSize=12,
            leading=14,
            alignment=1  # center
        )
    
    def _create_pdf(self, filename, elements):
        """إنشاء ملف PDF"""
        doc = SimpleDocTemplate(
            filename,
            pagesize=landscape(A4),
            rightMargin=30,
            leftMargin=30,
            topMargin=30,
            bottomMargin=30
        )
        doc.build(elements)
        
    def export_employee_list(self, output_format='pdf'):
        """تصدير قائمة الموظفين"""
        try:
            db = next(get_db())
            employees = db.query(Employee).filter_by(is_deleted=False).all()
            
            data = []
            for emp in employees:
                data.append([
                    emp.employee_id,
                    emp.name,
                    emp.position,
                    emp.department,
                    emp.join_date.strftime("%Y-%m-%d"),
                    str(emp.base_salary)
                ])
                
            headers = [
                tr.get_text("employee_id"),
                tr.get_text("name"),
                tr.get_text("position"),
                tr.get_text("department"),
                tr.get_text("join_date"),
                tr.get_text("base_salary")
            ]
            
            if output_format == 'pdf':
                filename = f"employee_list_{datetime.now().strftime('%Y%m%d')}.pdf"
                elements = []
                
                # العنوان
                title = Paragraph(
                    tr.get_text("employee_list_report_title"),
                    self.arabic_style
                )
                elements.append(title)
                
                # الجدول
                table = Table([headers] + data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Arabic'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), 'Arabic'),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(table)
                
                self._create_pdf(filename, elements)
                return filename
                
            elif output_format == 'excel':
                filename = f"employee_list_{datetime.now().strftime('%Y%m%d')}.xlsx"
                df = pd.DataFrame(data, columns=headers)
                df.to_excel(filename, index=False, sheet_name=tr.get_text("employee_list"))
                return filename
                
        except Exception as e:
            raise Exception(f"Error generating employee list report: {str(e)}")
            
    def generate_salary_report(self, month: date, output_format='pdf'):
        """إنشاء تقرير الرواتب الشهري"""
        try:
            db = next(get_db())
            salaries = db.query(Salary).filter(
                extract('year', Salary.month) == month.year,
                extract('month', Salary.month) == month.month
            ).all()
            
            data = []
            total_salaries = 0
            for salary in salaries:
                emp = salary.employee
                row = [
                    emp.employee_id,
                    emp.name,
                    str(salary.base_salary),
                    str(salary.allowances),
                    str(salary.deductions),
                    str(salary.overtime),
                    str(salary.net_salary)
                ]
                data.append(row)
                total_salaries += salary.net_salary
                
            headers = [
                tr.get_text("employee_id"),
                tr.get_text("name"),
                tr.get_text("base_salary"),
                tr.get_text("allowances"),
                tr.get_text("deductions"),
                tr.get_text("overtime"),
                tr.get_text("net_salary")
            ]
            
            if output_format == 'pdf':
                filename = f"salary_report_{month.strftime('%Y%m')}.pdf"
                elements = []
                
                # العنوان
                title = Paragraph(
                    f"{tr.get_text('salary_report_title')} - {month.strftime('%Y/%m')}",
                    self.arabic_style
                )
                elements.append(title)
                
                # الجدول
                table = Table([headers] + data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Arabic'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), 'Arabic'),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(table)
                
                # إجمالي الرواتب
                total = Paragraph(
                    f"{tr.get_text('total_salaries')}: {total_salaries}",
                    self.arabic_style
                )
                elements.append(total)
                
                self._create_pdf(filename, elements)
                return filename
                
            elif output_format == 'excel':
                filename = f"salary_report_{month.strftime('%Y%m')}.xlsx"
                df = pd.DataFrame(data, columns=headers)
                df.to_excel(filename, index=False, sheet_name=tr.get_text("salary_report"))
                return filename
                
        except Exception as e:
            raise Exception(f"Error generating salary report: {str(e)}")
            
    def generate_attendance_report(self, start_date: date, end_date: date, output_format='pdf'):
        """إنشاء تقرير الحضور والانصراف"""
        try:
            db = next(get_db())
            attendances = db.query(Attendance).filter(
                Attendance.date.between(start_date, end_date)
            ).order_by(Attendance.date).all()
            
            data = []
            for att in attendances:
                emp = att.employee
                row = [
                    emp.employee_id,
                    emp.name,
                    att.date.strftime("%Y-%m-%d"),
                    att.check_in.strftime("%H:%M") if att.check_in else "",
                    att.check_out.strftime("%H:%M") if att.check_out else "",
                    str(att.late_minutes),
                    str(att.overtime_minutes),
                    att.status
                ]
                data.append(row)
                
            headers = [
                tr.get_text("employee_id"),
                tr.get_text("name"),
                tr.get_text("date"),
                tr.get_text("check_in"),
                tr.get_text("check_out"),
                tr.get_text("late_minutes"),
                tr.get_text("overtime_minutes"),
                tr.get_text("status")
            ]
            
            if output_format == 'pdf':
                filename = f"attendance_report_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.pdf"
                elements = []
                
                # العنوان
                title = Paragraph(
                    f"{tr.get_text('attendance_report_title')} ({start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')})",
                    self.arabic_style
                )
                elements.append(title)
                
                # الجدول
                table = Table([headers] + data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Arabic'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('FONTNAME', (0, 1), (-1, -1), 'Arabic'),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(table)
                
                self._create_pdf(filename, elements)
                return filename
                
            elif output_format == 'excel':
                filename = f"attendance_report_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}.xlsx"
                df = pd.DataFrame(data, columns=headers)
                df.to_excel(filename, index=False, sheet_name=tr.get_text("attendance_report"))
                return filename
                
        except Exception as e:
            raise Exception(f"Error generating attendance report: {str(e)}")