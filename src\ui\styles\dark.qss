/* Dark Theme for Amin Al-<PERSON>t */

/* General */
QWidget {
    background-color: #121212;
    color: #FFFFFF;
    font-family: 'Cairo', 'Segoe UI', 'Arial', sans-serif;
}

/* Main Window */
QMainWindow {
    background-color: #121212;
}

/* Central Widget */
QMainWindow::centralWidget {
    background-color: #121212;
}

/* Input Fields */
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border-color: #333333;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #2196F3;
}

QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled {
    background-color: #242424;
    color: #757575;
}

/* Combo Box */
QComboBox {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border-color: #333333;
}

QComboBox:focus {
    border-color: #2196F3;
}

QComboBox::drop-down {
    background-color: #1E1E1E;
}

QComboBox::down-arrow {
    image: url(:/icons/arrow_down_light.png);
}

/* Tables */
QTableView {
    background-color: #1E1E1E;
    alternate-background-color: #242424;
    border: 1px solid #333333;
    gridline-color: #333333;
    border-radius: 5px;
    selection-background-color: #0D47A1;
    selection-color: #FFFFFF;
}

QTableView::item {
    padding: 5px;
    border-bottom: 1px solid #333333;
}

QTableView::item:selected {
    background-color: #0D47A1;
    color: #FFFFFF;
}

QTableView::item:hover:!selected {
    background-color: #2C2C2C;
}

QHeaderView::section {
    background-color: #242424;
    color: #FFFFFF;
    border: 1px solid #333333;
    padding: 5px;
    font-weight: bold;
}

QHeaderView::section:hover {
    background-color: #2C2C2C;
}

/* Scroll Bars */
QScrollBar:vertical {
    background-color: #1E1E1E;
    width: 12px;
    margin: 12px 0 12px 0;
    border-radius: 6px;
    border: none;
}

QScrollBar:horizontal {
    background-color: #1E1E1E;
    height: 12px;
    margin: 0 12px 0 12px;
    border-radius: 6px;
    border: none;
}

QScrollBar::handle {
    background-color: #424242;
    border-radius: 5px;
    border: none;
    min-height: 30px;
}

QScrollBar::handle:hover {
    background-color: #616161;
}

QScrollBar::handle:pressed {
    background-color: #757575;
}

QScrollBar::add-line:vertical {
    border: none;
    background-color: #1E1E1E;
    height: 12px;
    subcontrol-position: bottom;
    subcontrol-origin: margin;
    border-radius: 3px;
}

QScrollBar::sub-line:vertical {
    border: none;
    background-color: #1E1E1E;
    height: 12px;
    subcontrol-position: top;
    subcontrol-origin: margin;
    border-radius: 3px;
}

QScrollBar::add-line:horizontal {
    border: none;
    background-color: #1E1E1E;
    width: 12px;
    subcontrol-position: right;
    subcontrol-origin: margin;
    border-radius: 3px;
}

QScrollBar::sub-line:horizontal {
    border: none;
    background-color: #1E1E1E;
    width: 12px;
    subcontrol-position: left;
    subcontrol-origin: margin;
    border-radius: 3px;
}

QScrollBar::add-page, QScrollBar::sub-page {
    background: none;
}

/* Tab Widget */
QTabWidget::pane {
    background-color: #1E1E1E;
    border-color: #333333;
}

QTabBar::tab {
    background-color: #242424;
    color: #BDBDBD;
    border: 1px solid #333333;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #1E1E1E;
    color: #2196F3;
}

QTabBar::tab:hover:!selected {
    background-color: #2C2C2C;
}

/* Menu */
QMenu {
    background-color: #1E1E1E;
    border: 1px solid #333333;
    border-radius: 5px;
    padding: 5px;
}

QMenu::item {
    color: #FFFFFF;
    padding: 8px 25px 8px 8px;
    border-radius: 3px;
    margin: 2px;
}

QMenu::item:selected {
    background-color: #0D47A1;
    color: #FFFFFF;
}

QMenu::item:disabled {
    color: #757575;
}

QMenu::separator {
    height: 1px;
    background-color: #333333;
    margin: 5px 10px;
}

QMenu::indicator {
    width: 18px;
    height: 18px;
}

QMenu::icon {
    padding-left: 5px;
}

/* Separators */
QFrame[frameShape="4"] {
    background-color: #333333;
}

/* Tool Tips */
QToolTip {
    background-color: #424242;
    color: #FFFFFF;
    border: 1px solid #616161;
}

/* Message Box */
QMessageBox {
    background-color: #1E1E1E;
}

/* Progress Bar */
QProgressBar {
    background-color: #242424;
    color: #FFFFFF;
}

QProgressBar::chunk {
    background-color: #2196F3;
}

/* Group Box */
QGroupBox {
    border: 1px solid #333333;
    background-color: #1E1E1E;
    margin-top: 1.5ex;
}

QGroupBox::title {
    color: #FFFFFF;
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #1E1E1E;
}

/* Status Bar */
QStatusBar {
    background-color: #242424;
    color: #FFFFFF;
    border-top: 1px solid #333333;
}

/* Tool Bar */
QToolBar {
    background-color: #242424;
    border-bottom: 1px solid #333333;
}

/* Radio Buttons */
QRadioButton {
    color: #FFFFFF;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
}

/* Checkboxes */
QCheckBox {
    color: #FFFFFF;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
}

/* Spin Boxes */
QSpinBox, QDoubleSpinBox {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border-color: #333333;
}

/* Calendar Widget */
QCalendarWidget QToolButton {
    color: #FFFFFF;
    background-color: #1E1E1E;
}

QCalendarWidget QMenu {
    background-color: #1E1E1E;
}

QCalendarWidget QSpinBox {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border-color: #333333;
}

/* Date Edit */
QDateEdit {
    background-color: #1E1E1E;
    color: #FFFFFF;
    border-color: #333333;
}

/* Buttons */
QPushButton {
    background-color: #2A2A2A;
    color: #FFFFFF;
    border: none;
    border-radius: 5px;
    padding: 8px 16px;
    min-height: 30px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #3A3A3A;
}

QPushButton:pressed {
    background-color: #1A1A1A;
}

QPushButton:disabled {
    background-color: #1E1E1E;
    color: #757575;
}

/* Primary Button in Dark Theme */
QPushButton[primary="true"] {
    background-color: #1976D2;
    color: white;
}

QPushButton[primary="true"]:hover {
    background-color: #1E88E5;
}

QPushButton[primary="true"]:pressed {
    background-color: #1565C0;
}

QPushButton[primary="true"]:disabled {
    background-color: #1E1E1E;
    color: #757575;
}

/* Success Button in Dark Theme */
QPushButton[success="true"] {
    background-color: #2E7D32;
    color: white;
}

QPushButton[success="true"]:hover {
    background-color: #388E3C;
}

QPushButton[success="true"]:pressed {
    background-color: #1B5E20;
}

/* Danger Button in Dark Theme */
QPushButton[danger="true"] {
    background-color: #D32F2F;
    color: white;
}

QPushButton[danger="true"]:hover {
    background-color: #E53935;
}

QPushButton[danger="true"]:pressed {
    background-color: #C62828;
}

/* Warning Button in Dark Theme */
QPushButton[warning="true"] {
    background-color: #F57C00;
    color: white;
}

QPushButton[warning="true"]:hover {
    background-color: #FB8C00;
}

QPushButton[warning="true"]:pressed {
    background-color: #EF6C00;
}

/* Flat Button in Dark Theme */
QPushButton[flat="true"] {
    background-color: transparent;
    color: #2196F3;
    border: none;
}

QPushButton[flat="true"]:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

QPushButton[flat="true"]:pressed {
    background-color: rgba(33, 150, 243, 0.2);
}

/* Icon Button in Dark Theme */
QPushButton[icon="true"] {
    background-color: transparent;
    border-radius: 20px;
    min-width: 40px;
    min-height: 40px;
    padding: 8px;
}

QPushButton[icon="true"]:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

QPushButton[icon="true"]:pressed {
    background-color: rgba(255, 255, 255, 0.2);
}