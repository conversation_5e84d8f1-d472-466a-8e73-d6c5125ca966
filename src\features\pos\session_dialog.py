#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة إدارة جلسة POS
"""

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QSpinBox, QDoubleSpinBox, QDateTimeEdit, QGroupBox,
    QMessageBox, QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt5.QtCore import Qt, QDateTime, pyqtSignal
from PyQt5.QtGui import QFont
import qtawesome as qta
from datetime import datetime

from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledComboBox, StyledLabel, <PERSON>er<PERSON><PERSON>l,
    StyledTable, StyledTextEdit, StyledSpinBox
)
from src.utils import translation_manager as tr
from src.utils.logger import log_info, log_error
from src.database import get_db
from src.models.pos_session import POSSession, POSSessionStatus, CashMovement

class SessionDialog(QDialog):
    """نافذة إدارة جلسة POS"""

    session_created = pyqtSignal(object)  # إشارة عند إنشاء جلسة جديدة
    session_closed = pyqtSignal(object)   # إشارة عند إغلاق جلسة

    def __init__(self, session=None, parent=None):
        super().__init__(parent)
        self.session = session
        self.db = next(get_db())
        self.is_new_session = session is None

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        if self.is_new_session:
            self.setWindowTitle(tr.get_text("new_pos_session", "جلسة POS جديدة"))
        else:
            self.setWindowTitle(tr.get_text("pos_session_details", "تفاصيل جلسة POS"))

        self.setMinimumSize(600, 500)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # العنوان
        if self.is_new_session:
            header = HeaderLabel(tr.get_text("start_new_session", "بدء جلسة جديدة"))
        else:
            header = HeaderLabel(tr.get_text("session_details", "تفاصيل الجلسة"))
        layout.addWidget(header)

        # معلومات الجلسة
        session_group = QGroupBox(tr.get_text("session_info", "معلومات الجلسة"))
        session_layout = QFormLayout(session_group)

        # رقم الجلسة
        self.session_number_input = StyledLineEdit()
        self.session_number_input.setReadOnly(not self.is_new_session)
        session_layout.addRow(StyledLabel(tr.get_text("session_number", "رقم الجلسة:")), self.session_number_input)

        # تاريخ ووقت البداية
        self.start_datetime_input = QDateTimeEdit()
        self.start_datetime_input.setDateTime(QDateTime.currentDateTime())
        self.start_datetime_input.setReadOnly(not self.is_new_session)
        session_layout.addRow(StyledLabel(tr.get_text("start_time", "وقت البداية:")), self.start_datetime_input)

        # المبلغ الافتتاحي
        self.opening_cash_input = StyledSpinBox()
        self.opening_cash_input.setMaximum(999999)
        self.opening_cash_input.setReadOnly(not self.is_new_session)
        session_layout.addRow(StyledLabel(tr.get_text("opening_cash", "المبلغ الافتتاحي:")), self.opening_cash_input)

        # ملاحظات
        self.notes_input = StyledTextEdit()
        self.notes_input.setMaximumHeight(80)
        session_layout.addRow(StyledLabel(tr.get_text("notes", "ملاحظات:")), self.notes_input)

        layout.addWidget(session_group)

        # إحصائيات الجلسة (للجلسات الموجودة)
        if not self.is_new_session:
            stats_group = QGroupBox(tr.get_text("session_statistics", "إحصائيات الجلسة"))
            stats_layout = QFormLayout(stats_group)

            self.total_sales_label = StyledLabel("0.00")
            stats_layout.addRow(StyledLabel(tr.get_text("total_sales", "إجمالي المبيعات:")), self.total_sales_label)

            self.transactions_count_label = StyledLabel("0")
            stats_layout.addRow(StyledLabel(tr.get_text("transactions_count", "عدد المعاملات:")), self.transactions_count_label)

            self.cash_in_drawer_label = StyledLabel("0.00")
            stats_layout.addRow(StyledLabel(tr.get_text("cash_in_drawer", "النقد في الدرج:")), self.cash_in_drawer_label)

            layout.addWidget(stats_group)

            # حركات النقد
            movements_group = QGroupBox(tr.get_text("cash_movements", "حركات النقد"))
            movements_layout = QVBoxLayout(movements_group)

            self.movements_table = StyledTable()
            self.movements_table.setColumnCount(4)
            self.movements_table.setHorizontalHeaderLabels([
                tr.get_text("time", "الوقت"),
                tr.get_text("type", "النوع"),
                tr.get_text("amount", "المبلغ"),
                tr.get_text("reason", "السبب")
            ])

            header = self.movements_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
            header.setSectionResizeMode(3, QHeaderView.Stretch)

            movements_layout.addWidget(self.movements_table)
            layout.addWidget(movements_group)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        if self.is_new_session:
            cancel_btn = StyledButton(tr.get_text("cancel", "إلغاء"))
            cancel_btn.clicked.connect(self.reject)
            buttons_layout.addWidget(cancel_btn)

            buttons_layout.addStretch()

            start_btn = PrimaryButton(tr.get_text("start_session", "بدء الجلسة"))
            start_btn.setIcon(qta.icon("fa5s.play"))
            start_btn.clicked.connect(self.start_session)
            buttons_layout.addWidget(start_btn)
        else:
            close_btn = StyledButton(tr.get_text("close", "إغلاق"))
            close_btn.clicked.connect(self.accept)
            buttons_layout.addWidget(close_btn)

            buttons_layout.addStretch()

            if self.session and self.session.status == POSSessionStatus.ACTIVE:
                end_session_btn = DangerButton(tr.get_text("end_session", "إنهاء الجلسة"))
                end_session_btn.setIcon(qta.icon("fa5s.stop"))
                end_session_btn.clicked.connect(self.end_session)
                buttons_layout.addWidget(end_session_btn)

        layout.addLayout(buttons_layout)

    def load_data(self):
        """تحميل البيانات"""
        if self.is_new_session:
            # إنشاء رقم جلسة جديد
            session_number = self.generate_session_number()
            self.session_number_input.setText(session_number)
        else:
            # تحميل بيانات الجلسة الموجودة
            self.session_number_input.setText(self.session.session_number)
            self.start_datetime_input.setDateTime(QDateTime.fromString(
                self.session.start_time.isoformat(), Qt.ISODate
            ))
            self.opening_cash_input.setValue(int(self.session.opening_cash))
            self.notes_input.setPlainText(self.session.notes or "")

            # تحميل الإحصائيات
            self.load_statistics()

            # تحميل حركات النقد
            self.load_cash_movements()

    def generate_session_number(self):
        """إنشاء رقم جلسة جديد"""
        try:
            # الحصول على آخر جلسة
            last_session = self.db.query(POSSession).order_by(POSSession.id.desc()).first()

            if last_session:
                # استخراج الرقم من آخر جلسة وزيادته
                last_number = int(last_session.session_number.split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1

            # تنسيق رقم الجلسة
            today = datetime.now().strftime("%Y%m%d")
            return f"POS-{today}-{new_number:04d}"

        except Exception as e:
            log_error(f"خطأ في إنشاء رقم الجلسة: {str(e)}")
            # رقم افتراضي في حالة الخطأ
            return f"POS-{datetime.now().strftime('%Y%m%d')}-0001"

    def load_statistics(self):
        """تحميل إحصائيات الجلسة"""
        if not self.session:
            return

        try:
            # حساب إجمالي المبيعات
            total_sales = sum(t.total_amount for t in self.session.transactions)
            self.total_sales_label.setText(f"{total_sales:.2f}")

            # عدد المعاملات
            transactions_count = len(self.session.transactions)
            self.transactions_count_label.setText(str(transactions_count))

            # النقد في الدرج
            cash_in_drawer = self.session.calculate_cash_in_drawer()
            self.cash_in_drawer_label.setText(f"{cash_in_drawer:.2f}")

        except Exception as e:
            log_error(f"خطأ في تحميل إحصائيات الجلسة: {str(e)}")

    def load_cash_movements(self):
        """تحميل حركات النقد"""
        if not self.session:
            return

        try:
            movements = self.db.query(CashMovement).filter(
                CashMovement.session_id == self.session.id
            ).order_by(CashMovement.created_at.desc()).all()

            self.movements_table.setRowCount(len(movements))

            for row, movement in enumerate(movements):
                # الوقت
                time_item = QTableWidgetItem(movement.created_at.strftime("%H:%M:%S"))
                time_item.setFlags(time_item.flags() & ~Qt.ItemIsEditable)
                self.movements_table.setItem(row, 0, time_item)

                # النوع
                type_text = "إيداع" if movement.movement_type == "deposit" else "سحب"
                type_item = QTableWidgetItem(type_text)
                type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
                self.movements_table.setItem(row, 1, type_item)

                # المبلغ
                amount_item = QTableWidgetItem(f"{movement.amount:.2f}")
                amount_item.setFlags(amount_item.flags() & ~Qt.ItemIsEditable)
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.movements_table.setItem(row, 2, amount_item)

                # السبب
                reason_item = QTableWidgetItem(movement.reason or "")
                reason_item.setFlags(reason_item.flags() & ~Qt.ItemIsEditable)
                self.movements_table.setItem(row, 3, reason_item)

        except Exception as e:
            log_error(f"خطأ في تحميل حركات النقد: {str(e)}")

    def start_session(self):
        """بدء جلسة جديدة"""
        try:
            # التحقق من البيانات
            session_number = self.session_number_input.text().strip()
            if not session_number:
                QMessageBox.warning(
                    self,
                    tr.get_text("validation_error", "خطأ في التحقق"),
                    tr.get_text("session_number_required", "رقم الجلسة مطلوب")
                )
                return

            opening_cash = self.opening_cash_input.value()

            # إنشاء جلسة جديدة
            new_session = POSSession(
                session_number=session_number,
                user_id=1,  # TODO: الحصول على المستخدم الحالي
                start_time=self.start_datetime_input.dateTime().toPyDateTime(),
                opening_cash=opening_cash,
                status=POSSessionStatus.ACTIVE,
                notes=self.notes_input.toPlainText()
            )

            self.db.add(new_session)
            self.db.commit()

            log_info(f"تم بدء جلسة POS جديدة: {session_number}")

            # إرسال إشارة
            self.session_created.emit(new_session)

            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("session_started", "تم بدء الجلسة بنجاح")
            )

            self.accept()

        except Exception as e:
            log_error(f"خطأ في بدء الجلسة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_starting_session", "حدث خطأ أثناء بدء الجلسة")
            )

    def end_session(self):
        """إنهاء الجلسة الحالية"""
        try:
            # تأكيد الإنهاء
            reply = QMessageBox.question(
                self,
                tr.get_text("confirm_end_session", "تأكيد إنهاء الجلسة"),
                tr.get_text("confirm_end_session_message", "هل أنت متأكد من إنهاء الجلسة الحالية؟"),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # إنهاء الجلسة
            self.session.end_time = datetime.now()
            self.session.status = POSSessionStatus.CLOSED
            self.session.closing_cash = self.session.calculate_cash_in_drawer()

            self.db.commit()

            log_info(f"تم إنهاء جلسة POS: {self.session.session_number}")

            # إرسال إشارة
            self.session_closed.emit(self.session)

            QMessageBox.information(
                self,
                tr.get_text("success", "نجاح"),
                tr.get_text("session_ended", "تم إنهاء الجلسة بنجاح")
            )

            self.accept()

        except Exception as e:
            log_error(f"خطأ في إنهاء الجلسة: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error", "خطأ"),
                tr.get_text("error_ending_session", "حدث خطأ أثناء إنهاء الجلسة")
            )

# إضافة alias للتوافق مع الكود الموجود
SessionStartDialog = SessionDialog
