#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهات التقارير
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QRadioButton, QFileDialog
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QIcon
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

from datetime import datetime, timedelta
import os
import pandas as pd

from src.database import get_db
from src.models import Invoice, InvoiceItem, Customer, Supplier, Product, Expense, Income
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info
from src.utils.print_manager import PrintManager

class ReportsView(QWidget):
    """
    واجهة التقارير الرئيسية
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("reports_management", "إدارة التقارير"))
        layout.addWidget(header)

        # علامات التبويب
        tabs = QTabWidget()

        # تبويب تقارير المبيعات
        sales_tab = SalesReportView()
        tabs.addTab(sales_tab, tr.get_text("sales_reports", "تقارير المبيعات"))

        # تبويب تقارير المشتريات
        purchases_tab = PurchasesReportView()
        tabs.addTab(purchases_tab, tr.get_text("purchases_reports", "تقارير المشتريات"))

        # تبويب تقارير المخزون
        inventory_tab = InventoryReportView()
        tabs.addTab(inventory_tab, tr.get_text("inventory_reports", "تقارير المخزون"))

        # تبويب تقارير الأرباح والخسائر
        profit_loss_tab = ProfitLossReportView()
        tabs.addTab(profit_loss_tab, tr.get_text("profit_loss_reports", "تقارير الأرباح والخسائر"))

        # تبويب تقارير الخزينة
        treasury_tab = TreasuryReportView()
        tabs.addTab(treasury_tab, tr.get_text("treasury_reports", "تقارير الخزينة"))

        layout.addWidget(tabs)

class SalesReportView(QWidget):
    """
    واجهة تقارير المبيعات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # مجموعة الفلاتر
        filter_group = QGroupBox(tr.get_text("filters", "الفلاتر"))
        filter_layout = QHBoxLayout(filter_group)

        # فلتر التاريخ
        date_layout = QVBoxLayout()
        date_layout.addWidget(StyledLabel(tr.get_text("date_range", "نطاق التاريخ")))

        date_range_layout = QHBoxLayout()

        self.start_date = StyledDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        date_range_layout.addWidget(self.start_date)

        date_range_layout.addWidget(StyledLabel(" - "))

        self.end_date = StyledDateEdit()
        self.end_date.setDate(QDate.currentDate())
        date_range_layout.addWidget(self.end_date)

        date_layout.addLayout(date_range_layout)
        filter_layout.addLayout(date_layout)

        # فلتر العميل
        customer_layout = QVBoxLayout()
        customer_layout.addWidget(StyledLabel(tr.get_text("customer", "العميل")))

        self.customer_combo = StyledComboBox()
        self.customer_combo.addItem(tr.get_text("all_customers", "جميع العملاء"), None)
        # TODO: تحميل قائمة العملاء

        customer_layout.addWidget(self.customer_combo)
        filter_layout.addLayout(customer_layout)

        # زر تطبيق الفلتر
        filter_btn_layout = QVBoxLayout()
        filter_btn_layout.addStretch()

        self.apply_filter_btn = PrimaryButton(tr.get_text("apply_filter", "تطبيق الفلتر"))
        self.apply_filter_btn.clicked.connect(self.load_data)
        filter_btn_layout.addWidget(self.apply_filter_btn)

        filter_layout.addLayout(filter_btn_layout)

        layout.addWidget(filter_group)

        # أزرار التصدير
        export_layout = QHBoxLayout()

        self.export_excel_btn = StyledButton(tr.get_text("export_excel", "تصدير إلى Excel"))
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        export_layout.addWidget(self.export_excel_btn)

        self.export_pdf_btn = StyledButton(tr.get_text("export_pdf", "تصدير إلى PDF"))
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)
        export_layout.addWidget(self.export_pdf_btn)

        self.print_btn = StyledButton(tr.get_text("print", "طباعة"))
        self.print_btn.clicked.connect(self.print_report)
        export_layout.addWidget(self.print_btn)

        export_layout.addStretch()

        layout.addLayout(export_layout)

        # جدول التقرير
        self.table = StyledTable()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("customer", "العميل"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        layout.addWidget(self.table)

        # ملخص التقرير
        summary_layout = QHBoxLayout()

        self.total_invoices_label = StyledLabel(tr.get_text("total_invoices", "إجمالي الفواتير: 0"))
        summary_layout.addWidget(self.total_invoices_label)

        self.total_amount_label = StyledLabel(tr.get_text("total_amount", "إجمالي المبلغ: 0"))
        summary_layout.addWidget(self.total_amount_label)

        self.total_paid_label = StyledLabel(tr.get_text("total_paid", "إجمالي المدفوع: 0"))
        summary_layout.addWidget(self.total_paid_label)

        self.total_remaining_label = StyledLabel(tr.get_text("total_remaining", "إجمالي المتبقي: 0"))
        summary_layout.addWidget(self.total_remaining_label)

        layout.addLayout(summary_layout)

    def load_data(self):
        """تحميل بيانات التقرير"""
        # TODO: تنفيذ تحميل بيانات التقرير
        QMessageBox.information(
            self,
            tr.get_text("info", "معلومات"),
            tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً")
        )

    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        try:
            # الحصول على مسار الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                tr.get_text("save_excel_file", "حفظ ملف Excel"),
                "",
                "Excel Files (*.xlsx)"
            )

            if not file_path:
                return

            # إنشاء بيانات التقرير
            data = []
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # إنشاء DataFrame
            import pandas as pd
            headers = [self.table.horizontalHeaderItem(col).text() for col in range(self.table.columnCount())]
            df = pd.DataFrame(data, columns=headers)

            # حفظ الملف
            df.to_excel(file_path, index=False)

            QMessageBox.information(
                self,
                tr.get_text("success_title", "نجاح"),
                tr.get_text("excel_export_success", "تم تصدير التقرير إلى Excel بنجاح")
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى Excel: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("excel_export_error", "حدث خطأ أثناء تصدير التقرير إلى Excel")
            )

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        try:
            # إنشاء بيانات التقرير
            report_data = self.get_report_data()

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # تصدير التقرير إلى PDF
            print_manager.export_to_pdf(
                html_content=self.get_report_html(),
                title=tr.get_text("sales_report", "تقرير المبيعات"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى PDF: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("pdf_export_error", "حدث خطأ أثناء تصدير التقرير إلى PDF")
            )

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء بيانات التقرير
            report_data = self.get_report_data()

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # عرض معاينة الطباعة
            print_manager.print_preview(
                html_content=self.get_report_html(),
                title=tr.get_text("sales_report", "تقرير المبيعات"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("print_error", "حدث خطأ أثناء طباعة التقرير")
            )

    def get_report_data(self):
        """الحصول على بيانات التقرير"""
        # جمع بيانات التقرير
        data = {
            'report_title': tr.get_text("sales_report", "تقرير المبيعات"),
            'date_range': f"{self.start_date.date().toString('yyyy-MM-dd')} - {self.end_date.date().toString('yyyy-MM-dd')}",
            'generated_by': tr.get_text("system", "النظام"),
            'report_id': f"SR-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'table_headers': self.get_table_headers_html(),
            'table_rows': self.get_table_rows_html(),
            'summary_title': tr.get_text("summary", "الملخص"),
            'summary_rows': self.get_summary_rows_html(),
            'chart_title': tr.get_text("sales_chart", "رسم بياني للمبيعات"),
            'chart_placeholder_text': tr.get_text("chart_not_available", "الرسم البياني غير متاح حالياً"),
            'footer_text': tr.get_text("report_footer", "تم إنشاء هذا التقرير بواسطة برنامج أمين الحسابات"),
            'printed_date_label': tr.get_text("printed_date", "تاريخ الطباعة")
        }

        return data

    def get_report_html(self):
        """الحصول على HTML للتقرير"""
        # الحصول على مدير الطباعة
        print_manager = PrintManager.get_instance()

        # تحويل قالب التقرير
        return print_manager.render_template('report_general', self.get_report_data())

    def get_table_headers_html(self):
        """الحصول على HTML لرؤوس الجدول"""
        html = ""
        for col in range(self.table.columnCount()):
            header = self.table.horizontalHeaderItem(col).text()
            html += f"<th>{header}</th>"
        return html

    def get_table_rows_html(self):
        """الحصول على HTML لصفوف الجدول"""
        html = ""
        for row in range(self.table.rowCount()):
            html += "<tr>"
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                text = item.text() if item else ""

                # تطبيق تنسيق خاص للأعمدة المالية
                if col in [3, 4, 5]:  # الإجمالي، المدفوع، المتبقي
                    html += f'<td class="amount">{text}</td>'
                else:
                    html += f"<td>{text}</td>"

            html += "</tr>"
        return html

    def get_summary_rows_html(self):
        """الحصول على HTML لصفوف الملخص"""
        html = ""

        # إجمالي الفواتير
        total_invoices = self.table.rowCount()
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_invoices", "إجمالي الفواتير")}:</span> <span>{total_invoices}</span></div>'

        # إجمالي المبلغ
        total_amount_text = self.total_amount_label.text().split(": ")[1]
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_amount", "إجمالي المبلغ")}:</span> <span>{total_amount_text}</span></div>'

        # إجمالي المدفوع
        total_paid_text = self.total_paid_label.text().split(": ")[1]
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_paid", "إجمالي المدفوع")}:</span> <span>{total_paid_text}</span></div>'

        # إجمالي المتبقي
        total_remaining_text = self.total_remaining_label.text().split(": ")[1]
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_remaining", "إجمالي المتبقي")}:</span> <span>{total_remaining_text}</span></div>'

        return html

class PurchasesReportView(QWidget):
    """
    واجهة تقارير المشتريات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

class InventoryReportView(QWidget):
    """
    واجهة تقارير المخزون
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

class ProfitLossReportView(QWidget):
    """
    واجهة تقارير الأرباح والخسائر
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

class TreasuryReportView(QWidget):
    """
    واجهة تقارير الخزينة
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
