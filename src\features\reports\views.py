#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
واجهات التقارير
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QMessageBox, QDialog, QComboBox, QDateEdit, QLineEdit,
    QTextEdit, QCheckBox, QSpinBox, QDoubleSpinBox, QGroupBox,
    QRadioButton, QFileDialog
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QIcon
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

from datetime import datetime, timedelta
import os
import pandas as pd

from src.database import get_db
from src.models import Invoice, InvoiceItem, Customer, Supplier, Product, Expense, Income
from src.ui.widgets.base_widgets import (
    StyledButton, PrimaryButton, DangerButton,
    StyledLineEdit, StyledTextEdit, StyledComboBox,
    StyledCheckBox, StyledDateEdit, StyledLabel,
    HeaderLabel, StyledTable
)
from src.ui.styles.theme_colors import get_module_color
from src.utils import translation_manager as tr, log_error, log_info
from src.utils.print_manager import PrintManager

class ReportsView(QWidget):
    """
    واجهة التقارير الرئيسية
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("reports_management", "إدارة التقارير"))
        layout.addWidget(header)

        # علامات التبويب
        tabs = QTabWidget()

        # تبويب تقارير المبيعات
        sales_tab = SalesReportView()
        tabs.addTab(sales_tab, tr.get_text("sales_reports", "تقارير المبيعات"))

        # تبويب تقارير المشتريات
        purchases_tab = PurchasesReportView()
        tabs.addTab(purchases_tab, tr.get_text("purchases_reports", "تقارير المشتريات"))

        # تبويب تقارير المخزون
        inventory_tab = InventoryReportView()
        tabs.addTab(inventory_tab, tr.get_text("inventory_reports", "تقارير المخزون"))

        # تبويب تقارير الأرباح والخسائر
        profit_loss_tab = ProfitLossReportView()
        tabs.addTab(profit_loss_tab, tr.get_text("profit_loss_reports", "تقارير الأرباح والخسائر"))

        # تبويب تقارير الخزينة
        treasury_tab = TreasuryReportView()
        tabs.addTab(treasury_tab, tr.get_text("treasury_reports", "تقارير الخزينة"))

        layout.addWidget(tabs)

class SalesReportView(QWidget):
    """
    واجهة تقارير المبيعات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # مجموعة الفلاتر
        filter_group = QGroupBox(tr.get_text("filters", "الفلاتر"))
        filter_layout = QHBoxLayout(filter_group)

        # فلتر التاريخ
        date_layout = QVBoxLayout()
        date_layout.addWidget(StyledLabel(tr.get_text("date_range", "نطاق التاريخ")))

        date_range_layout = QHBoxLayout()

        self.start_date = StyledDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        date_range_layout.addWidget(self.start_date)

        date_range_layout.addWidget(StyledLabel(" - "))

        self.end_date = StyledDateEdit()
        self.end_date.setDate(QDate.currentDate())
        date_range_layout.addWidget(self.end_date)

        date_layout.addLayout(date_range_layout)
        filter_layout.addLayout(date_layout)

        # فلتر العميل
        customer_layout = QVBoxLayout()
        customer_layout.addWidget(StyledLabel(tr.get_text("customer", "العميل")))

        self.customer_combo = StyledComboBox()
        self.customer_combo.addItem(tr.get_text("all_customers", "جميع العملاء"), None)
        # TODO: تحميل قائمة العملاء

        customer_layout.addWidget(self.customer_combo)
        filter_layout.addLayout(customer_layout)

        # زر تطبيق الفلتر
        filter_btn_layout = QVBoxLayout()
        filter_btn_layout.addStretch()

        self.apply_filter_btn = PrimaryButton(tr.get_text("apply_filter", "تطبيق الفلتر"))
        self.apply_filter_btn.clicked.connect(self.load_data)
        filter_btn_layout.addWidget(self.apply_filter_btn)

        filter_layout.addLayout(filter_btn_layout)

        layout.addWidget(filter_group)

        # أزرار التصدير
        export_layout = QHBoxLayout()

        self.export_excel_btn = StyledButton(tr.get_text("export_excel", "تصدير إلى Excel"))
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        export_layout.addWidget(self.export_excel_btn)

        self.export_pdf_btn = StyledButton(tr.get_text("export_pdf", "تصدير إلى PDF"))
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)
        export_layout.addWidget(self.export_pdf_btn)

        self.print_btn = StyledButton(tr.get_text("print", "طباعة"))
        self.print_btn.clicked.connect(self.print_report)
        export_layout.addWidget(self.print_btn)

        export_layout.addStretch()

        layout.addLayout(export_layout)

        # جدول التقرير
        self.table = StyledTable()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("invoice_number", "رقم الفاتورة"),
            tr.get_text("date", "التاريخ"),
            tr.get_text("customer", "العميل"),
            tr.get_text("total", "الإجمالي"),
            tr.get_text("paid", "المدفوع"),
            tr.get_text("remaining", "المتبقي"),
            tr.get_text("status", "الحالة")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        layout.addWidget(self.table)

        # ملخص التقرير
        summary_layout = QHBoxLayout()

        self.total_invoices_label = StyledLabel(tr.get_text("total_invoices", "إجمالي الفواتير: 0"))
        summary_layout.addWidget(self.total_invoices_label)

        self.total_amount_label = StyledLabel(tr.get_text("total_amount", "إجمالي المبلغ: 0"))
        summary_layout.addWidget(self.total_amount_label)

        self.total_paid_label = StyledLabel(tr.get_text("total_paid", "إجمالي المدفوع: 0"))
        summary_layout.addWidget(self.total_paid_label)

        self.total_remaining_label = StyledLabel(tr.get_text("total_remaining", "إجمالي المتبقي: 0"))
        summary_layout.addWidget(self.total_remaining_label)

        layout.addLayout(summary_layout)

    def load_data(self):
        """تحميل بيانات التقرير"""
        try:
            # الحصول على جلسة قاعدة البيانات
            db = next(get_db())

            # الحصول على نطاق التاريخ
            start_date = self.start_date.date().toPyDate()
            end_date = self.end_date.date().toPyDate()

            # الحصول على العميل المحدد
            selected_customer_id = self.customer_combo.currentData()

            # بناء الاستعلام
            query = db.query(Invoice).filter(
                Invoice.invoice_type == 'sales',
                Invoice.invoice_date >= start_date,
                Invoice.invoice_date <= end_date
            )

            # تطبيق فلتر العميل إذا تم تحديده
            if selected_customer_id:
                query = query.filter(Invoice.customer_id == selected_customer_id)

            # الحصول على الفواتير
            invoices = query.order_by(Invoice.invoice_date.desc()).all()

            # تحديث الجدول
            self.update_sales_table(invoices)

            # تحديث الملخص
            self.update_summary(invoices)

            log_info(f"تم تحميل {len(invoices)} فاتورة مبيعات")

        except Exception as e:
            log_error(f"خطأ في تحميل تقرير المبيعات: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_report", "حدث خطأ أثناء تحميل التقرير")
            )

    def update_sales_table(self, invoices):
        """تحديث جدول المبيعات"""
        # تحديث عدد الصفوف
        self.table.setRowCount(len(invoices))

        # ملء البيانات
        for row, invoice in enumerate(invoices):
            # رقم الفاتورة
            invoice_number = QTableWidgetItem(invoice.invoice_number or "")
            invoice_number.setFlags(invoice_number.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 0, invoice_number)

            # التاريخ
            date_str = invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else ""
            date_item = QTableWidgetItem(date_str)
            date_item.setFlags(date_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 1, date_item)

            # العميل
            customer_name = invoice.customer.name if invoice.customer else tr.get_text("unknown", "غير محدد")
            customer_item = QTableWidgetItem(customer_name)
            customer_item.setFlags(customer_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 2, customer_item)

            # الإجمالي
            total_amount = invoice.total_amount or 0
            total_item = QTableWidgetItem(f"{total_amount:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row, 3, total_item)

            # المدفوع
            paid_amount = invoice.paid_amount or 0
            paid_item = QTableWidgetItem(f"{paid_amount:.2f}")
            paid_item.setFlags(paid_item.flags() & ~Qt.ItemIsEditable)
            paid_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.table.setItem(row, 4, paid_item)

            # المتبقي
            remaining_amount = total_amount - paid_amount
            remaining_item = QTableWidgetItem(f"{remaining_amount:.2f}")
            remaining_item.setFlags(remaining_item.flags() & ~Qt.ItemIsEditable)
            remaining_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # تلوين المبلغ المتبقي باستخدام QBrush
            from PyQt5.QtGui import QColor, QBrush
            if remaining_amount > 0:
                remaining_item.setForeground(QBrush(QColor("red")))
            else:
                remaining_item.setForeground(QBrush(QColor("green")))

            self.table.setItem(row, 5, remaining_item)

            # الحالة
            status_text = tr.get_text("paid", "مدفوع") if remaining_amount == 0 else tr.get_text("pending", "معلق")
            status_item = QTableWidgetItem(status_text)
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            status_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 6, status_item)

    def update_summary(self, invoices):
        """تحديث ملخص التقرير"""
        total_invoices = len(invoices)
        total_amount = sum([invoice.total_amount or 0 for invoice in invoices])
        total_paid = sum([invoice.paid_amount or 0 for invoice in invoices])
        total_remaining = total_amount - total_paid

        currency = "ج.م"  # يمكن الحصول عليها من الإعدادات

        self.total_invoices_label.setText(f"{tr.get_text('total_invoices', 'إجمالي الفواتير')}: {total_invoices}")
        self.total_amount_label.setText(f"{tr.get_text('total_amount', 'إجمالي المبلغ')}: {total_amount:.2f} {currency}")
        self.total_paid_label.setText(f"{tr.get_text('total_paid', 'إجمالي المدفوع')}: {total_paid:.2f} {currency}")
        self.total_remaining_label.setText(f"{tr.get_text('total_remaining', 'إجمالي المتبقي')}: {total_remaining:.2f} {currency}")

    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        try:
            # الحصول على مسار الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                tr.get_text("save_excel_file", "حفظ ملف Excel"),
                "",
                "Excel Files (*.xlsx)"
            )

            if not file_path:
                return

            # إنشاء بيانات التقرير
            data = []
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # إنشاء DataFrame
            import pandas as pd
            headers = [self.table.horizontalHeaderItem(col).text() for col in range(self.table.columnCount())]
            df = pd.DataFrame(data, columns=headers)

            # حفظ الملف
            df.to_excel(file_path, index=False)

            QMessageBox.information(
                self,
                tr.get_text("success_title", "نجاح"),
                tr.get_text("excel_export_success", "تم تصدير التقرير إلى Excel بنجاح")
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى Excel: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("excel_export_error", "حدث خطأ أثناء تصدير التقرير إلى Excel")
            )

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        try:
            # إنشاء بيانات التقرير
            report_data = self.get_report_data()

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # تصدير التقرير إلى PDF
            print_manager.export_to_pdf(
                html_content=self.get_report_html(),
                title=tr.get_text("sales_report", "تقرير المبيعات"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى PDF: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("pdf_export_error", "حدث خطأ أثناء تصدير التقرير إلى PDF")
            )

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء بيانات التقرير
            report_data = self.get_report_data()

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # عرض معاينة الطباعة
            print_manager.print_preview(
                html_content=self.get_report_html(),
                title=tr.get_text("sales_report", "تقرير المبيعات"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("print_error", "حدث خطأ أثناء طباعة التقرير")
            )

    def get_report_data(self):
        """الحصول على بيانات التقرير"""
        # جمع بيانات التقرير
        data = {
            'report_title': tr.get_text("sales_report", "تقرير المبيعات"),
            'date_range': f"{self.start_date.date().toString('yyyy-MM-dd')} - {self.end_date.date().toString('yyyy-MM-dd')}",
            'generated_by': tr.get_text("system", "النظام"),
            'report_id': f"SR-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'table_headers': self.get_table_headers_html(),
            'table_rows': self.get_table_rows_html(),
            'summary_title': tr.get_text("summary", "الملخص"),
            'summary_rows': self.get_summary_rows_html(),
            'chart_title': tr.get_text("sales_chart", "رسم بياني للمبيعات"),
            'chart_placeholder_text': tr.get_text("chart_not_available", "الرسم البياني غير متاح حالياً"),
            'footer_text': tr.get_text("report_footer", "تم إنشاء هذا التقرير بواسطة برنامج أمين الحسابات"),
            'printed_date_label': tr.get_text("printed_date", "تاريخ الطباعة")
        }

        return data

    def get_report_html(self):
        """الحصول على HTML للتقرير"""
        # الحصول على مدير الطباعة
        print_manager = PrintManager.get_instance()

        # تحويل قالب التقرير
        return print_manager.render_template('report_general', self.get_report_data())

    def get_table_headers_html(self):
        """الحصول على HTML لرؤوس الجدول"""
        html = ""
        for col in range(self.table.columnCount()):
            header = self.table.horizontalHeaderItem(col).text()
            html += f"<th>{header}</th>"
        return html

    def get_table_rows_html(self):
        """الحصول على HTML لصفوف الجدول"""
        html = ""
        for row in range(self.table.rowCount()):
            html += "<tr>"
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                text = item.text() if item else ""

                # تطبيق تنسيق خاص للأعمدة المالية
                if col in [3, 4, 5]:  # الإجمالي، المدفوع، المتبقي
                    html += f'<td class="amount">{text}</td>'
                else:
                    html += f"<td>{text}</td>"

            html += "</tr>"
        return html

    def get_summary_rows_html(self):
        """الحصول على HTML لصفوف الملخص"""
        html = ""

        # إجمالي الفواتير
        total_invoices = self.table.rowCount()
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_invoices", "إجمالي الفواتير")}:</span> <span>{total_invoices}</span></div>'

        # إجمالي المبلغ
        total_amount_text = self.total_amount_label.text().split(": ")[1]
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_amount", "إجمالي المبلغ")}:</span> <span>{total_amount_text}</span></div>'

        # إجمالي المدفوع
        total_paid_text = self.total_paid_label.text().split(": ")[1]
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_paid", "إجمالي المدفوع")}:</span> <span>{total_paid_text}</span></div>'

        # إجمالي المتبقي
        total_remaining_text = self.total_remaining_label.text().split(": ")[1]
        html += f'<div class="summary-row"><span class="summary-label">{tr.get_text("total_remaining", "إجمالي المتبقي")}:</span> <span>{total_remaining_text}</span></div>'

        return html

class PurchasesReportView(QWidget):
    """
    واجهة تقارير المشتريات
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

class InventoryReportView(QWidget):
    """
    واجهة تقارير المخزون
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

class ProfitLossReportView(QWidget):
    """
    واجهة تقارير الأرباح والخسائر
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db = next(get_db())
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # العنوان
        header = HeaderLabel(tr.get_text("profit_loss_report", "تقرير الأرباح والخسائر"))
        layout.addWidget(header)

        # مجموعة الفلاتر
        filter_group = QGroupBox(tr.get_text("filters", "الفلاتر"))
        filter_layout = QHBoxLayout(filter_group)

        # فلتر التاريخ
        date_layout = QVBoxLayout()
        date_layout.addWidget(StyledLabel(tr.get_text("date_range", "نطاق التاريخ")))

        date_range_layout = QHBoxLayout()

        self.start_date = StyledDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        date_range_layout.addWidget(self.start_date)

        date_range_layout.addWidget(StyledLabel(" - "))

        self.end_date = StyledDateEdit()
        self.end_date.setDate(QDate.currentDate())
        date_range_layout.addWidget(self.end_date)

        date_layout.addLayout(date_range_layout)
        filter_layout.addLayout(date_layout)

        # فلتر نوع التقرير
        type_layout = QVBoxLayout()
        type_layout.addWidget(StyledLabel(tr.get_text("report_type", "نوع التقرير")))

        self.report_type_combo = StyledComboBox()
        self.report_type_combo.addItem(tr.get_text("detailed", "تفصيلي"), "detailed")
        self.report_type_combo.addItem(tr.get_text("summary", "ملخص"), "summary")
        type_layout.addWidget(self.report_type_combo)
        filter_layout.addLayout(type_layout)

        # زر تطبيق الفلتر
        filter_btn_layout = QVBoxLayout()
        filter_btn_layout.addStretch()

        self.apply_filter_btn = PrimaryButton(tr.get_text("apply_filter", "تطبيق الفلتر"))
        self.apply_filter_btn.clicked.connect(self.load_data)
        filter_btn_layout.addWidget(self.apply_filter_btn)

        filter_layout.addLayout(filter_btn_layout)
        layout.addWidget(filter_group)

        # أزرار التصدير
        export_layout = QHBoxLayout()

        self.export_excel_btn = StyledButton(tr.get_text("export_excel", "تصدير إلى Excel"))
        self.export_excel_btn.clicked.connect(self.export_to_excel)
        export_layout.addWidget(self.export_excel_btn)

        self.export_pdf_btn = StyledButton(tr.get_text("export_pdf", "تصدير إلى PDF"))
        self.export_pdf_btn.clicked.connect(self.export_to_pdf)
        export_layout.addWidget(self.export_pdf_btn)

        self.print_btn = StyledButton(tr.get_text("print", "طباعة"))
        self.print_btn.clicked.connect(self.print_report)
        export_layout.addWidget(self.print_btn)

        export_layout.addStretch()
        layout.addLayout(export_layout)

        # جدول التقرير
        self.table = StyledTable()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("item", "البند"),
            tr.get_text("amount", "المبلغ"),
            tr.get_text("percentage", "النسبة %")
        ])

        # تعيين خصائص الجدول
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        layout.addWidget(self.table)

        # ملخص التقرير
        summary_layout = QHBoxLayout()

        self.net_profit_label = StyledLabel(tr.get_text("net_profit", "صافي الربح: 0"))
        self.net_profit_label.setStyleSheet(f"font-weight: bold; color: {get_module_color('sales_report')};")
        summary_layout.addWidget(self.net_profit_label)

        self.profit_margin_label = StyledLabel(tr.get_text("profit_margin", "هامش الربح: 0%"))
        self.profit_margin_label.setStyleSheet(f"font-weight: bold; color: {get_module_color('treasury')};")
        summary_layout.addWidget(self.profit_margin_label)

        layout.addLayout(summary_layout)

    def load_data(self):
        """تحميل بيانات تقرير الأرباح والخسائر"""
        try:
            # الحصول على نطاق التاريخ
            start_date = self.start_date.date().toPyDate()
            end_date = self.end_date.date().toPyDate()

            # حساب الإيرادات (المبيعات)
            sales_invoices = self.db.query(Invoice).filter(
                Invoice.invoice_type == 'sales',
                Invoice.invoice_date >= start_date,
                Invoice.invoice_date <= end_date
            ).all()

            total_sales = sum([invoice.total_amount or 0 for invoice in sales_invoices])

            # حساب تكلفة البضاعة المباعة
            cost_of_goods = 0
            for invoice in sales_invoices:
                for item in invoice.items:
                    if item.product:
                        cost_of_goods += (item.product.purchase_price or 0) * (item.quantity or 0)

            # حساب إجمالي الربح
            gross_profit = total_sales - cost_of_goods

            # حساب المصروفات
            expenses = self.db.query(Expense).filter(
                Expense.expense_date >= start_date,
                Expense.expense_date <= end_date
            ).all()

            total_expenses = sum([expense.amount or 0 for expense in expenses])

            # حساب صافي الربح
            net_profit = gross_profit - total_expenses

            # حساب النسب المئوية
            sales_percentage = 100.0
            cogs_percentage = (cost_of_goods / total_sales * 100) if total_sales > 0 else 0
            gross_profit_percentage = (gross_profit / total_sales * 100) if total_sales > 0 else 0
            expenses_percentage = (total_expenses / total_sales * 100) if total_sales > 0 else 0
            net_profit_percentage = (net_profit / total_sales * 100) if total_sales > 0 else 0

            # تحديث الجدول
            self.update_table(total_sales, cost_of_goods, gross_profit, total_expenses, net_profit,
                            sales_percentage, cogs_percentage, gross_profit_percentage,
                            expenses_percentage, net_profit_percentage)

            # تحديث الملخص
            currency = "ج.م"  # يمكن الحصول عليها من الإعدادات
            self.net_profit_label.setText(f"{tr.get_text('net_profit', 'صافي الربح')}: {net_profit:.2f} {currency}")
            self.profit_margin_label.setText(f"{tr.get_text('profit_margin', 'هامش الربح')}: {net_profit_percentage:.1f}%")

            log_info("تم تحميل تقرير الأرباح والخسائر")

        except Exception as e:
            log_error(f"خطأ في تحميل تقرير الأرباح والخسائر: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("error_loading_report", "حدث خطأ أثناء تحميل التقرير")
            )

    def update_table(self, total_sales, cost_of_goods, gross_profit, total_expenses, net_profit,
                    sales_percentage, cogs_percentage, gross_profit_percentage,
                    expenses_percentage, net_profit_percentage):
        """تحديث جدول التقرير"""
        currency = "ج.م"

        # بيانات التقرير
        data = [
            (tr.get_text("total_sales", "إجمالي المبيعات"), total_sales, sales_percentage, get_module_color('sales_report')),
            (tr.get_text("cost_of_goods_sold", "تكلفة البضاعة المباعة"), cost_of_goods, cogs_percentage, get_module_color('expenses_report')),
            (tr.get_text("gross_profit", "إجمالي الربح"), gross_profit, gross_profit_percentage, get_module_color('treasury')),
            (tr.get_text("total_expenses", "إجمالي المصروفات"), total_expenses, expenses_percentage, get_module_color('expenses_report')),
            (tr.get_text("net_profit", "صافي الربح"), net_profit, net_profit_percentage, get_module_color('sales_report'))
        ]

        # تحديث عدد الصفوف
        self.table.setRowCount(len(data))

        # ملء البيانات
        for row, (item, amount, percentage, color) in enumerate(data):
            # البند
            item_widget = QTableWidgetItem(item)
            item_widget.setFlags(item_widget.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 0, item_widget)

            # المبلغ
            amount_widget = QTableWidgetItem(f"{amount:.2f} {currency}")
            amount_widget.setFlags(amount_widget.flags() & ~Qt.ItemIsEditable)
            amount_widget.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

            # تلوين الأرقام السالبة باللون الأحمر باستخدام QBrush
            from PyQt5.QtGui import QColor, QBrush, QFont
            if amount < 0:
                amount_widget.setForeground(QBrush(QColor("red")))
            elif row in [2, 4]:  # إجمالي الربح وصافي الربح
                amount_widget.setForeground(QBrush(QColor(color)))
                # تطبيق الخط العريض
                font = QFont()
                font.setBold(True)
                amount_widget.setFont(font)

            self.table.setItem(row, 1, amount_widget)

            # النسبة المئوية
            percentage_widget = QTableWidgetItem(f"{percentage:.1f}%")
            percentage_widget.setFlags(percentage_widget.flags() & ~Qt.ItemIsEditable)
            percentage_widget.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, percentage_widget)

    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                tr.get_text("save_excel_file", "حفظ ملف Excel"),
                f"profit_loss_report_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if not file_path:
                return

            # إنشاء بيانات التقرير
            data = []
            headers = [self.table.horizontalHeaderItem(col).text() for col in range(self.table.columnCount())]

            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=headers)

            # حفظ الملف
            df.to_excel(file_path, index=False)

            QMessageBox.information(
                self,
                tr.get_text("success_title", "نجاح"),
                tr.get_text("excel_export_success", "تم تصدير التقرير إلى Excel بنجاح")
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى Excel: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("excel_export_error", "حدث خطأ أثناء تصدير التقرير إلى Excel")
            )

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        try:
            # إنشاء HTML للتقرير
            html_content = self.generate_report_html()

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # تصدير التقرير إلى PDF
            print_manager.export_to_pdf(
                html_content=html_content,
                title=tr.get_text("profit_loss_report", "تقرير الأرباح والخسائر"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في تصدير التقرير إلى PDF: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("pdf_export_error", "حدث خطأ أثناء تصدير التقرير إلى PDF")
            )

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء HTML للتقرير
            html_content = self.generate_report_html()

            # الحصول على مدير الطباعة
            print_manager = PrintManager.get_instance()

            # عرض معاينة الطباعة
            print_manager.print_preview(
                html_content=html_content,
                title=tr.get_text("profit_loss_report", "تقرير الأرباح والخسائر"),
                parent=self
            )

        except Exception as e:
            log_error(f"خطأ في طباعة التقرير: {str(e)}")
            QMessageBox.critical(
                self,
                tr.get_text("error_title", "خطأ"),
                tr.get_text("print_error", "حدث خطأ أثناء طباعة التقرير")
            )

    def generate_report_html(self):
        """إنشاء HTML للتقرير"""
        # إنشاء HTML بسيط للتقرير
        html = f"""
        <html dir="rtl">
        <head>
            <meta charset="utf-8">
            <title>{tr.get_text("profit_loss_report", "تقرير الأرباح والخسائر")}</title>
            <style>
                body {{ font-family: Arial, sans-serif; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 20px; }}
                .date-range {{ text-align: center; margin-bottom: 20px; color: #666; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; font-weight: bold; }}
                .amount {{ text-align: left; }}
                .summary {{ margin-top: 20px; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{tr.get_text("profit_loss_report", "تقرير الأرباح والخسائر")}</h1>
            </div>
            <div class="date-range">
                {tr.get_text("date_range", "نطاق التاريخ")}: {self.start_date.date().toString('yyyy-MM-dd')} - {self.end_date.date().toString('yyyy-MM-dd')}
            </div>
            <table>
                <thead>
                    <tr>
                        <th>{tr.get_text("item", "البند")}</th>
                        <th>{tr.get_text("amount", "المبلغ")}</th>
                        <th>{tr.get_text("percentage", "النسبة %")}</th>
                    </tr>
                </thead>
                <tbody>
        """

        # إضافة صفوف البيانات
        for row in range(self.table.rowCount()):
            html += "<tr>"
            for col in range(self.table.columnCount()):
                item = self.table.item(row, col)
                text = item.text() if item else ""
                css_class = "amount" if col == 1 else ""
                html += f'<td class="{css_class}">{text}</td>'
            html += "</tr>"

        html += """
                </tbody>
            </table>
            <div class="summary">
        """

        # إضافة الملخص
        html += f"<p>{self.net_profit_label.text()}</p>"
        html += f"<p>{self.profit_margin_label.text()}</p>"

        html += f"""
            </div>
            <div style="margin-top: 30px; text-align: center; color: #666; font-size: 12px;">
                {tr.get_text("printed_date", "تاريخ الطباعة")}: {datetime.now().strftime('%Y-%m-%d %H:%M')}
            </div>
        </body>
        </html>
        """

        return html

class TreasuryReportView(QWidget):
    """
    واجهة تقارير الخزينة
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # رسالة عدم التنفيذ
        label = StyledLabel(tr.get_text("feature_not_implemented", "هذه الميزة غير متاحة حالياً"))
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
