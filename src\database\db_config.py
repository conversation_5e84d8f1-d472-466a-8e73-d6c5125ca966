#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

class DatabaseConfig:
    """
    فئة لإدارة إعداد وتهيئة قاعدة البيانات
    """

    def __init__(self):
        """
        تهيئة اتصال قاعدة البيانات
        """
        # تحديد مسار قاعدة البيانات في مجلد البيانات المحلي للمستخدم
        app_data_path = os.path.join(os.getenv('LOCALAPPDATA'), 'Am<PERSON>')
        os.makedirs(app_data_path, exist_ok=True)

        db_path = os.path.join(app_data_path, 'amin_al_hisabat.db')
        self.DATABASE_URL = f"sqlite:///{db_path}"

        # إنشاء محرك قاعدة البيانات
        self.engine = create_engine(
            self.DATABASE_URL,
            echo=False,  # تعطيل طباعة استعلامات SQL للإنتاج
            connect_args={"check_same_thread": False}  # للسماح باستخدام SQLite في threads متعددة
        )

        # إنشاء session factory
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )

        # قاعدة النموذج التي سترث منها جميع نماذج قاعدة البيانات
        self.Base = declarative_base()

    def create_database(self):
        """
        إنشاء جميع الجداول في قاعدة البيانات
        """
        # استيراد جميع النماذج لضمان تسجيلها مع Base
        from src.models import (
            User, Product, Supplier, Customer,
            Invoice, InvoiceItem, Expense, Income, Payment
        )

        # إنشاء جميع الجداول
        self.Base.metadata.create_all(bind=self.engine)

    def get_db(self):
        """
        إنشاء جلسة قاعدة بيانات جديدة
        """
        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

    def init_db(self):
        """
        تهيئة قاعدة البيانات وإنشاء البيانات الأولية إذا لزم الأمر
        """
        try:
            self.create_database()
            # هنا يمكن إضافة أي بيانات أولية مطلوبة
            # مثل حساب المسؤول الافتراضي أو البيانات الأساسية
        except Exception as e:
            from src.utils.logger import log_error
            log_error(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
            raise

# إنشاء نسخة واحدة من إعدادات قاعدة البيانات
db_config = DatabaseConfig()