"""
واجهات إدارة الموظفين
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QDialog, QFormLayout, QLineEdit, QDateEdit, QComboBox,
    QMessageBox, QLabel, QSpinBox, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from datetime import datetime

from src.database import get_db
from src.ui.widgets.base_widgets import (
    PrimaryButton, DangerButton, SecondaryButton,
    StyledLineEdit, StyledComboBox, StyledDateEdit
)
from src.utils import translation_manager as tr
from .models import Employee, Attendance, Leave, Salary

class EmployeeManagementView(QWidget):
    """نافذة إدارة الموظفين"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_employees()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.add_btn = PrimaryButton(tr.get_text("btn_add_employee"))
        self.add_btn.clicked.connect(self.show_add_dialog)
        actions_layout.addWidget(self.add_btn)
        
        self.edit_btn = SecondaryButton(tr.get_text("btn_edit_employee"))
        self.edit_btn.clicked.connect(self.show_edit_dialog)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)
        
        self.delete_btn = DangerButton(tr.get_text("btn_delete_employee"))
        self.delete_btn.clicked.connect(self.delete_employee)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)
        
        actions_layout.addStretch()
        layout.addLayout(actions_layout)
        
        # جدول الموظفين
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels([
            tr.get_text("employee_id"),
            tr.get_text("name"),
            tr.get_text("position"),
            tr.get_text("department"),
            tr.get_text("join_date")
        ])
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        layout.addWidget(self.table)
        
    def load_employees(self):
        """تحميل بيانات الموظفين"""
        try:
            db = next(get_db())
            employees = db.query(Employee).filter_by(is_deleted=False).all()
            
            self.table.setRowCount(len(employees))
            for i, emp in enumerate(employees):
                self.table.setItem(i, 0, QTableWidgetItem(emp.employee_id))
                self.table.setItem(i, 1, QTableWidgetItem(emp.name))
                self.table.setItem(i, 2, QTableWidgetItem(emp.position))
                self.table.setItem(i, 3, QTableWidgetItem(emp.department))
                self.table.setItem(i, 4, QTableWidgetItem(emp.join_date.strftime("%Y-%m-%d")))
                
        except Exception as e:
            QMessageBox.critical(
                self,
                tr.get_text("error_title"),
                tr.get_text("error_loading_employees")
            )
    
    def on_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير التحديد"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        
    def show_add_dialog(self):
        """عرض نافذة إضافة موظف جديد"""
        dialog = EmployeeDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_employees()
            
    def show_edit_dialog(self):
        """عرض نافذة تعديل بيانات الموظف"""
        if not self.table.selectedItems():
            return
            
        row = self.table.currentRow()
        employee_id = self.table.item(row, 0).text()
        
        try:
            db = next(get_db())
            employee = db.query(Employee).filter_by(employee_id=employee_id).first()
            if employee:
                dialog = EmployeeDialog(self, employee)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_employees()
                    
        except Exception as e:
            QMessageBox.critical(
                self,
                tr.get_text("error_title"),
                tr.get_text("error_loading_employee")
            )
            
    def delete_employee(self):
        """حذف الموظف المحدد"""
        if not self.table.selectedItems():
            return
            
        row = self.table.currentRow()
        employee_id = self.table.item(row, 0).text()
        
        confirm = QMessageBox.question(
            self,
            tr.get_text("confirm_title"),
            tr.get_text("confirm_delete_employee"),
            QMessageBox.Yes | QMessageBox.No
        )
        
        if confirm == QMessageBox.Yes:
            try:
                db = next(get_db())
                employee = db.query(Employee).filter_by(employee_id=employee_id).first()
                if employee:
                    employee.is_deleted = True
                    db.commit()
                    self.load_employees()
                    
            except Exception as e:
                QMessageBox.critical(
                    self,
                    tr.get_text("error_title"),
                    tr.get_text("error_deleting_employee")
                )

class EmployeeDialog(QDialog):
    """نافذة إضافة/تعديل بيانات الموظف"""
    
    def __init__(self, parent=None, employee=None):
        super().__init__(parent)
        self.employee = employee
        self.setup_ui()
        if employee:
            self.load_employee_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(
            tr.get_text("edit_employee") if self.employee 
            else tr.get_text("add_employee")
        )
        self.setModal(True)
        
        layout = QFormLayout(self)
        
        # البيانات الأساسية
        self.emp_id_input = StyledLineEdit()
        layout.addRow(tr.get_text("employee_id"), self.emp_id_input)
        
        self.name_input = StyledLineEdit()
        layout.addRow(tr.get_text("name"), self.name_input)
        
        self.position_input = StyledLineEdit()
        layout.addRow(tr.get_text("position"), self.position_input)
        
        self.department_input = StyledComboBox()
        self.department_input.addItems([
            "الإدارة",
            "المبيعات",
            "المشتريات",
            "المحاسبة",
            "الموارد البشرية",
            "تقنية المعلومات"
        ])
        layout.addRow(tr.get_text("department"), self.department_input)
        
        self.join_date_input = StyledDateEdit()
        self.join_date_input.setCalendarPopup(True)
        layout.addRow(tr.get_text("join_date"), self.join_date_input)
        
        self.salary_input = QDoubleSpinBox()
        self.salary_input.setRange(0, 1000000)
        self.salary_input.setDecimals(2)
        layout.addRow(tr.get_text("base_salary"), self.salary_input)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_btn = PrimaryButton(tr.get_text("btn_save"))
        self.save_btn.clicked.connect(self.save_employee)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = SecondaryButton(tr.get_text("btn_cancel"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addRow("", buttons_layout)
        
    def load_employee_data(self):
        """تحميل بيانات الموظف في النموذج"""
        self.emp_id_input.setText(self.employee.employee_id)
        self.name_input.setText(self.employee.name)
        self.position_input.setText(self.employee.position)
        self.department_input.setCurrentText(self.employee.department)
        self.join_date_input.setDate(self.employee.join_date)
        self.salary_input.setValue(self.employee.base_salary)
        
    def save_employee(self):
        """حفظ بيانات الموظف"""
        try:
            db = next(get_db())
            
            if self.employee:  # تعديل
                self.employee.employee_id = self.emp_id_input.text()
                self.employee.name = self.name_input.text()
                self.employee.position = self.position_input.text()
                self.employee.department = self.department_input.currentText()
                self.employee.join_date = self.join_date_input.date().toPyDate()
                self.employee.base_salary = self.salary_input.value()
                
            else:  # إضافة
                employee = Employee(
                    employee_id=self.emp_id_input.text(),
                    name=self.name_input.text(),
                    position=self.position_input.text(),
                    department=self.department_input.currentText(),
                    join_date=self.join_date_input.date().toPyDate(),
                    base_salary=self.salary_input.value(),
                    contract_type="دائم"  # قيمة افتراضية
                )
                db.add(employee)
                
            db.commit()
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                tr.get_text("error_title"),
                tr.get_text("error_saving_employee")
            )